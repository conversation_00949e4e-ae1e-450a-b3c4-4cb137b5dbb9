﻿using System;
using System.Collections.Generic;

namespace HSSE_Domain.Models;

public partial class MstRoleMenuPermission
{
    public int RoleMenuPermissionsId { get; set; }

    public int? RoleId { get; set; }

    public int PermissionId { get; set; }

    public bool? CanView { get; set; }

    public bool? CanCreate { get; set; }

    public bool? CanEdit { get; set; }

    public bool? CanDelete { get; set; }

    public virtual MstPermission Permission { get; set; } = null!;

    public virtual MstUsersRole? Role { get; set; }
}
