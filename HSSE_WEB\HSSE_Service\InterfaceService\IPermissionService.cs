using HSSE_Models_Dto.ModelsDto;
using HSSE_Service.ServiceResponce;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace HSSE_Service.InterfaceService
{
    public interface IPermissionService
    {
        Task<ApiResponseDto<List<PermissionDto>>> GetPermissionsByUserIdAsync(int roleId);
        Task<ApiResponseDto<bool>> UpdatePermissionOrderAsync(List<PermissionOrderUpdateDto> orderUpdates);
        Task<ApiResponseDto<bool>> UpdatePermissionForRoleAsync(UpdatePermissionRequest request);
        Task<List<PermissionDto>> GetParentMenusAsync();
        Task<List<PermissionDto>> GetChildMenusByParentAsync(int parentId);
        Task<ApiResponseDto<bool>> MapPermissionToRoleAsync(UpdatePermissionRequest request);
        Task<ApiResponseDto<bool>> CreatePermissionAsync(PermissionDto permission);
        Task<ApiResponseDto<PermissionDto>> GetPermissionByIdAsync(int permissionId);
        Task<ApiResponseDto<bool>> UpdatePermissionDetailsAsync(PermissionDto permission);
        Task<ApiResponseDto<bool>> TogglePermissionActiveStatusAsync(int permissionId, bool isActive);
        Task<ApiResponseDto<List<PermissionDto>>> GetAllPermissionsAsync(string filterType = "all");
        Task<ApiResponseDto<bool>> DeletePermissionMappingAsync(int permissionId, int roleId);
    }
} 