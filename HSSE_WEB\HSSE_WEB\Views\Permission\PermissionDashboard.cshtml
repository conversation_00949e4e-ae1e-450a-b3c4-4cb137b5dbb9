﻿@{
    ViewData["Title"] = "Permission Dashboard";
}
<style>
    .tree-line {
        border-left: 2px dashed #ccc;
    }

    .tree-item {
        position: relative;
    }

        .tree-item::before {
            content: '';
            position: absolute;
            left: -18px;
            top: 18px;
            width: 16px;
            height: 2px;
            background-color: #ccc;
        }

    #permissionAccordion .card-header,
    #permissionAccordion .card-body {
        width: 100%;
    }
</style>

<div class="form-group">
    <label for="rolePermissionDropdown"><strong>Select Role</strong></label>
    <select id="rolePermissionDropdown" class="form-control w-25">
    </select>
    <button class="btn btn-primary map-parent-permission" id="openMapPermissionModal">Map Permissions</button>

</div>

<div id="permissionAccordion" class="accordion accordion-solid-header col-md-6"></div>

<!-- Modal or section to add child menu dynamically -->
<div id="addChildMenuModal" class="modal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add Child Menu</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <input type="text" id="newMenuName" class="form-control mb-2" placeholder="Menu Name" />
                <button class="btn btn-primary" id="saveChildMenu">Save</button>
            </div>
        </div>
    </div>
</div>
<div class="modal fade" id="editChildMenuModal" tabindex="-1" role="dialog" aria-labelledby="editChildMenuModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Child Menu</h5>
                <button type="button" class="close" data-dismiss="modal"><span>&times;</span></button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="editPermissionId">
                <div class="form-group">
                    <label for="editMenuName">Menu Name</label>
                    <input type="text" class="form-control" id="editMenuName">
                </div>
            <div class="row ml-1">
                    <div class="form-check mr-2">
                    <label class="form-check-label"><input type="checkbox" id="editIsCreate"> Create</label>            
                </div>
                <div class="form-check mr-2">
                <label class="form-check-label"><input type="checkbox" id="editIsView"> View</label>
                </div>
                    <div class="form-check mr-2">
                <label class="form-check-label"><input type="checkbox" id="editIsEdit"> Edit</label>
                </div>
                    <div class="form-check mr-2">
                <label class="form-check-label"><input type="checkbox" id="editIsDelete"> Delete</label>
                </div>
            </div>
            </div>
            <div class="modal-footer">
                <button type="button" id="saveEditChildMenu" class="btn btn-primary">Save Changes</button>
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
            </div>
        </div>
    </div>
</div>
<div class="modal fade" id="mapPermissionModal" tabindex="-1" role="dialog" aria-labelledby="mapPermissionModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-md" role="document">
        <div class="modal-content">

            <div class="modal-header">
                <h5 class="modal-title" id="mapPermissionModalLabel">Map Permissions</h5>
                <button type="button" class="close" data-dismiss="modal">&times;</button>
            </div>

            <div class="modal-body">
                <form id="mapPermissionForm">
                    <!-- Hidden field to store selected parent permission ID -->
                    <input type="hidden" id="selectedPermissionId" name="selectedPermissionId" />

                    <!-- Removed parent permission dropdown -->

                    <div class="form-group">
                        <label for="childPermissionDropdown">Select Child Menu (Optional)</label>
                        <select id="childPermissionDropdown" class="form-control" disabled>
                            <option value="">-- Select Child Menu --</option>
                        </select>
                    </div>

                    <!-- Optional area to display info about parent permission or selected child -->
                    <div id="permissionsDisplayArea" class="mb-3"></div>

                    <div class="form-group">
                        <div class="row ml-1">
                            <div class="form-check mr-2">
                                <label class="form-check-label"><input type="checkbox" id="permCreate"> Create</label>
                            </div>
                            <div class="form-check mr-2">
                                <label class="form-check-label"><input type="checkbox" id="permView"> View</label>
                            </div>
                            <div class="form-check mr-2">
                                <label class="form-check-label"><input type="checkbox" id="permEdit"> Edit</label>
                            </div>
                            <div class="form-check mr-2">
                                <label class="form-check-label"><input type="checkbox" id="permDelete"> Delete</label>
                            </div>
                        </div>
                    </div>
                </form>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn btn-success savePermissionMapping">Save Mapping</button>
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>

        </div>
    </div>
</div>

<div class="modal fade" id="mapParentPermissionModal" tabindex="-1" role="dialog" aria-labelledby="mapParentPermissionModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-md" role="document">
        <div class="modal-content">

            <div class="modal-header">
                <h5 class="modal-title" id="mapParentPermissionModalLabel">Map Permissions</h5>
                <button type="button" class="close" data-dismiss="modal">&times;</button>
            </div>

            <div class="modal-body">
                <form id="mapPermissionForm">
                    <!-- Removed parent permission dropdown -->

                    <div class="form-group">
                        <label for="parentPermissionDropdown">Select Parent Menu </label>
                        <select id="parentPermissionDropdown" class="form-control">
                            <option value="">-- Select Parent Menu --</option>
                        </select>
                    </div>

                    <!-- Optional area to display info about parent permission or selected child -->
                    <div id="permissionsDisplayArea" class="mb-3"></div>

                    <div class="form-group">
                        <div class="row ml-1">
                            <div class="form-check mr-2">
                                <label class="form-check-label"><input type="checkbox" id="permCreate"> Create</label>
                            </div>
                            <div class="form-check mr-2">
                                <label class="form-check-label"><input type="checkbox" id="permView"> View</label>
                            </div>
                            <div class="form-check mr-2">
                                <label class="form-check-label"><input type="checkbox" id="permEdit"> Edit</label>
                            </div>
                            <div class="form-check mr-2">
                                <label class="form-check-label"><input type="checkbox" id="permDelete"> Delete</label>
                            </div>
                        </div>
                    </div>
                </form>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn btn-success savePermissionMapping">Save Mapping</button>
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>

        </div>
    </div>
</div>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

<script>
    $(document).ready(function () {
        loadRoles();
    });
        $('#openMapPermissionModal').click(function () {
        $('#mapPermissionModal').modal('show');
        loadPermissionRoles();
        loadParentPermissions();
    });

    // Load roles into dropdown
    function loadPermissionRoles() {
        $.get( '@Url.Action("GetUserRoles", "Permission")', function (roles) {
            $('#mapRoleDropdown').empty().append('<option value="">-- Select Role --</option>');
            roles.forEach(role => {
                $('#mapRoleDropdown').append(`<option value="${role.roleId}">${role.roleName}</option>`);
            });
        });
    }

    // Load parent permissions
    function loadParentPermissions() {
        $.get('@Url.Action("GetParentMenus", "Permission")', function (permissions) {
            $('#parentPermissionDropdown').empty().append('<option value="">-- Select Parent Menu --</option>');
            permissions.forEach(menu => {
                $('#parentPermissionDropdown').append(`<option value="${menu.permissionId}">${menu.menuName}</option>`);
            });
        });
    }
       // When parent permission changes, load child menus
    $('#parentPermissionDropdown').change(function () {
        const parentId =parseInt($(this).val());
        if (!parentId) {
            $('#childPermissionDropdown').prop('disabled', true).html('<option value="">-- Select Child Menu --</option>');
            return;
        }

        $.get( '@Url.Action("GetChildMenusByParent", "Permission")?parentId=' + parentId, function (children) {
            const $childDropdown = $('#childPermissionDropdown');
            $childDropdown.prop('disabled', false).empty().append('<option value="">-- Select Child Menu --</option>');
            children.forEach(child => {
                $childDropdown.append(`<option value="${child.permissionId}">${child.menuName}</option>`);
            });
        });
    });

           $('#rolePermissionDropdown').on('change', function () {
        const selectedRoleId = $(this).val();
        if (selectedRoleId) {
            loadPermissionsByRole(selectedRoleId);
        } else {
            $('#permissionAccordion').empty();
        }
    });
       $('.savePermissionMapping').click(function () {
        const roleId = parseInt($('#rolePermissionDropdown').val());
        const parentId = parseInt($('#parentPermissionDropdown').val());
        const childId = parseInt($('#childPermissionDropdown').val()) || 0;

        const request = {
            RoleId: roleId,
            PermissionId: childId > 0 ? childId : parentId,
            CanCreate: $('#permCreate').is(':checked'),
            CanView: $('#permView').is(':checked'),
            CanEdit: $('#permEdit').is(':checked'),
            CanDelete: $('#permDelete').is(':checked'),
        };

        $.ajax({
            url: '@Url.Action("MapPermissionForRole", "Permission")',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(request),
            success: function (response) {
                if (response.success) {
                    alert("Mapping saved successfully.");
                    $('#mapPermissionModal').modal('hide');
                    location.reload();
                } else {
                    alert("Error: " + response.message);
                }
            },
            error: function () {
                alert("An error occurred while saving.");
            }
        });
    });
    function loadPermissionsByRole(roleId){
        // Fetch permissions for roleId=1
        $.ajax({
            url: '@Url.Action("GetSideBarPermissions", "Permission")?roleId=' + roleId,
            method: 'GET',
            success: function (response) {
                if (response) {
                    buildPermissionAccordion(response);
                }
            },
            error: function () {
                alert('Error fetching permissions');
            }
        });
    }
        // Build the accordion menu
        function buildPermissionAccordion(permissions) {
        localStorage.setItem('userPermissions', JSON.stringify(permissions));

        const container = $('#permissionAccordion');
        container.empty();

        permissions.forEach((item, index) => {
            const card = $(`
            <div class="card mb-3 border-left border-primary shadow-sm">
    <div class="card-header d-flex justify-content-between align-items-center bg-primary text-white w-100" id="heading-${index}">
        <h6 class="mb-0 font-weight-bold">
                <a class="text-white text-decoration-none" data-toggle="collapse" href="#collapse-${index}" aria-expanded="false" aria-controls="collapse-${index}">
                <i class="mdi mdi-folder-outline mr-1"></i> ${item.menuName}
            </a>
        </h6>
        <button class="btn btn-icon btn-light delete-mapping-btn mr-3" data-permission-id="${item.permissionId}" title="Delete Parent">
            <i class="mdi mdi-delete text-danger"></i>
        </button>
    </div>
                    <div id="collapse-${index}" class="collapse" aria-labelledby="heading-${index}" data-parent="#permissionAccordion">
                        <div class="card-body border-top">
                            <div class="mb-2 text-right">
                                <button class="btn btn-sm btn-outline-primary map-permission" data-id="${item.permissionId}" data-name="${item.menuName}">
                                    <i class="mdi mdi-plus-circle-outline"></i> Map Sub Menu
                                </button>
                            </div>
                            <div class="tree-line ml-3 border-left pl-3" id="drag-container-${index}"></div>
                        </div>
                    </div>
                </div>
            `);
            container.append(card);

            const dragContainer = $(`#drag-container-${index}`);
            const sortedChildren = (item.children || []).sort((a, b) => a.orderNo - b.orderNo);

            sortedChildren.forEach((child) => {
                const childCard = $(`
                    <div class="tree-item mb-2 p-2 bg-white border rounded d-flex align-items-start shadow-sm" data-id="${child.permissionId}">
                        <div class="flex-grow-1">
                            <div class="d-flex justify-content-between align-items-center mb-1">
                                <div>
                                    <strong class="text-secondary mr-2">${child.orderNo}.</strong>
                                    <span><i class="mdi mdi-subdirectory-arrow-right text-muted"></i> ${child.menuName}</span>
                                </div>
                                <div>
                                    <button class="btn btn-sm btn-outline-warning edit-child-menu"
                                        data-id="${child.permissionId}"
                                        data-name="${child.menuName}"
                                        data-iscreate="${child.canCreate}"
                                        data-isview="${child.canView}"
                                        data-isedit="${child.canEdit}"
                                        data-isdelete="${child.canDelete}"
                                        title="Edit">
                                        <i class="mdi mdi-pencil"></i>
                                    </button>
                                    <button class="btn btn-sm btn-danger delete-mapping-btn" data-permission-id="${child.permissionId}" title="Delete">
                                        <i class="mdi mdi-delete"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="permissions-status text-muted small ml-4">
                                <span class="mr-3">
                                    <strong>Create:</strong> ${child.canCreate ? '<i class="mdi mdi-check text-success"></i>' : '<i class="mdi mdi-close text-danger"></i>'}
                                </span>
                                <span class="mr-3">
                                    <strong>View:</strong> ${child.canView ? '<i class="mdi mdi-check text-success"></i>' : '<i class="mdi mdi-close text-danger"></i>'}
                                </span>
                                <span class="mr-3">
                                    <strong>Edit:</strong> ${child.canEdit ? '<i class="mdi mdi-check text-success"></i>' : '<i class="mdi mdi-close text-danger"></i>'}
                                </span>
                                <span>
                                    <strong>Delete:</strong> ${child.canDelete ? '<i class="mdi mdi-check text-success"></i>' : '<i class="mdi mdi-close text-danger"></i>'}
                                </span>
                            </div>
                        </div>
                    </div>
                `);

                dragContainer.append(childCard);
            });

            const drake = dragula([dragContainer[0]]);
            drake.on('drop', function () {
                updateOrderNumbers(dragContainer);
            });
        });
    }


        $(document).on('click', '.map-permission', function () {
     const parentId = $(this).data('id');
     const menuName = $(this).data('name');

     $('#mapPermissionModalLabel').text(`Map Permission - ${menuName}`);
     $('#selectedPermissionId').val(parentId);

     // Enable and clear the child dropdown, then fetch children
     const $childDropdown = $('#childPermissionDropdown');
     $childDropdown.prop('disabled', true).empty().append('<option value="">-- Loading Child Menus --</option>');

     $.get('@Url.Action("GetChildMenusByParent", "Permission")?parentId=' + parentId, function (children) {
         $childDropdown.empty().append('<option value="">-- Select Child Menu --</option>');

         if (children.length === 0) {
             $childDropdown.prop('disabled', true);
         } else {
             $childDropdown.prop('disabled', false);
             children.forEach(child => {
                 $childDropdown.append(`<option value="${child.permissionId}">${child.menuName}</option>`);
             });
         }
     });
         // $('#permissionsDisplayArea').html(`<p>Permissions for <strong>${menuName}</strong> will be mapped.</p>`);

    // Open modal
    $('#mapPermissionModal').modal('show');
          });

                  $(document).on('click', '.map-parent-permission', function () {
    loadParentPermissions();
    // Open modal
    $('#mapParentPermissionModal').modal('show');
          });
        // Update order numbers after drag and drop
        function updateOrderNumbers(container) {
            const updatedOrders = [];

            container.children('.card').each((i, el) => {
                const newOrder = i + 1;
                const $el = $(el);
                $el.find('.order-number').text(`${newOrder}.`);

                updatedOrders.push({
                    permissionId: $el.data('id'),
                    orderNo: newOrder
                });
            });

            // Send the updated order to the backend
            $.ajax({
                url: '@Url.Action("UpdatePermissionOrder", "Permission")',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(updatedOrders),
                success: function (response) {
                    console.log(response.message);
                                location.reload();

                },
                error: function (err) {
                    console.error('Failed to update order:', err);
                }
            });
        }

        // Track parent ID for adding child
        let currentParentId = null;

        // Open modal to add child
        $(document).on('click', '.add-child-menu', function () {
            currentParentId = $(this).data('id');
            $('#newMenuName').val('');
            $('#addChildMenuModal').modal('show');
        });

        // Save new child menu
        $('#saveChildMenu').click(function () {
            const menuName = $('#newMenuName').val();
            if (menuName && currentParentId) {
                $.ajax({
                    url:'@Url.Action("AddChildMenu", "Permission")',
                    method: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({ parentId: currentParentId, menuName: menuName }),
                    success: function () {
                        $('#addChildMenuModal').modal('hide');
                        location.reload();
                    },
                    error: function () {
                        alert('Error saving child menu');
                    }
                });
            }
        });
            let currentEditId = null;

    $(document).on('click', '.edit-child-menu', function () {
        currentEditId = $(this).data('id');
        $('#editPermissionId').val(currentEditId);
        $('#editMenuName').val($(this).data('name'));
        $('#editIsCreate').prop('checked', $(this).data('iscreate'));
        $('#editIsView').prop('checked', $(this).data('isview'));
        $('#editIsEdit').prop('checked', $(this).data('isedit'));
        $('#editIsDelete').prop('checked', $(this).data('isdelete'));
        $('#editChildMenuModal').modal('show');
    });

    $('#saveEditChildMenu').click(function () {
        const updatedData = {
            permissionId: $('#editPermissionId').val(),
            roleId: parseInt($('#rolePermissionDropdown').val()), // Make sure role dropdown is selected
            menuName: $('#editMenuName').val(),
            canCreate: $('#editIsCreate').is(':checked'),
            canView: $('#editIsView').is(':checked'),
            canEdit: $('#editIsEdit').is(':checked'),
            canDelete: $('#editIsDelete').is(':checked')
        };

        $.ajax({
            url: '@Url.Action("UpdatePermissionForRole", "Permission")',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(updatedData),
            success: function (response) {
                $('#editChildMenuModal').modal('hide');
               showSuccessToast(response.message);

                loadPermissionsByRole($('#rolePermissionDropdown').val()); // reload updated permissions
            },
            error:function (xhr) {
                const errorMessage = xhr.responseJSON?.message || Messages.GeneralError;
                showDangerToast(errorMessage);
            }
        });
    });



        function loadRoles() {
        $.ajax({
            url: '@Url.Action("GetUserRoles", "Permission")', // You must implement this API to fetch roles
            method: 'GET',
            success: function (roles) {
                const dropdown = $('#rolePermissionDropdown');
                roles.forEach(role => {
                    dropdown.append(`<option value="${role.roleId}">${role.roleName}</option>`);
                });
                var selectedRoleId = roles[0].roleId
               loadPermissionsByRole(selectedRoleId);

            },
            error: function () {
                alert('Failed to load roles');
            }
        });
    }

    // // Add delete mapping functionality
    // $(document).on('click', '.delete-mapping-btn', function () {
    //     const permissionId = $(this).data('permission-id');
    //     // Get the selected role ID from the dropdown at the top of the page
    //     const roleId = parseInt($('#rolePermissionDropdown').val());

    //     if (!roleId) {
    //         // Show an alert or toast if no role is selected
    //         alert('Please select a role first.');
    //         return;
    //     }

    //     // Show confirmation modal (assuming you have one like the previous delete modal)
    //     // You might want a specific modal for mapping deletion or reuse the existing one.
    //     // For simplicity, let's assume we're reusing the existing deletePermissionModal
    //     // and we'll store the permissionId and roleId temporarily before showing the modal.

    //     // Store IDs temporarily
    //     $('#confirmDeletePermissionBtn').data('permission-id', permissionId);
    //     $('#confirmDeletePermissionBtn').data('role-id', roleId);

    //     // Update modal body message if desired
    //     $('#deletePermissionModal .modal-body').text('Are you sure you want to delete this permission mapping?');

    //     // Show the modal
    //     $('#deletePermissionModal').modal('show');
    // });    // // Add delete mapping functionality
    // $(document).on('click', '.delete-mapping-btn', function () {
    //     const permissionId = $(this).data('permission-id');
    //     // Get the selected role ID from the dropdown at the top of the page
    //     const roleId = parseInt($('#rolePermissionDropdown').val());

    //     if (!roleId) {
    //         // Show an alert or toast if no role is selected
    //         alert('Please select a role first.');
    //         return;
    //     }

    //     // Show confirmation modal (assuming you have one like the previous delete modal)
    //     // You might want a specific modal for mapping deletion or reuse the existing one.
    //     // For simplicity, let's assume we're reusing the existing deletePermissionModal
    //     // and we'll store the permissionId and roleId temporarily before showing the modal.

    //     // Store IDs temporarily
    //     $('#confirmDeletePermissionBtn').data('permission-id', permissionId);
    //     $('#confirmDeletePermissionBtn').data('role-id', roleId);

    //     // Update modal body message if desired
    //     $('#deletePermissionModal .modal-body').text('Are you sure you want to delete this permission mapping?');

    //     // Show the modal
    //     $('#deletePermissionModal').modal('show');
    // });

    // Handle confirmation of delete mapping
       $(document).on('click', '.delete-mapping-btn', function () {
        const permissionId = $(this).data('permission-id');
        const roleId = parseInt($('#rolePermissionDropdown').val());

        if (!permissionId || !roleId) {
            alert('Error: Missing permission or role ID for deletion.');
            $('#deletePermissionModal').modal('hide');
            return;
        }

        $.ajax({
    url: '@Url.Action("DeletePermissionMapping", "Permission")' + `?permissionId=${permissionId}&roleId=${roleId}`,
            method: 'DELETE',
            success: function (response) {
                if (response.success) {
                    alert('Mapping deleted successfully.');
                    $('#deletePermissionModal').modal('hide');
                    // Reload permissions for the current role to update the accordion
                    const selectedRoleId = $('#rolePermissionDropdown').val();
                    if (selectedRoleId) {
                        loadPermissionsByRole(selectedRoleId);
                    }
                } else {
                    alert('Error deleting mapping: ' + response.message);
                    $('#deletePermissionModal').modal('hide');
                }
            },
            error: function (err) {
                alert('An error occurred while deleting the mapping.');
                console.error('Delete mapping error:', err);
                $('#deletePermissionModal').modal('hide');
            }
        });
    });

</script>
