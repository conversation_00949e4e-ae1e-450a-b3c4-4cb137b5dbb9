﻿@model HSSE_Models_Dto.ViewModels.AddUserViewModel

@{
    ViewBag.Title = "Edit Profile";
    var appBaseUrl = Url.Content("~/");
    var profileImageUrl = string.IsNullOrEmpty(Model.userDetails?.ProfileImageUrl)
          ? ""
          : Model.userDetails.ProfileImageUrl.StartsWith("/")
              ? Model.userDetails.ProfileImageUrl.Substring(1)
              : Model.userDetails.ProfileImageUrl;

    var hasFacility = Model.userDetails.facilityRoleConfigs.Any(f => !string.IsNullOrEmpty(f.FacilityName));

}


<div class="card mt-4" data-permission="view">
    <div class="card-body">
        <h4 class="card-title">Edit Profile</h4>

    <div class="row">
        <!-- Username -->
        <div class="col-md-6">
            <div class="form-group row">
                <label class="col-sm-3 col-form-label">Username</label>
                <div class="col-sm-9">
                    <input type="text" name="Username" value="@Model.userDetails.Username" class="form-control" readonly />
                </div>
            </div>
        </div>

        <!-- First Name -->
        <div class="col-md-6">
            <div class="form-group row">
                <label class="col-sm-3 col-form-label">First Name</label>
                <div class="col-sm-9">
                    <input type="text" name="FirstName" value="@Model.userDetails.FirstName" class="form-control" readonly />
                </div>
            </div>
        </div>
    </div>
<form asp-action="EditProfile" method="post" enctype="multipart/form-data">

    <div class="row">
                <div class="col-md-6">
                    <div class="form-group row">
                        <label class="col-sm-3 col-form-label">Employee Code</label>
                        <div class="col-sm-9">
                            <input type="text"
                                   name="EmployeeCode"
                                   value="@Model.userDetails.EmployeeCode"
                                   class="form-control"
                            @(string.IsNullOrWhiteSpace(Model.userDetails.EmployeeCode) ? "" : "readonly") />
                        </div>
                    </div>
                </div>
        <!-- Password -->
        <div class="col-md-6">
            <div class="form-group row">
                <label class="col-sm-3 col-form-label">Password</label>
                <div class="col-sm-9">
                            <input type="password" name="Password" value="@Model.userDetails.Password" id="password" class="form-control" />
                            <span class="input-group-append">
                                <i class="fa fa-eye-slash position-absolute" id="togglePassword"
                                   style="top: 38%; right: 19px; transform: translateY(-50%); cursor: pointer;"></i>
                            </span>
                </div>
            </div>
        </div>
    </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group row">
                        <label class="col-sm-3 col-form-label">Contact</label>
                        <div class="col-sm-9">
                            <input type="text" name="ContactNumber" value="@Model.userDetails.ContactNumber" class="form-control" />
                        </div>
                    </div>
                </div>
    <!-- Profile Image -->
    <div class="col-md-6">
                @{
                    var hasImage = !string.IsNullOrEmpty(profileImageUrl);
                }
                <div class="form-group row">
                    <label class="col-sm-3 col-form-label">Profile Image</label>
                    <div class="col-sm-9">
                        <div class="custom-file-upload">
                            <!-- Hidden actual file input -->
                            <input type="hidden" id="ExistingProfileImageUrl" name="ExistingProfileImageUrl" value="@profileImageUrl" />

                            <input type="file" class="d-none" id="editProfileImageInput" name="ProfileImage" accept="image/*" />

                            <!-- Visible input group -->
                            <div class="input-group">
                                <input type="text" class="form-control file-upload-info" id="editProfileImageFileName" readonly placeholder="Choose Image" />
                                <div class="input-group-append">
                                    <button class="btn btn-primary btn-sm" type="button" id="editProfileImageBtn">Upload</button>
                                    <button class="btn btn-danger btn-sm ml-1 @(hasImage ? "" : "d-none")" type="button" id="removeEditProfileImageBtn">Remove</button>
                                </div>
                            </div>

                            <!-- Preview -->
                            <div class="mt-2 @(hasImage ? "" : "d-none")" id="editProfileImagePreviewContainer">
                                <img id="editProfileImagePreview" src="@profileImageUrl" class="img-thumbnail" style="max-width: 250px;" />
                            </div>
                        </div>
                    </div>
                </div>

    </div>
    </div>
    <button type="submit" class="btn btn-primary">Save Changes</button>

</form>

    <hr />

        <table class="table table-bordered">
            <thead class="thead-light">
                <tr>
                    @if (hasFacility)
                    {
                        <th>Role</th>

                        <th>Facility</th>
                    }
                    else
                    {
                        <th>Role</th>
                    }
                </tr>
            </thead>
            <tbody>
                @foreach (var config in Model.userDetails.facilityRoleConfigs)
                {
                    <tr>
                        @if (hasFacility)
                        {
                            @if (!string.IsNullOrEmpty(config.FacilityName))
                            {
                               
                                <td>@config.RoleName</td>
                                <td>@config.FacilityName</td>
                            }
                            else
                            {
                                <td>@config.RoleName</td>
                                <td class="text-muted">N/A</td>
                                
                            }
                        }
                        else
                        {
                            <td>@config.RoleName</td>
                        }
                    </tr>
                }
            </tbody>
        </table>

    </div>
</div>

@section Scripts {
    <script src="~/js/User/editProfile.js"></script>
}
