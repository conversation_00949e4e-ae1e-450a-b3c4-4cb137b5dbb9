@model HSSE_Models_Dto.ViewModels.AddUserViewModel

@{
    ViewData["Title"] = "Create Post";
}
<div class="col-12 grid-margin">
    <div class="card">
        <div class="card-body">
            <h4 class="card-title">Create Post</h4>
            <form id="postForm" class="form-sample">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group row">
                            <label for="Title" class="col-sm-4 col-form-label">Title <span class="text-danger">*</span></label>
                            <div class="col-sm-8">
                                <input type="text" class="form-control" id="Title" name="Title" required />
                            </div>
                        </div>
                        <div class="form-group row">
                            <label for="PostType" class="col-sm-4 col-form-label">Post Type <span class="text-danger">*</span></label>
                            <div class="col-sm-8">
                                <input type="text" class="form-control" id="PostType" name="PostType" required />
                            </div>
                        </div>
                        <div class="form-group row">
                            <label for="Location" class="col-sm-4 col-form-label">Location</label>
                            <div class="col-sm-8">
                                <input type="text" class="form-control" id="Location" name="Location" />
                            </div>
                        </div>
                       @*  <div class="form-group row">
                            <label class="col-sm-4 col-form-label">Status</label>
                            <div class="form-check">
                                <label class="form-check-label">
                                    <input type="checkbox" class="form-check-input" id="Status" name="Status" />
                                </label>
                            </div>
                        </div> *@
                    </div>
                    <div class="col-md-6">
                        <div class="form-group row">
                            <label for="TaggedCategoryId" class="col-sm-4 col-form-label">Category<span class="text-danger">*</span></label>
                            <div class="col-sm-8">
                                <select name="TaggedCategoryId" id="TaggedCategoryId" class="form-control">
                                    <option value="">-- Select Category --</option>
                                @foreach (var user in Model.MstPostCategory)
                                {
                                    <option value="@user.CatId">@user.CategoryName</option>
                                }
                            </select>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="FacilityId" class="col-sm-4 col-form-label">Facility<span class="text-danger">*</span></label>
                            <div class="col-sm-8">
                                <select name="FacilityId" id="FacilityId" class="form-control">
                                    <option value="">-- Select Facility --</option>

                                    @foreach (var user in Model.Facilities)
                                    {
                                        <option value="@user.FacilityId">@user.FacilityName</option>
                                    }
                                </select>
                            </div>
                        </div>
                        <div class="form-group row">
                           @*      <label class="col-sm-4 col-form-label">Upload Image</label>
                                <div class="col-sm-6 grid-margin stretch-card">
                                    <div class="card">
                                        <div class="card-body">
                                            <input type="file" class="dropify" name="PostFile" id="PostFile" />
                                        </div>
                                    </div>
                                </div> *@
                     
                           
                                    <label class="col-sm-4 col-form-label">Upload Image<span class="text-danger">*</span></label>
                                    <div class="col-sm-8">
                                        <div class="custom-file-upload w-100">
                                            <input type="file" class="d-none" id="ThumbnailFile" name="ThumbnailFile" accept="image/*" />
                                            <div class="input-group">
                                                <input type="text" class="form-control file-upload-info" id="ThumbnailFileName" readonly placeholder="Choose Thumbnail" />
                                                <div class="input-group-append">
                                                    <button class="btn btn-primary btn-sm" type="button" id="ThumbnailBtn">Upload</button>
                                                    <button class="btn btn-danger btn-sm ml-1 d-none" type="button" id="removeThumbnailBtn">Remove</button>
                                                </div>
                                            </div>
                                            <div class="mt-2 d-none" id="ThumbnailPreviewContainer">
                                                <img id="ThumbnailPreview" src="" class="img-thumbnail" style="max-width: 250px;" />
                                            </div>
                                

                                    @*   <div class="col-sm-6 grid-margin stretch-card">
                            <div class="card">
                                <div class="card-body">
                                    <input type="file" class="dropify" name="ProfileImage" id="ThumbnailFile" />
                                </div>
                            </div>
                        </div> *@
                                </div>
                            </div>
@*                             <label class="col-sm-4 col-form-label">File upload</label>
                            <div class="col-sm-8">
                                <div class="input-group">
                                    <input type="file" name="img[]" class="file-upload-default d-none" id="PostFile">
                                    <input type="text" class="form-control file-upload-info" disabled placeholder="Upload Image">
                                    <div class="input-group-append">
                                        <button class="file-upload-browse btn btn-primary py-0 px-2" type="button">Upload</button>
                                    </div>
                                </div>
                            </div> *@
                        </div>

                    </div>

                </div>
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-group row">
                            <label class="col-sm-2 col-form-label">Description</label>
                            <div class="col-sm-12">
                                <textarea name="Description" id="tinyMceExample" class="form-control"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-12 text-end">
                        <button type="submit" class="btn btn-primary">Create Post</button>
                        <button type="reset" class="btn btn-light">Cancel</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
        var createPost = '@Url.Action("Create", "Post")';
</script>
@section Scripts {
    <script src="~/js/Post/post.js"></script>
} 