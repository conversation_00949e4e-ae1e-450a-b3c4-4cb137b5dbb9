using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HSSE_Models_Dto.ModelsDto
{
    public class MstFeedbackDto
    {
        public int FeedbackId { get; set; }

        public string Name { get; set; } = null!;

        public string Title { get; set; } = null!;

        public string? Description { get; set; }

        public string? Response { get; set; }

        public string? FilePath { get; set; }

        public int? Status { get; set; }

        public DateTime? CreatedAt { get; set; }

        public int? CreatedBy { get; set; }

        public int? UpdatedBy { get; set; }

        public int? FacilityId { get; set; }
        public string? FileName { get; set; }
        public string? ExistingDocUrl { get; set; }
        public DateTime? Date { get; set; }
    }
    public class MstFeedbackResponseDto
    {
        public int FeedbackId { get; set; }

        public string? Response { get; set; }

        public int? Status { get; set; }
        public int? userId { get; set; }
    }
} 