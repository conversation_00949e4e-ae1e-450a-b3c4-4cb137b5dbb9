/* Navbar */

.navbar {
  font-weight: 400;
  transition:background $action-transition-duration $action-transition-timing-function;
  -webkit-transition:background $action-transition-duration $action-transition-timing-function;
  -moz-transition:background $action-transition-duration $action-transition-timing-function;
  -ms-transition:background $action-transition-duration $action-transition-timing-function;
  border-bottom: 1px solid $border-color;

  .navbar-brand-wrapper {
    background: $sidebar-bg;
    padding: $sidebar-padding;
    transition: width $action-transition-duration $action-transition-timing-function, background $action-transition-duration $action-transition-timing-function;
    -webkit-transition: width $action-transition-duration $action-transition-timing-function, background $action-transition-duration $action-transition-timing-function;
    -moz-transition: width $action-transition-duration $action-transition-timing-function, background $action-transition-duration $action-transition-timing-function;
    -ms-transition: width $action-transition-duration $action-transition-timing-function, background $action-transition-duration $action-transition-timing-function;
    width: $sidebar-width-lg;
    height: $navbar-height;
    border-right: 1px solid $border-color;
    @media(max-width: 991px) {
      padding: .9rem;
    }

    .navbar-brand {
      color: lighten(color(gray-dark), 20%);
      font-size: 1.5rem;
      margin-right: 0;
      padding: .25rem 0;
      
      &.brand-logo-mini {
        display: none;
      }

      &:active,
      &:focus,
      &:hover {
        color: lighten(color(gray-dark), 10%);
      }

      img {
        width: calc(#{$sidebar-width-lg} - 130px );
        max-width: 100%;
        margin: auto;
        vertical-align: middle;
      }
    }

    .brand-logo-mini {
      padding-left: 0;
      text-align: center;
      img {
        width: calc(#{$sidebar-width-icon} - 30px );
        height: 22px;
        max-width: 100%;
        margin: auto;
      }
    }
  }

  .navbar-menu-wrapper {
    background: $navbar-default-bg;
    transition: width $action-transition-duration $action-transition-timing-function;
    -webkit-transition: width $action-transition-duration $action-transition-timing-function;
    -moz-transition: width $action-transition-duration $action-transition-timing-function;
    -ms-transition: width $action-transition-duration $action-transition-timing-function;
    color: $navbar-menu-color;
    padding-left: 1.25rem;
    padding-right: 1.25rem;
    width: calc(100% - #{$sidebar-width-lg});
    height: $navbar-height;
    box-shadow: 18px 5px 10px #222334;
    @media (max-width: 991px) {
      width: calc(100% - 55px);
      padding-left: 15px;
      padding-right: 15px;
    }

    .navbar-toggler {
      border: 0;
      color: inherit;
      font-size: 1.5rem;
      padding: 0;
      &:not(.navbar-toggler-right) {
        @media (max-width: 991px) {
          display: none;
        }
      }
      &.navbar-toggler-right{
        @media (max-width:991px){
          padding-left: 15px;
          padding-right: 11px;
        }
      }
    }

    .navbar-nav {
      @extend .d-flex;
      @extend .flex-row;
      @extend .align-items-center;
      .nav-item {
        margin-left: .25rem;
        margin-right: .25rem;
        width: 2rem;
        &:last-child {
          margin-right: 0;
        }
        .nav-link {
          color: inherit;
          font-size: $navbar-font-size;
        }
        &.nav-search {
          margin-left: 2rem;
          margin-right: 1.5rem;
          flex: 1;
          .input-group {
            background: $content-bg;
            border: 2px solid $border-color;
            border-radius: 0;
            height: 2.5rem;
            padding: 0 .75rem;
            .form-control,
            .input-group-text {
              background: transparent;
              border: 0;
              color: #6f7082;
              padding: 0;
              i {
                font-size: 1.5rem;
                color: $text-muted;
              }
            }
            .form-control {
              margin-left: .5rem;
              color: $navbar-menu-color;
              @include placeholder {
                color: #6f7082;
              }
            }
          }
        }
        &.nav-settings {
          @extend .align-self-stretch;
          @extend .align-items-center;
          margin: 0;
          padding-left: .75rem;
          width: auto;

          .nav-link {
            padding: 0;
            text-align: center;
          }
          i {
            font-size: 1.5rem;
            vertical-align: middle;
          }
        }
        &.nav-profile {
          margin-left: .6rem;
          @extend .d-flex;
          @extend .align-items-center;
          img {
            width: 30px;
            height: 30px;
            border-radius: 100%;
          }
          .nav-profile-name {
            margin-left: .5rem;
            @media (max-width: 767px) {
              display: none;
            }
          }
        }
        &.dropdown {
          .dropdown-menu {
            @extend .dropdownAnimation;        
            border: 2px solid $border-color;
          }
          .navbar-dropdown {
            position: absolute;            
            font-size: 0.9rem;
            right: -14px;
            left: auto;
            top: $navbar-height;
            &::before {
              content: '';
              width: 15px;
              height: 15px;
              background: $card-bg;
              position: absolute;
              right: 20px;
              top: -9px;
              border-left: 2px solid $border-color;
              border-top: 2px solid $border-color;
              transform: rotate(45deg);
              @media(max-width: 991px) {
                display: none;
              }
            }
            .rtl & {
              right: auto;
              left: 0;
            }

            .dropdown-item {
              @extend .d-flex;
              @extend .align-items-center;
              margin-bottom: 0;
              padding: .4rem 1.5rem;
              cursor: pointer;
              color: $navbar-menu-color;

              &:hover {
                background: darken($content-bg, 5%);
              }

              i {
                font-size: 17px;
              }

              .badge {
                margin-left: 2.5rem;
              }
              .ellipsis {
                max-width: 200px;
                overflow: hidden;
                text-overflow: ellipsis;
              }
              .rtl & {
                i {
                  @extend .mr-0;
                  margin-left: 10px;
                }
                .badge {
                  margin-left: 0;
                  margin-right: 2.5rem;
                }
              }
            }

            .dropdown-divider {
              margin: 0;
            }
          }
          i {
            margin-right: .5rem;
            vertical-align: middle;
          }
          @media (max-width: 991px) {
            position: static;
            .navbar-dropdown {
              left: 20px;
              right: 20px;
              top: $navbar-height;
              width: calc(100% - 40px);
            }
          }
          .count-indicator {
            position: relative;
            padding: 0;
            text-align: center;
            i {
              font-size: 1.38rem;
              margin-right: 0;
              vertical-align: middle;
            }
            .count {
              position: absolute;
              left: 36%;
              top: 2px;
              width: 6px;
              height: 6px;
              border-radius: 100%;
              background: theme-color(danger);
            }
            &:after {
              display: none;
            }
          }
        }
      }
      &:not(.navbar-nav-right) {
        flex: 1;
      }
      &.navbar-nav-right {
        @extend .align-self-stretch;
        @extend .align-items-stretch;
        .nav-item {
          @extend .d-flex;
          @extend .align-items-center;
          @extend .justify-content-center;
        }
        @media (min-width: 992px) {
          margin-left: auto;
          .rtl & {
            margin-left: 0;
            margin-right: auto;
          }
        }
      }
    }
  }

  /* Navbar color variations */
  @each $color, $value in $theme-colors {
    &.navbar-#{$color} {
      .navbar-menu-wrapper {
        background: $value;
      }
    }
    &.navbar-light {
      .navbar-menu-wrapper {
        background: $white;
        .navbar-toggler {
          color: theme-color(dark);
        }
        .nav-item {
          .nav-link {
            color: theme-color(dark);
          }
        }
      }
    }
    &.navbar-danger {
      .navbar-menu-wrapper {
        .nav-item {
          .nav-link {
            &.count-indicator {
              .count {
                background: theme-color(primary);
              }
            }
          }
        }
      }
    }
  }
}
@media (max-width:991px) {
  .navbar {
    flex-direction: row;
    .navbar-brand-wrapper {
      width: 55px;
      .navbar-brand {
        &.brand-logo {
          display: none;
        }
        &.brand-logo-mini {
          display: inline-block;
        }
      }
    }
  }

  .navbar-collapse {
    display: flex;
    margin-top: 0.5rem;
  }
}

@media (max-width:480px) {
  .navbar {
    .navbar-brand-wrapper {
      width: 55px;
      .brand-logo-mini{
        padding-top: 0px;
      }
    }
  }
}