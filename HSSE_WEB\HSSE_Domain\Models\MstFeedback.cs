﻿using System;
using System.Collections.Generic;

namespace HSSE_Domain.Models;

public partial class MstFeedback
{
    public int FeedbackId { get; set; }

    public string Name { get; set; } = null!;

    public string Title { get; set; } = null!;

    public string? Description { get; set; }

    public string? Response { get; set; }

    public string? FilePath { get; set; }

    public int? Status { get; set; }

    public DateTime? CreatedAt { get; set; }

    public int? CreatedBy { get; set; }

    public int? UpdatedBy { get; set; }

    public int? FacilityId { get; set; }

    public DateTime? Date { get; set; }

    public virtual MstFacility? Facility { get; set; }
}
