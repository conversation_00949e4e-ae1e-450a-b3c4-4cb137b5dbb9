@{
    ViewData["Title"] = "Assigned Inspections";
}
<div class="col-12 grid-margin">
    <div class="card shadow-sm">
        <div class="card-body">
            <h4 class="card-title">Assigned Inspections</h4>
            <div id="assigned-inspections-list">
                <div class="table-responsive">
                    <table class="table table-bordered table-hover" id="inspectionsTable">
                        <thead class="thead-light">
                            <tr>
                                <th>Observation</th>
                                <th>Recommendation</th>
                                <th>Date</th>
                                <th>Inspector</th>
                                <th>Status</th>
                                <th>Verification</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody id="inspectionsTableBody">
                            <!-- Rows inserted via JS -->
                        </tbody>
                    </table>
                </div>
            </div>
            <!-- Inspections will be loaded here by JS -->
            </div>
            <nav aria-label="Page navigation" class="mt-4">
                <ul class="pagination justify-content-center" id="pagination">
                    <!-- Pagination will be added here by JS -->
                </ul>
            </nav>
        </div>
    </div>
<!-- Modal to show card preview -->
<div class="modal fade" id="inspectionPreviewModal" tabindex="-1" role="dialog" aria-labelledby="inspectionPreviewModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Inspection Preview</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body" id="inspectionPreviewContent">
                <!-- Card content will be loaded here -->
            </div>
        </div>
    </div>
</div>

<!-- Complete Inspection Modal -->
<div class="modal fade" id="completeInspectionModal" tabindex="-1" role="dialog" aria-labelledby="completeInspectionModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <form id="completeInspectionForm">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="completeInspectionModalLabel">Complete Inspection</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <input type="hidden" id="completeInspectionId" name="InspectionId" />
                    <div class="form-group">
                        <label>After Image</label>
                        <div class="input-group  mt-3">
                            <input type="file" class="file-upload-default d-none" id="afterImageInput">
                            <input type="text" class="form-control file-upload-info" disabled placeholder="Upload image">
                            <div class="input-group-append">
                                <button class="file-upload-browse btn btn-primary py-0 px-2" type="button">Upload</button>
                            </div>

                        </div>
                    </div>
                    <div class="form-group">
                        <label>Completion Date</label>
                        <input type="date" class="form-control" id="completionDateInput" name="CompletionDate" />
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Complete Inspection</button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Rectification Remarks Modal -->
<div class="modal fade" id="rectificationModal" tabindex="-1" role="dialog" aria-labelledby="rectificationModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <form id="rectificationForm">
                <div class="modal-header">
                    <h5 class="modal-title" id="rectificationModalLabel">Cannot Rectify - Remarks</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <input type="hidden" id="rectificationInspectionId" name="inspectionId" />
                    <div class="form-group">
                        <label for="rectificationRemarks">Remarks</label>
                        <textarea class="form-control" id="rectificationRemarks" name="remarks" rows="4" required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">Submit</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
  var completeInspection =  '@Url.Action("CompleteInspection", "Inspection")';
  var getInspectionsByActionParty =   '@Url.Action("GetInspectionsByActionParty", "Inspection")?status=';
 var getAllFacility = '@Url.Action("GetAllFacility", "Facility")';
                var updateVerificationStatus = '@Url.Action("UpdateVerificationStatus", "Inspection")';

 var status =  null ;
    const basePath = '@Url.Content("~/")'; // returns '/UEMS/HSSEWeb/' in production

</script>
@section Scripts {
    <script src="~/js/Inspection/assignedInspections.js"></script>
} 