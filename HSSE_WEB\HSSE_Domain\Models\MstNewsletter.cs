﻿using System;
using System.Collections.Generic;

namespace HSSE_Domain.Models;

public partial class MstNewsletter
{
    public int NewsletterId { get; set; }

    public string Title { get; set; } = null!;

    public string? Description { get; set; }

    public string? ThumbnailPath { get; set; }

    public DateTime? CreatedAt { get; set; }

    public int? CreatedBy { get; set; }

    public int? FacilityId { get; set; }

    public bool IsActive { get; set; }

    public DateTime? ScheduleAt { get; set; }

    public string? FileUrl { get; set; }

    public DateTime? ModifiedAt { get; set; }

    public int? ModifiedBy { get; set; }

    public virtual MstFacility? Facility { get; set; }
}
