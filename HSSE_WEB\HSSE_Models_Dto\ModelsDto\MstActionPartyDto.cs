using System;
using System.Collections.Generic;

namespace HSSE_Models_Dto.ModelsDto
{
    public class MstActionPartyDto
    {
        public int ActionPartyId { get; set; }
        public string Name { get; set; } = null!;
        public DateTime? CreatedAt { get; set; }
        public int? CreatedBy { get; set; }
        public DateTime? ModifiedAt { get; set; }
        public int? ModifiedBy { get; set; }
        public bool IsActive { get; set; }
        public int? FacilityId { get; set; }
        public List<MstActionPartyUserMappingDto>? UserMappings { get; set; }
    }

    public class MstActionPartyUserMappingDto
    {
        public int MappingId { get; set; }
        public int? ActionPartyId { get; set; }
        public int? UserId { get; set; }
        public DateTime? CreatedAt { get; set; }
        public int? CreatedBy { get; set; }
        public DateTime? ModifiedAt { get; set; }
        public int? ModifiedBy { get; set; }
        public bool IsActive { get; set; }
        public string? UserName { get; set; }
    }
    public class ActionPartyViewDto
    {
        public int ActionPartyId { get; set; }
        public string ActionPartyName { get; set; }
        public string FacilityName { get; set; }
        public string UserNames { get; set; }
        public bool IsActive { get; set; }
    }
} 