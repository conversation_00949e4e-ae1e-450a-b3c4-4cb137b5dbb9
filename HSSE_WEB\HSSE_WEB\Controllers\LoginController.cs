﻿using AspNetCoreGeneratedDocument;
using HSSE_Models_Dto.ModelsDto;
using HSSE_Service.Constant;
using HSSE_Service.InterfaceService;
using HSSE_Service.ServiceResponce;
using Microsoft.AspNetCore.Identity.Data;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;

namespace HSSE_WEB.Controllers
{
    public class LoginController : Controller
    {
        private readonly ILogger<LoginController> _logger;
        private readonly IUserService _userService;
        private readonly IFacilityService _facilityService;

        private IConfiguration _configuration;

        public LoginController(IConfiguration configuration, IUserService userService, ILogger<LoginController> logger, IFacilityService facilityService)
        {
            _logger = logger;
            _userService = userService;
            _facilityService = facilityService;
            _configuration = configuration;
        }

        public IActionResult Index()
        {
            var ssoUrl = _configuration["SSO:SSO_WEB_URL"];
            ViewData["SSOUrl"] = ssoUrl;
            return View();
        }

        public IActionResult Login()
        {
            return View();
        }

        [HttpPost]
        public async Task<IActionResult> LoginUser(string username, string password)
        {
            //if (!ModelState.IsValid)
            //{
            //    ViewBag.ErrorMessage = "Please fill in all required fields.";
            //    return View(model);
            //}

            var response = await _userService.LoginUserAsync( username, password);

            //if (response is UnauthorizedObjectResult unauthorizedResult)
            //{
            //    ViewBag.ErrorMessage = "Invalid username or password.";
            //    return View(model);
            //}

            if (response.Success)
            {
                var dataJson = JsonConvert.SerializeObject(response.Data);
                var resultData = JsonConvert.DeserializeObject<LoginResponseDto>(dataJson);

                HttpContext.Session.SetString("Token", resultData.Token.ToString());
                HttpContext.Session.SetString("Username", resultData.Username);
                HttpContext.Session.SetString("Email", resultData.Email);
                HttpContext.Session.SetString("UserId", resultData.UserId.ToString());
                HttpContext.Session.SetString("FacilityIds", string.Join(",", resultData?.FacilityIds));

                // If needed, store RoleIds as comma-separated string
                HttpContext.Session.SetString("RoleIds", string.Join(",", resultData.RoleIds));

                HttpContext.Session.SetString("IsAppAdmin", resultData.IsAppAdmin.ToString());
                HttpContext.Session.SetString("IsDepartmentAdmin", resultData.IsDepartmentAdmin.ToString());
                HttpContext.Session.SetString("IsFacilityAdmin", resultData.IsFacilityAdmin.ToString());
                HttpContext.Session.SetString("IsManager", resultData.IsManager.ToString());

                // Save only if FacilityAdminFacilityIds is available
                if (resultData.FacilityAdminFacilityIds != null)
                {
                    HttpContext.Session.SetString("FacilityAdminFacilityIds", string.Join(",", resultData.FacilityAdminFacilityIds));
                }
                if (resultData.ManagerFacilityIds != null)
                {
                    HttpContext.Session.SetString("ManagerFacilityIds", string.Join(",", resultData.ManagerFacilityIds));
                }
                //var facilityDetails = await _facilityService.GetFacilityListAsync();
                return Ok(new ApiResponseDto<bool> { Success = true, Message = ApiMessages.UserRegisteredSuccessfully, Status = 200 });
            }
            else
            {
                return StatusCode(400, new ApiResponseDto<bool> { Success = false, Message = response.Message, Status = 400 });
            }
        }
        public IActionResult Register()
        {
            return View();
        }
      
        public async Task<IActionResult> RegisterUser([FromBody] MstUserDto model)
        {
            var response = await _userService.RegisterUserAsync(model);

            // Return response based on success or failure
            if (response.Success)
            {
                return Ok(new ApiResponseDto<bool> { Success = true, Message = ApiMessages.UserRegisteredSuccessfully, Status = 200 });
            }
            else
            {
                return StatusCode(400, new ApiResponseDto<bool> { Success = false, Message = response.Message, Status = 400 });
            }
        }

    }
}
