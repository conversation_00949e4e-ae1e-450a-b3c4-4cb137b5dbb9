(function ($) {
    showSwal = function (type, message = '', title = '') {
        'use strict';

        switch (type) {
            case 'basic':
                swal({
                    text: message || 'Any fool can use a computer',
                    button: {
                        text: "OK",
                        value: true,
                        visible: true,
                        className: "btn btn-primary"
                    }
                });
                break;

            case 'title-and-text':
                swal({
                    title: title || 'Read the alert!',
                    text: message || 'Click OK to close this alert',
                    button: {
                        text: "OK",
                        value: true,
                        visible: true,
                        className: "btn btn-primary"
                    }
                });
                break;

            case 'success-message':
                swal({
                    title: title || 'Success!',
                    text: message || 'Operation completed successfully.',
                    icon: 'success',
                    button: {
                        text: "Continue",
                        value: true,
                        visible: true,
                        className: "btn btn-success"
                    }
                });
                break;

            case 'error-message':
                swal({
                    title: title || 'Error!',
                    text: message || 'Something went wrong.',
                    icon: 'error',
                    button: {
                        text: "OK",
                        value: true,
                        visible: true,
                        className: "btn btn-danger"
                    }
                });
                break;

            case 'info-message':
                swal({
                    title: title || 'Information',
                    text: message || 'Here is some useful info.',
                    icon: 'info',
                    button: {
                        text: "OK",
                        value: true,
                        visible: true,
                        className: "btn btn-info"
                    }
                });
                break;

            case 'auto-close':
                swal({
                    title: title || 'Auto close alert!',
                    text: message || 'I will close in 2 seconds.',
                    timer: 2000,
                    button: false
                }).then(
                    function () { },
                    function (dismiss) {
                        if (dismiss === 'timer') {
                            console.log('Closed by timer');
                        }
                    }
                );
                break;

            case 'warning-message-and-cancel':
                swal({
                    title: title || 'Are you sure?',
                    text: message || "You won't be able to revert this!",
                    icon: 'warning',
                    buttons: {
                        cancel: {
                            text: "Cancel",
                            value: null,
                            visible: true,
                            className: "btn btn-danger",
                            closeModal: true,
                        },
                        confirm: {
                            text: "Yes, proceed!",
                            value: true,
                            visible: true,
                            className: "btn btn-primary",
                            closeModal: true
                        }
                    }
                });
                break;

            case 'custom-html':
                swal({
                    content: {
                        element: "input",
                        attributes: {
                            placeholder: "Type your password",
                            type: "password",
                            class: 'form-control'
                        },
                    },
                    button: {
                        text: "Submit",
                        value: true,
                        visible: true,
                        className: "btn btn-primary"
                    }
                });
                break;

            default:
                swal({
                    title: title || 'Notice',
                    text: message || 'Unknown alert type',
                    icon: 'info',
                    button: {
                        text: "OK",
                        className: "btn btn-primary"
                    }
                });
        }
    };
})(jQuery);
