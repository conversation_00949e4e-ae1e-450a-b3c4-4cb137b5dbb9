﻿<!DOCTYPE html>
<html lang="en">

<head>
    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title>HSSE Dashboard</title>
    <!-- base:css -->
    <link rel="stylesheet icon" href="~/css/site.css" />
    <link rel="stylesheet" href="~/Content/css/vertical-layout-light/style.css">
    <link rel="stylesheet" href="~/Content/vendors/font-awesome/css/font-awesome.min.css">
    <link rel="stylesheet" href="~/Content/vendors/jquery-file-upload/uploadfile.css">
    <link rel="stylesheet" href="~/Content/vendors/select2/select2.min.css">
    <link rel="stylesheet" href="~/Content/vendors/select2-bootstrap-theme/select2-bootstrap.min.css">
    <link rel="stylesheet" href="~/Content/vendors/summernote/dist/summernote-bs4.css">
    <link rel="stylesheet" href="~/Content/vendors/quill/quill.snow.css">
    <link rel="stylesheet" href="~/Content/vendors/simplemde/simplemde.min.css">
    <link rel="stylesheet" href="~/Content/vendors/mdi/css/materialdesignicons.min.css">
    <link rel="stylesheet" href="~/Content/vendors/css/vendor.bundle.base.css">
    <!-- endinject -->
    <!-- plugin css for this page -->
    <link rel="stylesheet" href="~/Content/vendors/jqvmap/jqvmap.min.css">
    <link rel="stylesheet" href="~/Content/vendors/flag-icon-css/css/flag-icon.min.css">
    <!-- End plugin css for this page -->
    <!-- inject:css -->
    <link rel="stylesheet" href="~/Content/vendors/dropify/dropify.min.css">
    <link rel="stylesheet" href="~/Content/vendors/jquery-toast-plugin/jquery.toast.min.css" />
    <link rel="stylesheet" href="~/Content/vendors/datatables.net-bs4/dataTables.bootstrap4.css">
    <link rel="stylesheet" href="~/Content/vendors/dragula/dragula.min.css">

    <!-- endinject -->
    <!-- Custom Profile Navbar CSS -->
    <link rel="stylesheet" href="~/Content/css/profile-navbar.css">
    <link rel="shortcut icon" href="~/content/images/favicon.png" />

</head>
<body>
    <div class="container-scroller">
        <!-- partial:partials/_navbar.html -->
        <nav class="navbar col-lg-12 col-12 p-0 fixed-top d-flex flex-row">
            <div class="text-center navbar-brand-wrapper d-flex align-items-center">
                <a class="navbar-brand brand-logo" asp-controller="Home" asp-action="login"><img src="~/Content/images/dashboard/image-removebg-preview (1).png" alt="logo" /></a>
                <a class="navbar-brand brand-logo-mini" asp-controller="Home" asp-action="login"><img src="~/Content/images/dashboard/image-removebg-preview (1).png" style="height:50px; width:auto;" alt="logo" /></a>
            </div>
            <div class="navbar-menu-wrapper d-flex align-items-center justify-content-between">
                <button class="navbar-toggler navbar-toggler align-self-center" type="button" data-toggle="minimize">
                    <span class="mdi mdi-menu"></span>
                </button>

                <!-- Left side navigation items (commented out) -->
                <ul class="navbar-nav mr-lg-2">
                 @*    <li class="nav-item col-md-3">
                        <select id="facilityDropdown" class="form-control">
                            <option value="">Select Facility</option>
                        </select>
                    </li>
                    <li class="nav-item col-md-3">
                        <select id="roleDropdown" class="form-control" disabled>
                            <option value="">Select Role</option>
                        </select>
                    </li> *@
                </ul>

                <!-- Right side - Profile and Logout Section -->
                <div class="d-flex align-items-center ml-auto">
                    <ul class="navbar-nav navbar-nav-right ml-auto">
                        <li class="nav-item nav-profile dropdown">
                            <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" data-toggle="dropdown" id="profileDropdown" aria-haspopup="true" aria-expanded="false">
                                <div class="nav-profile-img">
                                    <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiM2Yzc1N2QiLz4KPHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4PSI4IiB5PSI4Ij4KPHBhdGggZD0iTTEyIDEyQzE0LjIwOTEgMTIgMTYgMTAuMjA5MSAxNiA4QzE2IDUuNzkwODYgMTQuMjA5MSA0IDEyIDRDOS43OTA4NiA0IDggNS43OTA4NiA4IDhDOCAxMC4yMDkxIDkuNzkwODYgMTIgMTIgMTJaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNMTIgMTRDOC4xMzQwMSAxNCA1IDE3LjEzNDAxIDUgMjFIMTlDMTkgMTcuMTM0MDEgMTUuODY2IDE0IDEyIDE0WiIgZmlsbD0id2hpdGUiLz4KPC9zdmc+Cjwvc3ZnPgo=" alt="Profile" id="userProfileImage">
                                    <span class="availability-status online"></span>
                                </div>
                                <div class="nav-profile-text d-flex flex-column">
                                    <span class="font-weight-bold mb-0" id="userDisplayName">@Context.Session.GetString("Username")</span>
                                    <span class="text-secondary text-small" id="userEmail">@Context.Session.GetString("Email")</span>
                                </div>
                            </a>
                            <div class="dropdown-menu dropdown-menu-right navbar-dropdown" aria-labelledby="profileDropdown">
                                <a class="dropdown-item" asp-controller="User" asp-action="EditProfile">
                                    <i class="mdi mdi-account-circle text-primary"></i>
                                    My Profile
                                </a>
                                <a class="dropdown-item" href="#" id="settingsLink">
                                    <i class="mdi mdi-settings text-primary"></i>
                                    Settings
                                </a>
                                <div class="dropdown-divider"></div>
                                <a class="dropdown-item" href="#" id="navLogoutBtn">
                                    <i class="mdi mdi-logout text-primary"></i>
                                    Sign Out
                                </a>
                            </div>
                        </li>
                    </ul>

                    <button class="navbar-toggler navbar-toggler-right d-lg-none align-self-center ml-2" type="button" data-toggle="offcanvas">
                        <span class="mdi mdi-menu"></span>
                    </button>
                </div>
            </div>
        </nav>
        <!-- partial -->
        <div class="container-fluid page-body-wrapper">
            <!-- partial:partials/_settings-panel.html -->
     @*        <div class="theme-setting-wrapper">
                <div id="settings-trigger"><i class="mdi mdi-settings"></i></div>
                <div id="theme-settings" class="settings-panel">
                    <i class="settings-close mdi mdi-close"></i>
                    <p class="settings-heading">SIDEBAR SKINS</p>
                    <div class="sidebar-bg-options selected" id="sidebar-light-theme"><div class="img-ss rounded-circle bg-light border mr-3"></div>Light</div>
                    <div class="sidebar-bg-options" id="sidebar-dark-theme"><div class="img-ss rounded-circle bg-dark border mr-3"></div>Dark</div>
                    <p class="settings-heading mt-2">HEADER SKINS</p>
                    <div class="color-tiles mx-0 px-4">
                        <div class="tiles primary"></div>
                        <div class="tiles secondary"></div>
                        <div class="tiles dark"></div>
                        <div class="tiles default"></div>
                    </div>
                </div>
            </div> *@
         
            <!-- partial -->
            <!-- partial:partials/_sidebar.html -->
            <!-- SIDEBAR -->
            <nav class="sidebar sidebar-offcanvas" id="sidebar">
                <ul class="nav" id="dynamic-sidebar">
                    <!-- Menu will be injected here via jQuery -->
                
                </ul>
            </nav>
            <!-- partial -->
            <div class="main-panel">
                <div class="content-wrapper">
                    @RenderBody() <!-- This will render the content of the views -->

                </div>
                <!-- content-wrapper ends -->
                <!-- partial:partials/_footer.html -->
       @*          <footer class="footer">
                    <div class="d-sm-flex justify-content-center justify-content-sm-between">
                        <span class="text-muted text-center text-sm-left d-block d-sm-inline-block">Copyright © 2018 <a href="https://www.bootstrapdash.com/" target="_blank" class="text-muted">Bootstrapdash</a>. All rights reserved.</span>
                        <span class="float-none float-sm-right d-block mt-1 mt-sm-0 text-center">Hand-crafted & made with <i class="mdi mdi-heart-outline text-primary"></i></span>
                    </div>
                </footer> *@
                <!-- partial -->
            </div>
            <!-- main-panel ends -->
        </div>
        <!-- page-body-wrapper ends -->
    </div>
    <!-- Custom Loader Overlay -->
    <div id="loader-overlay" style="display: none; position: fixed; z-index: 9999; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(255, 255, 255, 0.7); text-align: center;">
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);">
            <div class="loader"></div>
            <div class="mt-2">Loading...</div>
        </div>
    </div>
    <!-- container-scroller -->
    <!-- base:js -->

    <script src="~/Content/vendors/js/vendor.bundle.base.js"></script>
    <!-- endinject -->
    <!-- Plugin js for this page-->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.5/jquery.validate.min.js"></script>
    <script src="~/Content/vendors/datatables.net/jquery.dataTables.js"></script>
    <script src="~/Content/vendors/datatables.net-bs4/dataTables.bootstrap4.js"></script>
    <script src="~/Content/vendors/jquery.flot/jquery.flot.js"></script>
    <script src="~/Content/vendors/jquery.flot/jquery.flot.pie.js"></script>
    <script src="~/Content/vendors/jquery.flot/jquery.flot.resize.js"></script>
    <script src="~/Content/vendors/jqvmap/jquery.vmap.min.js"></script>
    <script src="~/Content/vendors/jqvmap/maps/jquery.vmap.world.js"></script>
    <script src="~/Content/vendors/jqvmap/maps/jquery.vmap.usa.js"></script>
    <script src="~/Content/vendors/peity/jquery.peity.min.js"></script>
    <script src="~/Content/js/jquery.flot.dashes.js"></script>
    <script src="~/Content/vendors/dropify/dropify.min.js"></script>
    <script src="~/Content/vendors/typeahead.js/typeahead.bundle.min.js"></script>
    <script src="~/Content/vendors/select2/select2.min.js"></script>
    <script src="~/Content/vendors/sweetalert/sweetalert.min.js"></script>
    <script src="~/Content/vendors/jquery.avgrund/jquery.avgrund.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="~/Content/vendors/tinymce/tinymce.min.js"></script>
    <script src="~/Content/vendors/dragula/dragula.min.js"></script>

    <!-- End plugin js for this page-->
    <!-- inject:js -->

    <script src="~/Content/vendors/jquery-toast-plugin/jquery.toast.min.js"></script>
    <script src="~/Content/js/toastDemo.js"></script>
    <script src="~/Content/js/off-canvas.js"></script>
    <script src="~/Content/js/hoverable-collapse.js"></script>
    <script src="~/Content/js/template.js"></script>
    <script src="~/Content/js/settings.js"></script>
    <script src="~/Content/js/todolist.js"></script>
    <script src="~/Content/js/file-upload.js"></script>
    <script src="~/Content/js/dropify.js"></script>
    <script src="~/Content/js/select2.js"></script>
    <script src="~/Content/js/typeahead.js"></script>
    <script src="~/Content/js/modal-demo.js"></script>
           <script src="~/Content/js/tooltips.js"></script>
  <script src="~/Content/js/popover.js"></script>
    <script src="~/js/constants.js"></script>
    <script src="~/Content/js/data-table.js"></script>
    <script src="~/js/formValidation.js"></script>
    <script src="~/Content/js/editorDemo.js"></script>
    <script src="~/Content/js/dragula.js"></script>
 
    <!-- endinject -->
    <!-- plugin js for this page -->
    <!-- End plugin js for this page -->
    <!-- Custom js for this page-->
    <script src="~/Content/js/dashboard.js"></script>
    <script src="~/js/services/permissionService.js"></script>
    <script src="~/js/permissionManager.js"></script>
    <!-- Enhanced Date Picker -->
    <script src="~/js/enhanced-datepicker.js"></script>
    <!-- End custom js for this page-->
    @RenderSection("Scripts", required: false)
    <script>
                var getPermissions = '@Url.Action("GetPermissions", "Permission")';
        var getRolesForFacility = '@Url.Action("GetRolesForFacility", "Facility")';
                var getUserFacilities = '@Url.Action("GetUserFacilities", "Facility")';
                var logoutRedirectUrl = '@Url.Action("Index", "Login")';
                    const appLayoutBaseUrl = '@Url.Content("~/")'; // ensures base path works on live

        // Handle navbar logout button
        $(document).ready(function() {
            $('#navLogoutBtn').click(function(e) {
                e.preventDefault();

                // Clear browser-side data
                sessionStorage.clear();
                localStorage.clear();

                // Optional: Clear cookies
                document.cookie.split(";").forEach(function(c) {
                    document.cookie = c.trim().split("=")[0] + "=;expires=Thu, 01 Jan 1970 00:00:00 UTC;path=/";
                });

                // Redirect to server-side logout to clear session
                window.location.href = logoutRedirectUrl;
            });

            // Handle settings link click
            $('#settingsLink').click(function(e) {
                e.preventDefault();
                // You can redirect to a settings page or show a modal
                // For now, we'll show a placeholder alert
                alert('Settings functionality will be implemented here.');
                // Uncomment the line below when you have a settings page
                // window.location.href = '@Url.Action("Settings", "User")';
            });

            // Load user profile image if available
            const userInfo = localStorage.getItem('userInfo');
            const defaultAvatar = "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiM2Yzc1N2QiLz4KPHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4PSI4IiB5PSI4Ij4KPHBhdGggZD0iTTEyIDEyQzE0LjIwOTEgMTIgMTYgMTAuMjA5MSAxNiA4QzE2IDUuNzkwODYgMTQuMjA5MSA0IDEyIDRDOS43OTA4NiA0IDggNS43OTA4NiA4IDhDOCAxMC4yMDkxIDkuNzkwODYgMTIgMTIgMTJaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNMTIgMTRDOC4xMzQwMSAxNCA1IDE3LjEzNDAxIDUgMjFIMTlDMTkgMTcuMTM0MDEgMTUuODY2IDE0IDEyIDE0WiIgZmlsbD0id2hpdGUiLz4KPC9zdmc+Cjwvc3ZnPgo=";

            if (userInfo) {
                try {
                    const userData = JSON.parse(userInfo);

                    // Set profile image with fallback
                    if (userData.profileImageUrl && userData.profileImageUrl.trim() !== '') {
                        $('#userProfileImage').attr('src', userData.profileImageUrl).on('error', function() {
                            $(this).attr('src', defaultAvatar);
                        });
                    } else {
                        $('#userProfileImage').attr('src', defaultAvatar);
                    }

                    // Set display name
                    if (userData.firstName && userData.lastName) {
                        $('#userDisplayName').text(userData.firstName + ' ' + userData.lastName);
                    } else if (userData.username) {
                        $('#userDisplayName').text(userData.username);
                    }

                    // Set email
                    if (userData.email) {
                        $('#userEmail').text(userData.email);
                    }
                } catch (e) {
                    console.log('Error parsing user info:', e);
                    $('#userProfileImage').attr('src', defaultAvatar);
                }
            } else {
                // Fallback to session data if localStorage is not available
                $('#userProfileImage').attr('src', defaultAvatar);
            }
        });

    </script>

</body>

</html>

