using HSSE_Domain.Models;
using HSSE_Models_Dto.ModelsDto;
using HSSE_Service.Constant;
using HSSE_Service.InterfaceService;
using HSSE_Service.ServiceResponce;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Json;
using System.Threading.Tasks;

namespace HSSE_Service.DataManager
{
    public class DocumentLibraryService : IDocumentLibraryService
    {
        private readonly HttpClient _httpClient;
        private readonly string _apiBaseUrl;
        private readonly HsseDbLatestContext _context;


        public DocumentLibraryService(IHttpClientFactory httpClientFactory, IConfiguration configuration, HsseDbLatestContext context)
        {
            _httpClient = httpClientFactory.CreateClient("AuthenticatedClient");
            var baseUrl = configuration["ExternalApi:BaseUrl"];
            if (!string.IsNullOrEmpty(baseUrl))
            {
                _httpClient.BaseAddress = new Uri(baseUrl);
            }
            _apiBaseUrl = configuration["ExternalApi:BaseUrl"];
            _context = context ?? throw new ArgumentNullException(nameof(context));
        }

        public async Task<ApiResponseDto<object>> CreateOrUpdateDocumentAsync(MstDocumentLibraryDto dto)
        {
            try
            {
                var apiUrl = $"{_apiBaseUrl}{ApiEndpoints.CreateOrUpdateDocument}";
                var response = await _httpClient.PostAsJsonAsync(apiUrl, dto);
                if (response.IsSuccessStatusCode)
                {
                    var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<object>>();
                    return ApiResponseDto<object>.SuccessResponse(apiResult?.Message ?? "Document saved successfully.", (int)response.StatusCode, apiResult?.Data);
                }
                else
                {
                    return ApiResponseDto<object>.ErrorResponse("Failed to save document.", (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse("Failed to save document.", 500, new List<HSSE_Service.ServiceResponce.Error> {
                    new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
                });
            }
        }

        public async Task<ApiResponseDto<List<MstDocumentLibraryDto>>> GetDocumentsAsync()
        {
            try
            {
                var apiUrl = $"{_apiBaseUrl}{ApiEndpoints.GetDocuments}";
                var response = await _httpClient.GetAsync(apiUrl);
                if (response.IsSuccessStatusCode)
                {
                    var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<List<MstDocumentLibraryDto>>>();
                    return ApiResponseDto<List<MstDocumentLibraryDto>>.SuccessResponse(apiResult?.Message ?? "Documents fetched successfully.", (int)response.StatusCode, apiResult?.Data ?? new List<MstDocumentLibraryDto>());
                }
                else
                {
                    return ApiResponseDto<List<MstDocumentLibraryDto>>.ErrorResponse("Failed to fetch documents.", (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<List<MstDocumentLibraryDto>>.ErrorResponse("Failed to fetch documents.", 500, new List<HSSE_Service.ServiceResponce.Error> {
                    new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
                });
            }
        }

        public async Task<ApiResponseDto<List<MstDocumentLibraryDto>>> GetDocumentsByUserIdAsync(int userId)
        {
            try
            {
                var apiUrl = $"{_apiBaseUrl}{ApiEndpoints.GetDocumentsByUserId}{userId}";
                var response = await _httpClient.GetAsync(apiUrl);
                if (response.IsSuccessStatusCode)
                {
                    var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<List<MstDocumentLibraryDto>>>();
                    return ApiResponseDto<List<MstDocumentLibraryDto>>.SuccessResponse(apiResult?.Message ?? "Documents fetched successfully.", (int)response.StatusCode, apiResult?.Data ?? new List<MstDocumentLibraryDto>());
                }
                else
                {
                    return ApiResponseDto<List<MstDocumentLibraryDto>>.ErrorResponse("Failed to fetch documents.", (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<List<MstDocumentLibraryDto>>.ErrorResponse("Failed to fetch documents.", 500, new List<HSSE_Service.ServiceResponce.Error> {
                    new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
                });
            }
        }

        public async Task<ApiResponseDto<MstDocumentLibraryDto>> GetDocumentByIdAsync(int id)
        {
            try
            {
                var apiUrl = $"{_apiBaseUrl}{ApiEndpoints.GetDocumentById}{id}";
                var response = await _httpClient.GetAsync(apiUrl);
                if (response.IsSuccessStatusCode)
                {
                    var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<MstDocumentLibraryDto>>();
                    return ApiResponseDto<MstDocumentLibraryDto>.SuccessResponse(apiResult?.Message ?? "Document fetched successfully.", (int)response.StatusCode, apiResult?.Data);
                }
                else
                {
                    return ApiResponseDto<MstDocumentLibraryDto>.ErrorResponse("Failed to fetch document.", (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<MstDocumentLibraryDto>.ErrorResponse("Failed to fetch document.", 500, new List<HSSE_Service.ServiceResponce.Error> {
                    new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
                });
            }
        }

        public async Task<ApiResponseDto<object>> DeleteDocumentAsync(int documentId, int deletedBy)
        {
            try
            {
                var apiUrl = $"{_apiBaseUrl}/api/DocumentLibrary/DeleteDocument?documentId={documentId}&deletedBy={deletedBy}";
                var response = await _httpClient.DeleteAsync(apiUrl);
                if (response.IsSuccessStatusCode)
                {
                    var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<object>>();
                    return ApiResponseDto<object>.SuccessResponse(apiResult?.Message ?? "Post deleted successfully.", (int)response.StatusCode, apiResult?.Data);
                }
                else
                {
                    return ApiResponseDto<object>.ErrorResponse("Failed to delete post.", (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse("Failed to delete post.", 500, new System.Collections.Generic.List<HSSE_Service.ServiceResponce.Error> {
                    new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
                });
            }
        }
        public async Task<ApiResponseDto<List<MstDocumentLibraryDto>>> GetDocumentsLibraryAsync()
        {
            try
            {
                var entities = await _context.MstDocumentLibraries
                    .Where(x => !x.IsDeleted)
                    .ToListAsync();

                // Step 1: Convert to a dictionary of DTOs
                var allDtos = entities.Select(e => new MstDocumentLibraryDto
                {
                    DocumentId = e.DocumentId,
                    Title = e.Title,
                    Category = e.Category,
                    Version = e.Version,
                    DocumentUrl = e.DocumentUrl,
                    Date = e.Date
                }).ToList();

                return ApiResponseDto<List<MstDocumentLibraryDto>>.SuccessResponse("Documents retrieved successfully", StatusCodes.Status200OK, allDtos);
            }
            catch (Exception ex)
            {
                return ApiResponseDto<List<MstDocumentLibraryDto>>.ErrorResponse($"Error: {ex.Message}", StatusCodes.Status500InternalServerError);
            }
        }
    }
} 