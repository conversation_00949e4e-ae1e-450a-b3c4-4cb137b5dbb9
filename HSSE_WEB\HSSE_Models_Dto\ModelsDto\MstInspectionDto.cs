using System;
using System.Collections.Generic;

namespace HSSE_Models_Dto.ModelsDto
{
    public class MstInspectionDto
    {
        public int InspectionId { get; set; }
        public int? FacilityId { get; set; }
        public string Title { get; set; } = null!;
        public string? Description { get; set; }
        public DateTime? InspectionDate { get; set; }
        public string? AfterImagePath { get; set; }
        public string? ReferenceNo { get; set; }
        public int? TypeOfInspection { get; set; }
        public string? TypeOfInspectionName { get; set; }

        public int? ActionPartyId { get; set; }
        public int? DepartmentId { get; set; }
        public string? SpecificLocation { get; set; }
        public string? InspectorName { get; set; }
        public int? Verification { get; set; }
      public string?  Rectification { get; set; }
        public int? CreatedBy { get; set; }
        public DateTime? CreatedAt { get; set; }
        public DateTime? ModifiedAt { get; set; }
        public DateTime? CompletionDateTime { get; set; }
        public string? Observation { get; set; }
        public string? Recommendation { get; set; }
        public int? Status { get; set; }

        public string? ObservationAttachmentBase64 { get; set; }
        public string? ObservationAttachmentName { get; set; }
        public int? ContactPersonId { get; set; }
        public string? ContactPersonName { get; set; }
        public string? RecommendationAttachmentBase64 { get; set; }
        public string? RecommendationAttachmentName { get; set; }
        public string? FacilityName { get; set; }
       public string?  InspectionLocation { get; set; }

    }

    public class MstInspectionItemDto
    {
        public int ItemId { get; set; }

        public string? Observation { get; set; }
        public string? Recommendation { get; set; }
        public int? Status { get; set; }

        public string? ObservationFileBase64 { get; set; }
        public string? ObservationFileName { get; set; }

        public string? RecommendationFileBase64 { get; set; }
        public string? RecommendationFileName { get; set; }
    }

    public class MstInspectionItemAuditDto
    {
        public int AuditId { get; set; }
        public int ItemId { get; set; }
        public string? OldStatus { get; set; }
        public string? NewStatus { get; set; }
        public string? OldRectification { get; set; }
        public string? NewRectification { get; set; }
        public string? OldAfterImagePath { get; set; }
        public string? NewAfterImagePath { get; set; }
        public int? ChangedBy { get; set; }
        public DateTime? ChangedAt { get; set; }
    }
    public class UpdateInspectionStatusDto
    {
        public int InspectionId { get; set; }
        public int Status { get; set; }
        public DateTime? CompletionDate { get; set; }
        public string? Remarks { get; set; }
        public bool? isVerified { get; set; }

        public string? AfterImageUrl { get; set; }
        public string? AfterImageName { get; set; }
        public int? ChangedBy { get; set; }
    }

    public partial class MstNewInspectionDto
    {
        public int InspectionId { get; set; }
        public int? FacilityId { get; set; }
        public string? Title { get; set; }
        public string? Description { get; set; }
        public string? Location { get; set; }
        public DateTime InspectionDate { get; set; }
        public string? ReferenceNo { get; set; }
        public int? CreatedBy { get; set; }
        public DateTime? CreatedAt { get; set; }
        public DateTime? ModifiedAt { get; set; }
        public string? TypeOfInspectionName { get; set; }
        public int? TypeOfInspection { get; set; }
        public string? InspectorName { get; set; }
      public string?  InspectionLocation { get; set; }
        public virtual ICollection<MstNewInspectionItemDto> MstInspectionItems { get; set; } = new List<MstNewInspectionItemDto>();
    }
    public partial class MstNewInspectionItemDto
    {
        public int? ItemId { get; set; }
        public int InspectionId { get; set; }

        public string? Observation { get; set; }
        public string? Recommendation { get; set; }
        public string? ObservationMediaUrl { get; set; }
        public string? RecommendationMediaUrl { get; set; }
        public string? ObservationMediaName { get; set; }
        public string? RecommendationMediaName { get; set; }
        public string? Location { get; set; }                // NEW
        public int? ObservationType { get; set; }            // NEW (1 = Positive, 2 = For Action)
        public int? ContactPersonId { get; set; }
        public string? ActionPartyId { get; set; }

        public string? SpecificLocation { get; set; }
        public int? Status { get; set; }
        public string? Rectification { get; set; }
        public string? AfterImagePath { get; set; }

        public DateTime? CompletionDateTime { get; set; }
        public DateTime? CreatedAt { get; set; }
        public DateTime? ModifiedAt { get; set; }
        public string? ActionPartyNames { get; set; } // For display purposes
        public int? Verification { get; set; }
        public int? TypeOfInspection { get; set; }
    }
} 