@{
    ViewData["Title"] = "Create Announcement Category";
}
<div class="col-12 grid-margin">
    <div class="card">
        <div class="card-body">
            <h4 class="card-title">Create Announcement Category</h4>
            <form id="announcementCategoryForm" class="form-sample">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group row">
                            <label for="AnnoucementCategoryName" class="col-sm-4 col-form-label">Category Name <span class="text-danger">*</span></label>
                            <div class="col-sm-8">
                                <input type="text" class="form-control" id="AnnoucementCategoryName" name="AnnoucementCategoryName" required />
                            </div>
                        </div>
                   
                            <div class="form-group row">
                                <label class="col-sm-4 col-form-label">Status</label>
                                <div class="form-check">
                                    <label class="form-check-label">
                                        <input type="checkbox" class="form-check-input" name="Status" id="Status" checked/>
                                    </label>
                                </div>
                        
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-12 text-end">
                        <button type="submit" class="btn btn-primary">Save Category</button>
                        <button type="reset" class="btn btn-light">Cancel</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
<!-- Table for future listing -->
<div class="card mt-4">
    <div class="card-body">
        <h4 class="card-title">Categories List</h4>
        <div class="table-responsive">
            <table id="announcement-category-listing" class="table">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>Name</th>
                        <th>Status</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
        </div>
    </div>
</div>
<!-- Edit Category Modal -->
<div class="modal fade" id="editCategoryModal" tabindex="-1" aria-labelledby="editCategoryModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editCategoryModalLabel">Edit Category</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="editCategoryForm">
                    <input type="hidden" id="EditCategoryId" />
                    <div class="mb-3">
                        <label for="EditCategoryName" class="form-label">Category Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="EditCategoryName" required />
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" form="editCategoryForm" class="btn btn-primary">Save Changes</button>
            </div>
        </div>
    </div>
</div>
<script>
    var getCategoriesByUserId = '@Url.Action("GetAnnouncementCategoriesByUserId", "Announcement")';
    var insertOrUpdateCategory = '@Url.Action("InsertOrUpdateAnnouncementCategory", "Announcement")';
    var userId = '@ViewBag.UserId';
    var getCategoryById = '@Url.Action("GetAnnouncementCategoryById", "Announcement")';
    var toggleCategoryStatus = '@Url.Action("ToggleAnnouncementCategoryStatus", "Announcement")';
</script>
@section Scripts {
    <script src="~/js/Announcement/announcementCategory.js"></script>
} 