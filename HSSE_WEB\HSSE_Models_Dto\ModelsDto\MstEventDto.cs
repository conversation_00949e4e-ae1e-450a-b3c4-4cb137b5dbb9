using System;

namespace HSSE_Models_Dto.ModelsDto
{
    public class MstEventDto
    {
        public int EventId { get; set; }
        public string Title { get; set; } = null!;
        public string? Description { get; set; }
        public string? MediaUrl { get; set; }
        public DateTime? EventDateTime { get; set; }
        public string? Location { get; set; }
        public string? ExternalLink { get; set; }
        public DateTime? CreatedAt { get; set; }
        public int? CreatedBy { get; set; }
        public int? FacilityId { get; set; }
        public int? LikeCount { get; set; }
        public string? FileName { get; set; } // Needed to save file with original name
        public bool? IsLikedByUser { get; set; }
        public bool? IsAcceptedByUser { get; set; }
        public bool IsActive { get; set; }
        public DateTime? ScheduleAt { get; set; }

        public DateTime? ExpiryAt { get; set; }
        public List<int>? GroupId { get; set; }
        public bool IsRsvp { get; set; }
        public int? ModifiedBy { get; set; }
        public int? RsvpAcceptedCount { get; set; }
        public string? Attachment { get; set; }

        public DateTime? ModifiedAt { get; set; }
        public List<MstAnnouncementReceiverDto>? MstAnnouncementReceivers { get; set; }
        public List<MstEventResponseDto>? MstEventResponses { get; set; }
    }

    public class EventResponseDto
    {
        public int ResponseId { get; set; }
        public int EventId { get; set; }
        public int UserId { get; set; }
        public bool IsAccepted { get; set; }
        public DateTime RespondedAt { get; set; }
    }

    public class MstAnnouncementReceiverDto
    {
        public int ReceiverId { get; set; }
        public int? GroupId { get; set; }
        public int? UserId { get; set; }
        public int? EventId { get; set; }
    }

    public class MstEventResponseDto
    {
        public int ResponseId { get; set; }
        public int EventId { get; set; }
        public int UserId { get; set; }
        public bool IsAccepted { get; set; }
        public DateTime RespondedAt { get; set; }
    }
} 