@{
    ViewData["Title"] = "View Newsletters";
}
<div class="card mt-4">
    <div class="card-body">
        <div class="d-flex justify-content-between align-items-center">
        <h4 class="card-title">Today's News letters</h4>
        <div class="form-inline">
            <button id="btn-toggle-view" class="btn btn-primary btn-sm p-2">
                    <i class="fa fa-table"></i> Table View
                </button>
        </div>
        </div>
        <div class="row py-4" id="newsletter-card-container"></div>
    </div>
</div>

<script>
    var getNewslettersByFacilityId = '@Url.Action("GetNewslettersByFacilityId", "Newsletter")';
        const basePath = '@Url.Content("~/")'; // returns '/UEMS/HSSEWeb/' in production
                                                        var defaultIconUrl = '@Url.Content("~/Content/images/Image_not_available.jpg")';


</script>
@section Scripts {
    <script src="~/js/Newsletter/viewNewsletter.js"></script>
} 