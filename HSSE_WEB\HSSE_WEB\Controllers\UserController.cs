﻿using Azure;
using HSSE_Domain.Models;
using HSSE_Models_Dto.ModelsDto;
using HSSE_Models_Dto.ViewModels;
using HSSE_Service.InterfaceService;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;

namespace HSSE_WEB.Controllers
{
    public class UserController : Controller
    {
        private readonly IUserService _userService;
        private readonly IFacilityService _facilityService;
        private readonly IUserRoleService _userRoleService;

        private IConfiguration _configuration;
        public UserController(IConfiguration configuration, IUserService userService, IFacilityService facilityService, IUserRoleService userRoleService)
        {
            _userService = userService;
            _facilityService = facilityService;
            _userRoleService = userRoleService;
            _configuration = configuration;
        }
        public async Task<IActionResult> AddUser()
        {
            var allRoles = await _userRoleService.GetAllUserRoleAsync();
            var viewModel = new AddUserViewModel
            {
                Facilities = await _facilityService.GetAllFacilityAsync(),
                Roles = allRoles.Where(r => r.IsActive).ToList(), // 👈 filter active here
                ExistingUsers = await _userService.GetAllUserAsync(),
                Languages = await _userService.GetAllLanguagesAsync(),
            };

            return View(viewModel);
        }
        public async Task<IActionResult> EditProfile()
        {
            var userId = int.Parse(HttpContext.Session.GetString("UserId")); // Safe to access here

            var viewModel = new AddUserViewModel
            {
                userDetails = await _userService.GetUserByIdApiAsync(userId),
            };
            string wwwrootPath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot");

            // Check if ProfileImageUrl is not null or empty
            if (!string.IsNullOrEmpty(viewModel.userDetails.ProfileImageUrl))
            {
                // Replace the root path with relative path and ensure correct path format
                string relativePath = viewModel.userDetails.ProfileImageUrl.Replace(wwwrootPath, "").Replace("\\", "/");
                viewModel.userDetails.ProfileImageUrl = "/" + relativePath.TrimStart('/');
            }
            return View(viewModel);
        }
        [HttpGet]
        public async Task<IActionResult> GetUserById(int id)
        {
            // Call the service to fetch user details.
            var user = await _userService.GetUserByIdApiAsync(id);

            // Check if user is found, if not return NotFound
            if (user == null)
            {
                return NotFound(new { message = "User not found" });
            }

            // Path to the wwwroot folder for resolving image URLs
            string wwwrootPath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot");

            // Check if ProfileImageUrl is not null or empty
            if (!string.IsNullOrEmpty(user.ProfileImageUrl))
            {
                // Replace the root path with relative path and ensure correct path format
                string relativePath = user.ProfileImageUrl.Replace(wwwrootPath, "").Replace("\\", "/");
                user.ProfileImageUrl = "/" + relativePath.TrimStart('/');
            }

            // Return the user data as a JSON response
            return Json(user);
        }

        [HttpPost]
        public async Task<IActionResult> SaveUserWithRoles([FromForm] IFormFile profileImage, [FromForm] string userJson)
        {
            try
            {
                var dto = JsonConvert.DeserializeObject<MstUserDto>(userJson);
                if (dto == null)
                    return BadRequest("Invalid user data.");
                var userId = int.Parse(HttpContext.Session.GetString("UserId")); // Safe to access here
                dto.CreatedBy = userId; // Set CreatedBy from session

                var result = await _userService.UpdateUserDetailsAsync(dto, profileImage);

                if (result.Success)
                    return Ok(new { success = true, message = result.Message });
                else
                    return StatusCode(500, new { success = false, message = result.Message });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { success = false, message = ex.Message });
            }
        }
        public async Task<IActionResult> CreateUser([FromForm] MstUserDto model, IFormFile ProfileImage)
        {
            if (model == null)
            {
                return BadRequest("Invalid user data.");
            }
            var userId = int.Parse(HttpContext.Session.GetString("UserId")); // Safe to access here
            model.CreatedBy = userId; // Set CreatedBy from session
            var result = await _userService.CreateUserAsync(model, ProfileImage);

            return Json(result);
        }
        [HttpPost]
        public async Task<IActionResult> EditProfile(string Password, IFormFile ProfileImage, string? ExistingProfileImageUrl, string EmployeeCode, string ContactNumber)
        {
            var userId = int.Parse(HttpContext.Session.GetString("UserId")); // Safe to access here

            var result = await _userService.UpdateUserProfileAsync(Password, ProfileImage, userId, ExistingProfileImageUrl, EmployeeCode, ContactNumber);

            if (result.Success)
            {
                TempData["SuccessMessage"] = result.Message;
            }
            else
            {
                TempData["ErrorMessage"] = result.Message;
            }

            return RedirectToAction("EditProfile");
        }
        public async Task<IActionResult> AssignRole()
        {
            var viewModel = new AddUserViewModel
            {
                Facilities = await _facilityService.GetAllFacilityAsync(),
                Roles = await _userRoleService.GetAllUserRoleAsync(),
                ExistingUsers = await _userService.GetAllUserAsync(),
                UserRoleMappings = await _userRoleService.GetAllUserRoleMappingAsync()
            };

            return View(viewModel);
        }
        [HttpPost]
        public async Task<IActionResult> SaveUserRoleMapping([FromBody] MstUserRolesConfigDto dto)
        {
            try
            {
                if (dto == null)
                    return BadRequest("Invalid user data.");

                var result = await _userRoleService.SaveUserRoleMappingAsync(dto);
                    return StatusCode(result.Status, result);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { success = false, message = ex.Message });
            }
        }
        public async Task<IActionResult> ManageRole()
        {
            return View();
        }
        [HttpGet]
        public async Task<IActionResult> GetAllRoles()
        {
            var roles = await _userRoleService.GetAllUserRoleAsync();
            return Json(roles);
        }

        [HttpGet]
        public async Task<IActionResult> GetAllRolesMappingDetails()
        {
            var roles = await _userRoleService.GetAllUserRoleMappingAsync();
            return Json(roles);
        }
        [HttpPost]
        public async Task<IActionResult> ToggleRoleActivation(int roleId)
        {
            var result = await _userRoleService.ToggleRoleActivationAsync(roleId);
            return Json(result);
        }

        [HttpGet]
        public async Task<IActionResult> GetAllUserRoleById(int roleId)
        {
            var result = await _userRoleService.GetAllUserRoleByIdAsync(roleId);
            return Json(result);
        }
        [HttpGet]
        public async Task<IActionResult> GetAllUserRoleMappingById(int userConfigId)
        {
            var result = await _userRoleService.GetAllUserRoleMappingByIdAsync(userConfigId);
            return Json(result);
        }
        
        public async Task<IActionResult> CreateRole([FromForm] MstUsersRoleDto model)
        {
            var userId = HttpContext.Session.GetString("UserId"); // Safe to access here
            model.CreatedBy = int.Parse(userId);
            var result = await _userRoleService.SaveRoleAsync(model);
            return Json(result);
        }
        [HttpDelete]
        public async Task<IActionResult> DeleteUserRoleMapping(int id)
        {
            var response = await _userRoleService.DeleteUserRoleMappingAsync(id);
            return StatusCode(response.Status, response);
        }
        [HttpGet]
        public async Task<IActionResult> GetUsersByFacilityId(int facilityId)
        {
            var users = await _userService.GetUsersByFacilityId(facilityId);
            return Json(users);
        }
    }
}
