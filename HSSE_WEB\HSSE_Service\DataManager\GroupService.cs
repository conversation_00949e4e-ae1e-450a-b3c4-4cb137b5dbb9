using HSSE_Models_Dto.ModelsDto;
using HSSE_Service.Constant;
using HSSE_Service.InterfaceService;
using HSSE_Service.ServiceResponce;
using Microsoft.Extensions.Configuration;
using System;
using System.Net.Http;
using System.Net.Http.Json;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Text.Json;
using System.Net.Http.Headers;
using System.Text;
using Newtonsoft.Json;

namespace HSSE_Service.DataManager
{
    public class GroupService : IGroupService
    {
        private readonly HttpClient _httpClient;
        private readonly string _apiBaseUrl;

        public GroupService(IHttpClientFactory httpClientFactory, IConfiguration configuration)
        {
            _httpClient = httpClientFactory.CreateClient("AuthenticatedClient");
            var baseUrl = configuration["ExternalApi:BaseUrl"];
            _apiBaseUrl =  configuration["ExternalApi:BaseUrl"];

        }
        public async Task<ApiResponseDto<object>> CreateGroupAsync(GroupCreateDto dto, int createdBy)
        {
            try
            {
                // Construct the full API URL
                var apiUrl = $"{_apiBaseUrl}{ApiEndpoints.InsertOrUpdateGroup}";

                // Create the payload
                var payload = new
                {
                    groupId = dto.GroupId,
                    groupName = dto.GroupName,
                    description = dto.Description,
                    createdBy = createdBy,
                    createdAt = DateTime.UtcNow,
                    modifiedBy = 0,
                    modifiedAt = DateTime.UtcNow
                };

                // Create the HTTP request
                var request = new HttpRequestMessage(HttpMethod.Post, apiUrl)
                {
                    Content = new StringContent(JsonConvert.SerializeObject(payload), Encoding.UTF8, "application/json")
                };

                // Add Bearer token if provided
                //if (!string.IsNullOrEmpty(authToken))
                //{
                //    request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", authToken);
                //}

                // Send the request
                var response = await _httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    var msg = ApiMessages.GroupSaveSuccess;
                    // Try to read message from API response if available
                    try
                    {
                        var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<object>>();
                        if (apiResult != null && !string.IsNullOrEmpty(apiResult.Message))
                            msg = apiResult.Message;
                    }
                    catch { }
                    return ApiResponseDto<object>.SuccessResponse(msg, (int)response.StatusCode);
                }
                else
                {
                    var errorMsg = ApiMessages.GroupSaveFailed;
                    try
                    {
                        var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<object>>();
                        if (apiResult != null && !string.IsNullOrEmpty(apiResult.Message))
                            errorMsg = apiResult.Message;
                    }
                    catch { }
                    return ApiResponseDto<object>.ErrorResponse(errorMsg, (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse(ApiMessages.GroupSaveFailed, 500, new System.Collections.Generic.List<HSSE_Service.ServiceResponce.Error> {
                    new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
                });
            }
        }
        public async Task<ApiResponseDto<List<GroupDto>>> GetAllGroupsAsync()
        {
            try
            {
                // Construct the full API URL
                var apiUrl = $"{_apiBaseUrl}{ApiEndpoints.GetGroups}";

                // Create the HTTP request
                var request = new HttpRequestMessage(HttpMethod.Get, apiUrl);

                // Optional: Add Bearer token if provided
                //if (!string.IsNullOrEmpty(authToken))
                //{
                //    request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", authToken);
                //}

                // Send the request
                var response = await _httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    var rawJson = await response.Content.ReadAsStringAsync();

                    var groups = await response.Content.ReadFromJsonAsync<ApiResponseDto<List<GroupDto>>>();
                    return ApiResponseDto<List<GroupDto>>.SuccessResponse(ApiMessages.GroupSaveSuccess, (int)response.StatusCode, groups.Data ?? new List<GroupDto>());
                }
                else
                {
                    return ApiResponseDto<List<GroupDto>>.ErrorResponse(ApiMessages.GroupSaveFailed, (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<List<GroupDto>>.ErrorResponse(ApiMessages.GroupSaveFailed, 500, new List<HSSE_Service.ServiceResponce.Error> {
                    new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
                });
            }
        }
        public async Task<ApiResponseDto<GroupDto>> GetGroupByIdAsync(int groupId)
        {
            try
            {
                // Construct the full API URL
                var apiUrl = $"{_apiBaseUrl}{ApiEndpoints.GetGroupById}{groupId}";

                // Create the HTTP request
                var request = new HttpRequestMessage(HttpMethod.Get, apiUrl);

                // Optional: Add Bearer token if provided
                //if (!string.IsNullOrEmpty(authToken))
                //{
                //    request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", authToken);
                //}

                // Send the request
                var response = await _httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<GroupDto>>();
                    return ApiResponseDto<GroupDto>.SuccessResponse(apiResult?.Message ?? "Success", (int)response.StatusCode, apiResult?.Data);
                }
                else
                {
                    return ApiResponseDto<GroupDto>.ErrorResponse("Failed to fetch group details.", (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<GroupDto>.ErrorResponse("Failed to fetch group details.", 500, new List<HSSE_Service.ServiceResponce.Error> {
                    new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
                });
            }
        }
        public async Task<ApiResponseDto<object>> DeleteGroupAsync(int id)
        {
            try
            {
                // Build the full API URL
                var apiUrl = $"{_apiBaseUrl}{ApiEndpoints.DeleteGroup}{id}";

                // Create HTTP DELETE request
                var request = new HttpRequestMessage(HttpMethod.Delete, apiUrl);

                //// Optional: Add Bearer token if available
                //if (!string.IsNullOrEmpty(authToken))
                //{
                //    request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", authToken);
                //}

                // Send the request
                var response = await _httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<object>>();
                    return ApiResponseDto<object>.SuccessResponse(apiResult?.Message ?? "Group deleted successfully.", (int)response.StatusCode);
                }
                else
                {
                    return ApiResponseDto<object>.ErrorResponse("Failed to delete group.", (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse("Failed to delete group.", 500, new List<HSSE_Service.ServiceResponce.Error> {
                    new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
                });
            }
        }
        public async Task<ApiResponseDto<object>> InsertGroupMemberAsync(int groupId, List<int> userIds, int OldGroupId)
        {
            try
            {
                // Construct the full URL
                var apiUrl = $"{_apiBaseUrl}{ApiEndpoints.InsertGroupMember}";

                // Create the payload object
                var payload = new
                {
                    GroupId = groupId,
                    UserIds = userIds,
                    OldGroupId = OldGroupId
                };

                // Serialize the payload to JSON
                var jsonPayload = JsonConvert.SerializeObject(payload);
                var content = new StringContent(jsonPayload, Encoding.UTF8, "application/json");

                // Build the HTTP POST request
                var request = new HttpRequestMessage(HttpMethod.Post, apiUrl)
                {
                    Content = content
                };

                //// Add Bearer token if available
                //if (!string.IsNullOrEmpty(authToken))
                //{
                //    request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", authToken);
                //}

                // Send the request
                var response = await _httpClient.SendAsync(request);
                var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<object>>();

                if (response.IsSuccessStatusCode)
                {
                    return ApiResponseDto<object>.SuccessResponse(apiResult?.Message ?? "Group members added successfully.", (int)response.StatusCode);
                }
                else
                {
                    return ApiResponseDto<object>.ErrorResponse(apiResult?.Message, (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse("Failed to add group members.", 500, new List<HSSE_Service.ServiceResponce.Error> {
                    new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
                });
            }
        }
        public async Task<ApiResponseDto<object>> UpdateGroupMemberAsync(int groupId, List<int> userIds, int OldGroupId)
        {
            try
            {
                // Construct the full URL
                var apiUrl = $"{_apiBaseUrl}{ApiEndpoints.UpdateGroupMember}";

                // Prepare the payload
                var payload = new
                {
                    GroupId = groupId,
                    UserIds = userIds,
                    OldGroupId = OldGroupId
                };

                // Serialize payload to JSON
                var jsonPayload = JsonConvert.SerializeObject(payload);
                var content = new StringContent(jsonPayload, Encoding.UTF8, "application/json");

                // Create HTTP request
                var request = new HttpRequestMessage(HttpMethod.Post, apiUrl)
                {
                    Content = content
                };

                // Add Bearer token if available
                //if (!string.IsNullOrEmpty(authToken))
                //{
                //    request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", authToken);
                //}

                // Send the request
                var response = await _httpClient.SendAsync(request);

                // Read and process the response
                var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<object>>();
                if (response.IsSuccessStatusCode)
                {
                    return ApiResponseDto<object>.SuccessResponse(apiResult?.Message ?? "Group members added successfully.", (int)response.StatusCode);
                }
                else
                {
                    return ApiResponseDto<object>.ErrorResponse(apiResult?.Message, (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse("Failed to add group members.", 500, new List<HSSE_Service.ServiceResponce.Error> {
                    new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
                });
            }
        }
        public async Task<ApiResponseDto<List<GroupMemberListDto>>> GetGroupMembersAsync()
        {
            try
            {
                // Construct the full API URL
                var apiUrl = $"{_apiBaseUrl}{ApiEndpoints.GetGroupMembers}";

                // Create the HTTP request
                var request = new HttpRequestMessage(HttpMethod.Get, apiUrl);

                // Add the Bearer token if available
                //if (!string.IsNullOrEmpty(authToken))
                //{
                //    request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", authToken);
                //}

                // Send the request
                var response = await _httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<List<GroupMemberListDto>>>();
                    return ApiResponseDto<List<GroupMemberListDto>>.SuccessResponse(apiResult?.Message ?? "Success", (int)response.StatusCode, apiResult?.Data);
                }
                else
                {
                    return ApiResponseDto<List<GroupMemberListDto>>.ErrorResponse("Failed to fetch group members.", (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<List<GroupMemberListDto>>.ErrorResponse("Failed to fetch group members.", 500, new List<HSSE_Service.ServiceResponce.Error> {
                    new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
                });
            }
        }
        public async Task<ApiResponseDto<GroupMemberListDto>> GetGroupMembersByGroupIdAsync(int groupId)
        {
            try
            {
                // Construct the full API URL
                var apiUrl = $"{_apiBaseUrl}{ApiEndpoints.GetGroupMembersByGroupId}{groupId}";

                // Create the HTTP request
                var request = new HttpRequestMessage(HttpMethod.Get, apiUrl);

                // Add Bearer token if provided
                //if (!string.IsNullOrEmpty(authToken))
                //{
                //    request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", authToken);
                //}

                // Send the request
                var response = await _httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<GroupMemberListDto>>();
                    return ApiResponseDto<GroupMemberListDto>.SuccessResponse(apiResult?.Message ?? "Success", (int)response.StatusCode, apiResult?.Data);
                }
                else
                {
                    return ApiResponseDto<GroupMemberListDto>.ErrorResponse("Failed to fetch group member details.", (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<GroupMemberListDto>.ErrorResponse("Failed to fetch group member details.", 500, new List<HSSE_Service.ServiceResponce.Error> {
                    new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
                });
            }
        }
        public async Task<ApiResponseDto<object>> DeleteAllGroupMembersByGroupIdAsync(int groupId)
        {
            try
            {
                var apiUrl = $"{_apiBaseUrl}{ApiEndpoints.DeleteAllGroupMembersByGroupId}{groupId}";

                // Create the HTTP request
                var request = new HttpRequestMessage(HttpMethod.Delete, apiUrl);

                // Add Bearer token if provided
                //if (!string.IsNullOrEmpty(authToken))
                //{
                //    request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", authToken);
                //}

                // Send the request
                var response = await _httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<object>>();
                    return ApiResponseDto<object>.SuccessResponse(apiResult?.Message ?? "Group members deleted successfully.", (int)response.StatusCode);
                }
                else
                {
                    return ApiResponseDto<object>.ErrorResponse("Failed to delete group members.", (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse("Failed to delete group members.", 500, new List<HSSE_Service.ServiceResponce.Error> {
                    new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
                });
            }
        }
    }
} 