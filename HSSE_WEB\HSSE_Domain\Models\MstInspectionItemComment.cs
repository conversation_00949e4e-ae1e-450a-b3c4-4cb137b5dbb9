﻿using System;
using System.Collections.Generic;

namespace HSSE_Domain.Models;

public partial class MstInspectionItemComment
{
    public int InspectionCommentId { get; set; }

    public int ItemId { get; set; }

    public string? CommentText { get; set; }

    public int? CommentedBy { get; set; }

    public DateTime? CommentedAt { get; set; }

    public virtual MstInspectionItem Item { get; set; } = null!;
}
