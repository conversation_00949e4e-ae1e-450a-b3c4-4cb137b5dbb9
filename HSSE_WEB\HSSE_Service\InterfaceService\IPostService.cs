using HSSE_Domain.Models;
using HSSE_Model_Dto.ModelDto;
using HSSE_ModelDto.ModelDto;
using HSSE_Models_Dto.ModelsDto;
using HSSE_Service.Constant;
using HSSE_Service.ServiceResponce;
using System.Threading.Tasks;

namespace HSSE_Service.InterfaceService
{
    public interface IPostService
    {
        Task<ApiResponseDto<object>> CreatePostAsync(MstPostDto dto);
        Task<ApiResponseDto<object>> InsertOrUpdatePostCategoryAsync(MstPostCategoryDto dto);
        Task<ApiResponseDto<List<MstPostCategoryDto>>> GetPostCategoriesAsync();
        Task<ApiResponseDto<MstPostCategoryDto>> GetPostCategoryByIdAsync(int catId);
        Task<ApiResponseDto<object>> DeletePostCategoryAsync(int catId);
        Task<ApiResponseDto<List<MstFacilityDto>>> GetUserFacilityAsync(int userId);
        Task<ApiResponseDto<List<MstPostDto>>> GetAllPostsAsync();
        Task<ApiResponseDto<MstPostDto>> GetPostByIdAsync(int postId);
        Task<ApiResponseDto<object>> UpdatePostAsync(MstPostDto dto);
        Task<ApiResponseDto<PaginatedResponseDto<MstPostDto>>> GetPostsByUserIdAsync(
                    int? userId,
                    bool isAdmin,
                    bool isMyPosts,
                    int pageNumber = 1,
                    int pageSize = 10);
        Task<ApiResponseDto<object>> ToggleLikeAsync(MstLikesConfigDto dto);
        Task<ApiResponseDto<List<MstPostCommentDto>>> GetPostCommentAsync(int postId);
        Task<ApiResponseDto<object>> PostCommentAsync(MstPostCommentDto dto);
        Task<ApiResponseDto<object>> AssignPostAsync(MstFollowupPostDto dto);
        Task<ApiResponseDto<List<MstUserDto>>> GetUserByFacilityIdAsync(int facilityId);
        Task<ApiResponseDto<List<MstPostDto>>> GetAssignedPostsAsync(int? userId, int statusId);
        Task<ApiResponseDto<object>> UpdateFollowUpStatusAsync(FollowupStatusUpdateRequestDto dto);
        Task<ApiResponseDto<object>> DeletePostAsync(int postId, int deletedBy);
        Task<ApiResponseDto<List<MstUserDto>>> GetUserByFacilityIdAndUserIdAsync(List<int> facilityIds, int? userId = null);
    }
} 