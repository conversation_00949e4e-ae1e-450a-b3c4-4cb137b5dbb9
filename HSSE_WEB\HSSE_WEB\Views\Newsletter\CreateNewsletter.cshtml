@model HSSE_Models_Dto.ViewModels.AddUserViewModel

@{
    ViewData["Title"] = "Create Newsletter";
}
<div class="col-12 grid-margin">
    <div class="card">
        <div class="card-body">
            <h4 class="card-title">Create Newsletter</h4>
            <form id="newsletterForm" class="form-sample" enctype="multipart/form-data">
                <p class="card-description">Newsletter Details</p>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group row">
                            <label for="Title" class="col-sm-4 col-form-label">Title <span class="text-danger">*</span></label>
                            <div class="col-sm-8">
                                <input type="text" class="form-control" id="Title" name="Title" required />
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group row">
                            <label for="ScheduleAt" class="col-sm-4 col-form-label">ScheduleAt</label>
                            <div class="col-sm-8">
                                <input type="date" class="form-control" id="ScheduledAt" name="ScheduledAt" />
                            </div>
                        </div>
                    </div>
              
             </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group row">
                            <label for="Title" class="col-sm-4 col-form-label">Attachment</label>
                            <div class="col-sm-8">
                                <div class="input-group">
                                    <input type="file" name="MediaFile" class="file-upload-default d-none" id="MediaFile">
                                    <input type="text" class="form-control file-upload-info" disabled placeholder="Upload Document">
                                    <div class="input-group-append">
                                        <button class="file-upload-browse btn btn-primary btn-sm" type="button">Upload</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group row">
                            <label for="ScheduleAt" class="col-sm-4 col-form-label">Facility<span class="text-danger">*</span></label>
                            <div class="col-sm-8">
                                <select name="newsLetter-FacilityId" id="newsLetter-FacilityId" class="form-control">
                                    <option value="">-- Select Facility --</option>

                                    @foreach (var user in Model.Facilities)
                                    {
                                        <option value="@user.FacilityId">@user.FacilityName</option>
                                    }
                                </select>
                            </div>

                        </div>
                    </div>

                </div>
                <div class="row">
                <div class="col-md-6">
                    <div class="form-group row">
                        <label class="col-sm-4 col-form-label">Thumbnail Image<span class="text-danger">*</span></label>
                        <div class="col-sm-8">
                            <div class="custom-file-upload w-100">
                                <input type="file" class="d-none" id="ThumbnailFile" name="ThumbnailFile" accept="image/*" />
                                <div class="input-group">
                                    <input type="text" class="form-control file-upload-info" id="ThumbnailFileName" readonly placeholder="Choose Thumbnail" />
                                    <div class="input-group-append">
                                        <button class="btn btn-primary btn-sm" type="button" id="ThumbnailBtn">Upload</button>
                                        <button class="btn btn-danger btn-sm ml-1 d-none" type="button" id="removeThumbnailBtn">Remove</button>
                                    </div>
                                </div>
                                <div class="mt-2 d-none" id="ThumbnailPreviewContainer">
                                    <img id="ThumbnailPreview" src="" class="img-thumbnail" style="max-width: 250px;" />
                                </div>
                            </div>
                        </div>

                      @*   <div class="col-sm-6 grid-margin stretch-card">
                            <div class="card">
                                <div class="card-body">
                                    <input type="file" class="dropify" name="ProfileImage" id="ThumbnailFile" />
                                </div>
                            </div>
                        </div> *@
                    </div>
                </div>
                </div>
                    <div class="col-sm-12">
                        <div class="form-group row">
                            <label class="col-sm-2 col-form-label">Description</label>
                            <div class="col-sm-12">
                                <textarea name="Description" id="tinyMceExample" class="form-control"></textarea>
                            </div>
                        </div>
              
                </div>
                <div class="row mt-3">
                    <div class="col-md-12 text-end">
                        <button type="submit" class="btn btn-primary">Create Newsletter</button>
                        <button type="reset" class="btn btn-light">Cancel</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
<hr />
<div class="card mt-4">
    <div class="card-body">
        <h4 class="card-title">Newsletters List</h4>
        <div class="table-responsive">
            <table id="newsletter-listing" class="table">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>Title</th>
                        <th>Description</th>
                        <th>Thumbnail</th>
                        <th>Status</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
        </div>
    </div>
</div>
<!-- Edit Newsletter Modal -->
<div class="modal fade" id="newsletterModal" tabindex="-1" aria-labelledby="newsletterModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="newsletterModalLabel">Edit Newsletter</h5>
                <button type="button" class="close" data-dismiss="modal"><span>&times;</span></button>
            </div>
            <div class="modal-body">
                <form id="editNewsletterForm" class="form-sample" enctype="multipart/form-data">
                    <input type="hidden" id="EditNewsletterId" name="NewsletterId" />
                    <p class="card-description">Newsletter Details</p>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group row">
                                <label for="EditTitle" class="col-sm-4 col-form-label">Title <span class="text-danger">*</span></label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" id="EditTitle" name="Title" required />
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group row">
                                <label for="EditScheduleAt" class="col-sm-4 col-form-label">ScheduleAt</label>
                                <div class="col-sm-8">
                                    <input type="datetime-local" class="form-control" id="EditScheduleAt" name="EditScheduleAt" />
                                </div>
                            </div>
                        </div>
                </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group row">
                                <label class="col-sm-4 col-form-label">Attachment</label>
                                <div class="col-sm-8">
                                    <input type="hidden" id="ExistingAttachmentFile" />
                                    <input type="file" name="img[]" class="file-upload-default d-none" id="Attachment">
                                    <div class="input-group">
                                        <input type="text" class="form-control file-upload-info" id="announcementAttachmentFileName" readonly placeholder="Choose attachment" />
                                        <div class="input-group-append">
                                            <button class="btn btn-primary btn-sm" type="button" id="announcementAttachmentBtn">Upload</button>
                                            <button class="btn btn-danger btn-sm ml-1 d-none" type="button" id="removeAnnouncementAttachmentBtn">Remove</button>
                                        </div>
                                        @*      <input type="text" class="form-control file-upload-info attachment" disabled placeholder="Upload Attachment">
                                        <div class="input-group-append">
                                            <button class="file-upload-browse btn btn-primary btn-sm" type="button">Upload</button>
                                        </div> *@
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group row">
                                <label for="ScheduleAt" class="col-sm-4 col-form-label">Facility</label>
                                <div class="col-sm-8">
                                    <select name="newsLetter-FacilityId" id="edit-newsLetter-FacilityId" class="form-control">
                                        <option value="">-- Select Facility --</option>

                                        @foreach (var user in Model.Facilities)
                                        {
                                            <option value="@user.FacilityId">@user.FacilityName</option>
                                        }
                                    </select>
                                </div>

                            </div>
                        </div>

                    </div>
                 <div class="row">
                    <div class="col-md-6">
                        <div class="form-group row">
                            <label class="col-sm-4 col-form-label">Thumbnail</label>
                            <div class="col-sm-8">
                                <div class="custom-file-upload w-100">
                                    <input type="hidden" id="ExistingThumbnailPath" />

                                    <input type="file" class="d-none" id="AnnouncementFile" name="ThumbnailImage" accept="image/*" />
                                    <div class="input-group">
                                        <input type="text" class="form-control file-upload-info" id="announcementThumbnailFileName" readonly placeholder="Choose Thumbnail" />
                                        <div class="input-group-append">
                                            <button class="btn btn-primary btn-sm" type="button" id="announcementThumbnailBtn">Upload</button>
                                            <button class="btn btn-danger btn-sm ml-1 d-none" type="button" id="removeAnnouncementThumbnailBtn">Remove</button>
                                        </div>
                                    </div>
                                    <div class="mt-2 d-none" id="announcementThumbnailPreviewContainer">
                                        <img id="announcementThumbnailPreview" src="" class="img-thumbnail" style="max-width: 250px;" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    </div>
                  
                        <div class="col-sm-12">
                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label">Description<span class="text-danger">*</span></label>
                                <div class="col-sm-12">
                                    <textarea name="Description" id="editTinyMceExample" class="form-control"></textarea>
                                </div>
                            </div>
                        </div>
                 
                </form>
            </div>
            <div class="modal-footer">
                <button type="submit" form="editNewsletterForm" class="btn btn-primary">Save Changes</button>
            </div>
        </div>
    </div>
</div>
<script>
    var insertOrUpdateNewsletter = '@Url.Action("InsertOrUpdateNewsletter", "Newsletter")';
    var getNewsletters = '@Url.Action("GetNewsletters", "Newsletter")';
    var toggleNewsletterActivation = '@Url.Action("ToggleNewsletterActivation", "Newsletter")';
        var getNewsletterById =  '@Url.Action("GetNewsletterById", "Newsletter")';
     var deleteNewsletter =  '@Url.Action("DeleteNewsletter", "Newsletter")';
         const basePath = '@Url.Content("~/")'; // returns '/UEMS/HSSEWeb/' in production

</script>
@section Scripts {
    <script src="~/js/Newsletter/newsletter.js"></script>
} 