﻿@{
    Layout = "~/Views/Shared/_LoginLayout.cshtml";
    ViewData["Title"] = "Login";
}

<div class="container-scroller">
    <div class="container-fluid page-body-wrapper full-page-wrapper">
        <div class="content-wrapper d-flex align-items-center auth px-0">
            <div class="row w-100 mx-0">
                <div class="col-lg-4 mx-auto">
                    <div class="auth-form-light text-left py-2 px-4 px-sm-5 border">
                        <div class="brand-logo">
                            <img src="~/Content/images/dashboard/image-removebg-preview (1).png" alt="logo">
                        </div>
                        <h4>Hello! let's get started</h4>
                        <h6 class="font-weight-light">Sign in to continue.</h6>
                        <form id="loginForm" class="pt-3">
                            <div class="mb-2">
                                <a type="button" id="ssoLoginBtn"
                                   class="btn btn-block btn-facebook auth-form-btn pt-3"
                                        href="@ViewData["SSOUrl"]">
                                    Login with SSO
                                </a>
                            </div>
                            <div class="text-center mt-4 font-weight-light">
                                Login with custom creds? <a asp-controller="Login" asp-action="Login" class="text-primary">Login</a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <!-- content-wrapper ends -->
    </div>
    <!-- page-body-wrapper ends -->
</div>

@section Scripts {
    <script src="~/js/User/Login.js"></script>
}