﻿using System;
using System.Collections.Generic;

namespace HSSE_Domain.Models;

public partial class MstAuditLog
{
    public int AuditLogId { get; set; }

    public string TableName { get; set; } = null!;

    public int? RecordPrimaryKey { get; set; }

    public string OperationType { get; set; } = null!;

    public string? OldValues { get; set; }

    public string? NewValues { get; set; }

    public int ChangedBy { get; set; }

    public DateTime? ChangedAt { get; set; }
}
