@{
    ViewData["Title"] = "Manage Action Party";
}
<div class="col-12 grid-margin">
    <div class="card">
        <div class="card-body">
            <h4 class="card-title">Create Action Party</h4>
            <form id="actionPartyForm" class="form-sample">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group row">
                            <label for="ActionPartyName" class="col-sm-4 col-form-label">Action Party Name <span class="text-danger">*</span></label>
                            <div class="col-sm-8">
                                <input type="text" class="form-control" id="ActionPartyName" name="ActionPartyName" required />
                            </div>
                        </div>
                        <div class="form-group row">
                            <label for="FacilityId" class="col-sm-4 col-form-label">Facility <span class="text-danger">*</span></label>
                            <div class="col-sm-8">
                                <select class="form-control" id="FacilityId" name="FacilityId" required></select>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label for="UserIds" class="col-sm-4 col-form-label">Users <span class="text-danger">*</span></label>
                            <div class="col-sm-8">
                                <select id="UserIds" name="UserIds" class="form-control js-example-basic-multiple w-100" multiple="multiple"></select>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-sm-4 col-form-label">Status</label>
                            <div class="col-sm-8">
                                <div class="form-check">
                                    <label class="form-check-label" for="IsActive">Active</label>
                                    <label class="form-check-label">
                                <input type="checkbox" class="form-check-input" id="IsActive" name="IsActive" checked />
                                </label></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-12 text-end">
                        <button type="submit" class="btn btn-primary">Save Action Party</button>
                        <button type="reset" class="btn btn-light">Cancel</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
<div class="card mt-4">
    <div class="card-body">
        <h4 class="card-title">Action Party List</h4>
        <div class="table-responsive">
            <table id="action-party-listing" class="table">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>Name</th>
                        <th>Facility</th>
                        <th>Users</th>
                        <th>Status</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
        </div>
    </div>
</div>
<!-- Edit Modal (optional, for update) -->
<div class="modal fade" id="editActionPartyModal" tabindex="-1" aria-labelledby="editActionPartyModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editActionPartyModalLabel">Edit Action Party</h5>
                <button type="button" class="close" data-dismiss="modal"><span>&times;</span></button>
            </div>
            <div class="modal-body">
                <form id="editActionPartyForm">
                    <input type="hidden" id="EditActionPartyId" />
                    <div class="mb-3">
                        <label for="EditActionPartyName" class="form-label">Action Party Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="EditActionPartyName" required />
                    </div>
                    <div class="mb-3">
                        <label for="EditFacilityId" class="form-label">Facility <span class="text-danger">*</span></label>
                        <select class="form-control" id="EditFacilityId" required></select>
                    </div>
                    <div class="mb-3">
                        <label for="EditUserIds" class="form-label">Users <span class="text-danger">*</span></label>
                        <select class="form-control js-example-basic-multiple w-100" multiple="multiple" id="EditUserIds"  required></select>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <label class="form-check-label" for="EditIsActive">Active</label>

                            <label class="form-check-label">
                        <input type="checkbox" class="form-check-input" id="EditIsActive" />
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" form="editActionPartyForm" class="btn btn-primary">Save Changes</button>
            </div>
        </div>
    </div>
</div>
<script>
    var getActionPartyByUserId = '@Url.Action("GetActionPartyByUserId", "Inspection")';
    var createOrUpdateActionParty = '@Url.Action("CreateOrUpdateActionParty", "Inspection")';
    var getActionPartyById = '@Url.Action("GetActionPartyById", "Inspection")';
    var getAllActionPartyByFacilityId = '@Url.Action("GetAllActionPartyByFacilityId", "Inspection")';
    var getFacilities = '@Url.Action("GetFacilities", "Facility")';
    var getUsersByFacilityId = '@Url.Action("GetUsersByFacilityId", "User")';
    var toggleActionPartyActivation = '@Url.Action("ToggleEventActivation", "Inspection")';
</script>
@section Scripts {
    <script src="~/js/Inspection/actionParty.js"></script>
} 