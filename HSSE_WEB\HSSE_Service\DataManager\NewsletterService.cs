using HSSE_Models_Dto.ModelsDto;
using HSSE_Service.InterfaceService;
using HSSE_Service.ServiceResponce;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Json;
using System.Threading.Tasks;

namespace HSSE_Service.DataManager
{
    public class NewsletterService : INewsletterService
    {
        private readonly HttpClient _httpClient;
        private readonly string _apiBaseUrl;

        public NewsletterService(IHttpClientFactory httpClientFactory, IConfiguration configuration)
        {
            _httpClient = httpClientFactory.CreateClient("AuthenticatedClient");
            var baseUrl = configuration["ExternalApi:BaseUrl"];
            if (!string.IsNullOrEmpty(baseUrl))
            {
                _httpClient.BaseAddress = new Uri(baseUrl);
            }
            _apiBaseUrl = configuration["ExternalApi:BaseUrl"];
        }

        public async Task<ApiResponseDto<object>> InsertOrUpdateNewsletterAsync(MstNewsletterDto dto)
        {
            try
            {
                var apiUrl = $"{_apiBaseUrl}/api/NewsLetter/InsertOrUpdateNewsLetter";
                var response = await _httpClient.PostAsJsonAsync(apiUrl, dto);
                if (response.IsSuccessStatusCode)
                {
                    var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<object>>();
                    return ApiResponseDto<object>.SuccessResponse(apiResult?.Message ?? "Newsletter saved successfully.", (int)response.StatusCode, apiResult?.Data);
                }
                else
                {
                    return ApiResponseDto<object>.ErrorResponse("Failed to save newsletter.", (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse("Failed to save newsletter.", 500, new List<HSSE_Service.ServiceResponce.Error> {
                    new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
                });
            }
        }

        public async Task<ApiResponseDto<List<MstNewsletterDto>>> GetNewslettersAsync(int? userId, List<int>? facilityIds = null)
        {
            try
            {

                var facilityIdParams = facilityIds != null && facilityIds.Any()
                    ? string.Join("&", facilityIds.Select(id => $"facilityIds={id}"))
                    : string.Empty;
                var apiUrl = $"{_apiBaseUrl}/api/NewsLetter/GetNewsLettersByUserId?{facilityIdParams}&userId={userId}";

                var response = await _httpClient.GetAsync(apiUrl);

                if (response.IsSuccessStatusCode)
                {
                    var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<List<MstNewsletterDto>>>();
                    return ApiResponseDto<List<MstNewsletterDto>>.SuccessResponse(
                        apiResult?.Message ?? "Newsletters fetched successfully.",
                        (int)response.StatusCode,
                        apiResult?.Data ?? new List<MstNewsletterDto>()
                    );
                }
                else
                {
                    return ApiResponseDto<List<MstNewsletterDto>>.ErrorResponse("Failed to fetch newsletters.", (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<List<MstNewsletterDto>>.ErrorResponse("Failed to fetch newsletters.", 500, new List<HSSE_Service.ServiceResponce.Error>
        {
            new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
        });
            }
        }


        public async Task<ApiResponseDto<object>> ToggleNewsletterActivationAsync(int newsletterId)
        {
            try
            {
                var apiUrl = $"{_apiBaseUrl}/api/NewsLetter/ToggleNewsLetterActivation?newsletterId={newsletterId}";
                var response = await _httpClient.PostAsync(apiUrl, null);
                if (response.IsSuccessStatusCode)
                {
                    var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<object>>();
                    return ApiResponseDto<object>.SuccessResponse(apiResult?.Message ?? "Newsletter activation toggled successfully.", (int)response.StatusCode, apiResult?.Data);
                }
                else
                {
                    return ApiResponseDto<object>.ErrorResponse("Failed to toggle newsletter activation.", (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse("Failed to toggle newsletter activation.", 500, new List<HSSE_Service.ServiceResponce.Error> {
                    new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
                });
            }
        }

        public async Task<ApiResponseDto<object>> DeleteNewsletterAsync(int newsletterId)
        {
            try
            {
                var apiUrl = $"{_apiBaseUrl}/api/NewsLetter/DeleteNewsletter?newsletterId={newsletterId}";
                var response = await _httpClient.DeleteAsync(apiUrl);
                if (response.IsSuccessStatusCode)
                {
                    var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<object>>();
                    return ApiResponseDto<object>.SuccessResponse(apiResult?.Message ?? "Newsletter deleted successfully.", (int)response.StatusCode, apiResult?.Data);
                }
                else
                {
                    return ApiResponseDto<object>.ErrorResponse("Failed to delete newsletter.", (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse("Failed to delete newsletter.", 500, new List<HSSE_Service.ServiceResponce.Error> {
                    new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
                });
            }
        }

        public async Task<ApiResponseDto<MstNewsletterDto>> GetNewsletterByIdAsync(int newsletterId)
        {
            try
            {
                var apiUrl = $"{_apiBaseUrl}/api/NewsLetter/GetNewsLettersById?newsletterId={newsletterId}";
                var response = await _httpClient.GetAsync(apiUrl);
                if (response.IsSuccessStatusCode)
                {
                    var jsonString = await response.Content.ReadAsStringAsync();

                    var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<MstNewsletterDto>>();
                    return ApiResponseDto<MstNewsletterDto>.SuccessResponse(apiResult?.Message ?? "Newsletter fetched successfully.", (int)response.StatusCode, apiResult?.Data);
                }
                else
                {
                    return ApiResponseDto<MstNewsletterDto>.ErrorResponse("Failed to fetch newsletter.", (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<MstNewsletterDto>.ErrorResponse("Failed to fetch newsletter.", 500, new List<HSSE_Service.ServiceResponce.Error> {
                    new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
                });
            }
        }

        public async Task<ApiResponseDto<List<MstNewsletterDto>>> GetNewslettersByFacilityIdsAsync(List<int> facilityIds, int userId)
        {
            try
            {
                // Build query string for multiple facility IDs
                var facilityIdParams = string.Join("&", facilityIds.Select(id => $"facilityIds={id}"));

                var apiUrl = $"{_apiBaseUrl}/api/NewsLetter/GetNewsLettersByFacilityId?{facilityIdParams}&userId={userId}";

                var response = await _httpClient.GetAsync(apiUrl);
                if (response.IsSuccessStatusCode)
                {
                    var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<List<MstNewsletterDto>>>();
                    return ApiResponseDto<List<MstNewsletterDto>>.SuccessResponse(
                        apiResult?.Message ?? "Newsletters fetched successfully.",
                        (int)response.StatusCode,
                        apiResult?.Data ?? new List<MstNewsletterDto>()
                    );
                }
                else
                {
                    return ApiResponseDto<List<MstNewsletterDto>>.ErrorResponse("Failed to fetch newsletters.", (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<List<MstNewsletterDto>>.ErrorResponse("Failed to fetch newsletters.", 500, new List<HSSE_Service.ServiceResponce.Error> {
            new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
        });
            }
        }

    }
} 