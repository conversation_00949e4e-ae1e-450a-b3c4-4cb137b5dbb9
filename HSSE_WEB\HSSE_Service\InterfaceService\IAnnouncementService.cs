using HSSE_Models_Dto.ModelsDto;
using HSSE_Models_Dto.ViewModels;
using HSSE_Service.ServiceResponce;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace HSSE_Service.InterfaceService
{
    public interface IAnnouncementService
    {
        Task<ApiResponseDto<List<MstAnnouncementDto>>> GetAllAnnouncementsAsync(string token);
        Task<ApiResponseDto<object>> GetAnnouncementByIdAsync(int id);
        Task<ApiResponseDto<object>> CreateAnnouncementAsync(CreateAnnouncementViewModel dto);
        //Task<bool> UpdateAnnouncementAsync(MstAnnouncementDto dto);
        Task<ApiResponseDto<object>> DeleteAnnouncementAsync(int id);
        Task<ApiResponseDto<object>> GetAnnouncementByUserIdAsync(int? userId = null, List<int>? facilityIds = null);
        Task<ApiResponseDto<object>> GetAnnouncementDetailsByIdAsync(int id);
        Task<ApiResponseDto<object>> UpdateAnnouncementAsync(CreateAnnouncementViewModel dto);
        Task<ApiResponseDto<object>> GetUsersAnnouncementsByUserId(int? userId = null, List<int>? facilityIds = null);
        Task<ApiResponseDto<object>> ToggleAnnouncementStatusAsync(int announcementId, int statusId);
        Task<ApiResponseDto<object>> InsertOrUpdateAnnouncementCategoryAsync(MstAnnoucementCategoryDto dto);
        Task<ApiResponseDto<List<MstAnnoucementCategoryDto>>> GetAnnouncementCategoriesByUserIdAsync();
        Task<ApiResponseDto<MstAnnoucementCategoryDto>> GetAnnouncementCategoryByIdAsync(int id);
        Task<ApiResponseDto<object>> ToggleAnnouncementCategoryStatusAsync(int categoryId);
        Task<ApiResponseDto<List<MstAnnoucementCategoryDto>>> GetAnnouncementCategoriesAsync();
    }
} 