﻿@{
    ViewData["Title"] = "View Documents";
}
@* <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" /> *@
<style>
.document-tree {
    font-family: 'Segoe UI', sans-serif;
    font-size: 15px;
    line-height: 1.6;
    position: relative;
    padding-left: 20px;
    margin-top: 10px;
}

/* Vertical dashed line */
.document-tree ul {
    list-style-type: none;
    margin-left: 12px;
    padding-left: 12px;
    border-left: 1px dashed #ccc;
    position: relative;
}

/* Node container */
.document-tree li {
    position: relative;
    padding-left: 12px;
    margin-bottom: 10px;
}

/* Icon and title row */
.doc-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px 10px;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.2s ease-in-out;
}

.doc-item:hover {
    background-color: #f9f9f9;
}

/* Icons */
.doc-icon {
    font-size: 16px;
    color: #6c757d;
}

.folder-icon {
    color: #f0ad4e;
}

.download-icon {
    margin-left: auto;
    color: #198754;
}

/* Meta info */
.document-meta {
    font-size: 13px;
    color: #6c757d;
    margin-left: 28px;
    margin-top: 4px;
}

.badge-meta {
    background-color: #e9ecef;
    border-radius: 12px;
    padding: 2px 8px;
    margin-right: 6px;
    font-size: 12px;
}


</style>


<div class="card mt-2">
    <div class="card-body">
        <h4 class="card-title">Documents List</h4>
@* <table class="table table-bordered" id="documentsTable">
    <thead>
        <tr>
            <th>Title</th>
            <th>Category</th>
            <th>Version</th>
            <th>Date</th>
            <th>Download</th>
        </tr>
    </thead>
    <tbody>
    </tbody>
</table> *@
        <div id="docGrid" class="d-flex flex-wrap gap-3"></div>

</div></div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

<script>
            var getUserDocuments = '@Url.Action("GetUserDocuments", "DocumentLibrary")';

        var getDocuments = '@Url.Action("GetDocuments", "DocumentLibrary")';
                                var getDocumentById =  '@Url.Action("GetDocumentById", "DocumentLibrary")?documentId=';
    var createOrUpdateDocument =  '@Url.Action("CreateOrUpdateDocument", "DocumentLibrary")';
                                                var defaultIconUrl = '@Url.Content("~/Content/images/document-icon.png")';

</script>
@section Scripts {
    <script src="~/js/DocumentLibrary/document-create.js"></script>
} 