using AutoMapper;
using HSSE_Domain.Models;
using HSSE_Models_Dto.ModelsDto;
using HSSE_Models_Dto.ViewModels;
using HSSE_Service.Constant;
using HSSE_Service.InterfaceService;
using HSSE_Service.ServiceResponce;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Net.Http.Json;
using System.Threading.Tasks;

namespace HSSE_Service.DataManager
{
    public class AnnouncementService : IAnnouncementService
    {
        private readonly HsseDbLatestContext _context;
        private readonly IMapper _mapper;
        private readonly HttpClient _httpClient;
        private readonly string _apiBaseUrl;

        public AnnouncementService(IHttpClientFactory httpClientFactory, HsseDbLatestContext context, IMapper mapper, IConfiguration configuration)
        {
            _context = context;
            _mapper = mapper;
            _httpClient = httpClientFactory.CreateClient("AuthenticatedClient");
            var baseUrl = configuration["ExternalApi:BaseUrl"];
            if (!string.IsNullOrEmpty(baseUrl))
            {
                _httpClient.BaseAddress = new Uri(baseUrl);
            }
            _apiBaseUrl =  configuration["ExternalApi:BaseUrl"];

        }

        public async Task<ApiResponseDto<List<MstAnnouncementDto>>> GetAllAnnouncementsAsync(string token)
        {
            try
            {

                // Construct the full API URL
                var apiUrl = $"{_apiBaseUrl}{ApiEndpoints.GetAnnouncements}";

                // Create the HTTP request
                var request = new HttpRequestMessage(HttpMethod.Get, apiUrl);

                // Add Bearer token if provided
                if (!string.IsNullOrEmpty(token))
                {
                    request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", token);
                }

                // Send the request
                var response = await _httpClient.SendAsync(request);

                if (response.IsSuccessStatusCode)
                {

                    var groups = await response.Content.ReadFromJsonAsync<ApiResponseDto<List<MstAnnouncementDto>>>();
                    return ApiResponseDto<List<MstAnnouncementDto>>.SuccessResponse(
                        ApiMessages.GroupSaveSuccess,
                        (int)response.StatusCode,
                        groups?.Data ?? new List<MstAnnouncementDto>());
                }
                else
                {
                    return ApiResponseDto<List<MstAnnouncementDto>>.ErrorResponse(
                        ApiMessages.InternalServerError,
                        (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<List<MstAnnouncementDto>>.ErrorResponse(
                    ApiMessages.GroupSaveFailed,
                    500,
                    new List<HSSE_Service.ServiceResponce.Error> {
                new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
                    });
            }
        }

        public async Task<ApiResponseDto<object>> GetAnnouncementByIdAsync(int id)
        {
            try
            {
                // Construct the full API URL
                var apiUrl = $"{_apiBaseUrl}{ApiEndpoints.GetAnnouncementsById}{id}";

                // Create the HTTP request
                var request = new HttpRequestMessage(HttpMethod.Get, apiUrl);

                // Add Bearer token if provided
                //if (!string.IsNullOrEmpty(authToken))
                //{
                //    request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", authToken);
                //}

                // Send the request
                var response = await _httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<object>>();
                    return ApiResponseDto<object>.SuccessResponse(apiResult?.Message ?? "Group deleted successfully.", (int)response.StatusCode, apiResult.Data);
                }
                else
                {
                    return ApiResponseDto<object>.ErrorResponse("Failed to delete group.", (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse("Failed to delete group.", 500, new List<HSSE_Service.ServiceResponce.Error> {
                    new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
                });
            }
        }
        public async Task<ApiResponseDto<object>> GetAnnouncementDetailsByIdAsync(int id)
        {
            try
            {
                // Construct the full API URL
                var apiUrl = $"{_apiBaseUrl}{ApiEndpoints.GetAnnouncementDetailsById}{id}";

                // Create the HTTP request
                var request = new HttpRequestMessage(HttpMethod.Get, apiUrl);

                // Add Bearer token if provided
                //if (!string.IsNullOrEmpty(authToken))
                //{
                //    request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", authToken);
                //}

                // Send the request
                var response = await _httpClient.SendAsync(request); if (response.IsSuccessStatusCode)
                {
                    var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<object>>();
                    return ApiResponseDto<object>.SuccessResponse(apiResult?.Message ?? "Group deleted successfully.", (int)response.StatusCode, apiResult.Data);
                }
                else
                {
                    return ApiResponseDto<object>.ErrorResponse("Failed to delete group.", (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse("Failed to delete group.", 500, new List<HSSE_Service.ServiceResponce.Error> {
                    new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
                });
            }
        }
        public async Task<ApiResponseDto<object>> GetAnnouncementByUserIdAsync(int? userId = null, List<int>? facilityIds = null)
        {
            try
            {
                // Construct query parameters
                var queryParams = new List<string>();

                if (userId.HasValue)
                    queryParams.Add($"userId={userId.Value}");

                if (facilityIds != null && facilityIds.Any())
                {
                    foreach (var fid in facilityIds)
                    {
                        queryParams.Add($"facilityIds={fid}");
                    }
                }

                var queryString = queryParams.Any() ? "?" + string.Join("&", queryParams) : string.Empty;

                // Construct the full API URL
                var apiUrl = $"{_apiBaseUrl}{ApiEndpoints.GetAnnouncementsByUserId}{queryString}";

                // Create the HTTP request
                var request = new HttpRequestMessage(HttpMethod.Get, apiUrl);

                // Add Bearer token if needed
                // request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", authToken);

                // Send the request
                var response = await _httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<object>>();
                    return ApiResponseDto<object>.SuccessResponse(
                        apiResult?.Message ?? "Announcements retrieved successfully.",
                        (int)response.StatusCode,
                        apiResult?.Data
                    );
                }
                else
                {
                    return ApiResponseDto<object>.ErrorResponse("Failed to retrieve announcements.", (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse("Failed to retrieve announcements.", 500, new List<HSSE_Service.ServiceResponce.Error> {
            new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
        });
            }
        }


        public async Task<ApiResponseDto<object>> CreateAnnouncementAsync(CreateAnnouncementViewModel dto)
        {
            try
            {

                // Construct the full API URL
                var apiUrl = $"{_apiBaseUrl}{ApiEndpoints.InsertOrUpdateAnnouncement}";

                // Create the HTTP request
                var request = new HttpRequestMessage(HttpMethod.Post, apiUrl)
                {
                    Content = JsonContent.Create(dto) // Sends the payload as JSON content
                };

                // Add Bearer token if provided
                //if (!string.IsNullOrEmpty(authToken))
                //{
                //    request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", authToken);
                //}

                // Send the request
                var response = await _httpClient.SendAsync(request); 
                if (response.IsSuccessStatusCode)
                {
                    var msg = ApiMessages.GroupSaveSuccess;
                    // Try to read message from API response if available
                    try
                    {
                        var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<object>>();
                        if (apiResult != null && !string.IsNullOrEmpty(apiResult.Message))
                            msg = apiResult.Message;
                    }
                    catch { }
                    return ApiResponseDto<object>.SuccessResponse(msg, (int)response.StatusCode);
                }
                else
                {
                    var errorMsg = ApiMessages.GroupSaveFailed;
                    try
                    {
                        var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<object>>();
                        if (apiResult != null && !string.IsNullOrEmpty(apiResult.Message))
                            errorMsg = apiResult.Message;
                    }
                    catch { }
                    return ApiResponseDto<object>.ErrorResponse(errorMsg, (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse(ApiMessages.GroupSaveFailed, 500, new System.Collections.Generic.List<HSSE_Service.ServiceResponce.Error> {
                    new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
                });
            }
        }
        public async Task<ApiResponseDto<object>> UpdateAnnouncementAsync(CreateAnnouncementViewModel dto)
        {
            try
            {

                // Construct the full API URL
                var apiUrl = $"{_apiBaseUrl}{ApiEndpoints.UpdateAnnouncement}";

                // Create the HTTP request
                var request = new HttpRequestMessage(HttpMethod.Post, apiUrl)
                {
                    Content = JsonContent.Create(dto) // Sends the payload as JSON content
                };

                // Add Bearer token if provided
                //if (!string.IsNullOrEmpty(authToken))
                //{
                //    request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", authToken);
                //}

                // Send the request
                var response = await _httpClient.SendAsync(request); 
                if (response.IsSuccessStatusCode)
                {
                    var msg = ApiMessages.GroupSaveSuccess;
                    // Try to read message from API response if available
                    try
                    {
                        var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<object>>();
                        if (apiResult != null && !string.IsNullOrEmpty(apiResult.Message))
                            msg = apiResult.Message;
                    }
                    catch { }
                    return ApiResponseDto<object>.SuccessResponse(msg, (int)response.StatusCode);
                }
                else
                {
                    var errorMsg = ApiMessages.GroupSaveFailed;
                    try
                    {
                        var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<object>>();
                        if (apiResult != null && !string.IsNullOrEmpty(apiResult.Message))
                            errorMsg = apiResult.Message;
                    }
                    catch { }
                    return ApiResponseDto<object>.ErrorResponse(errorMsg, (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse(ApiMessages.GroupSaveFailed, 500, new System.Collections.Generic.List<HSSE_Service.ServiceResponce.Error> {
                    new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
                });
            }
        }
        public async Task<ApiResponseDto<object>> GetUsersAnnouncementsByUserId(int? userId = null, List<int>? facilityIds = null)
        {
            try
            {
                // Construct query parameters
                var queryParams = new List<string>();

                if (userId.HasValue)
                    queryParams.Add($"userId={userId.Value}");

                if (facilityIds != null && facilityIds.Any())
                {
                    foreach (var fid in facilityIds)
                        queryParams.Add($"facilityId={fid}");
                }

                var queryString = queryParams.Any() ? "?" + string.Join("&", queryParams) : string.Empty;

                // Construct the full API URL
                var apiUrl = $"{_apiBaseUrl}{ApiEndpoints.GetUsersAnnouncementsByUserId}{queryString}";

                // Create the HTTP request
                var request = new HttpRequestMessage(HttpMethod.Get, apiUrl);

                // Add Bearer token if needed
                // request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", authToken);

                // Send the request
                var response = await _httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<object>>();
                    return ApiResponseDto<object>.SuccessResponse(
                        apiResult?.Message ?? "Announcements retrieved successfully.",
                        (int)response.StatusCode,
                        apiResult?.Data
                    );
                }
                else
                {
                    return ApiResponseDto<object>.ErrorResponse("Failed to get announcements.", (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse("Failed to get announcements.", 500, new List<HSSE_Service.ServiceResponce.Error>
        {
            new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
        });
            }
        }

        public async Task<bool> UpdateAnnouncementAsync(MstAnnouncementDto dto)
        {
            var entity = await _context.MstAnnouncements.FindAsync(dto.AnnouncementsId);
            if (entity == null) return false;
            _mapper.Map(dto, entity);
            _context.MstAnnouncements.Update(entity);
            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<ApiResponseDto<object>> DeleteAnnouncementAsync(int id)
        {
            try
            {
                // Construct the full API URL
                var apiUrl = $"{_apiBaseUrl}{ApiEndpoints.DeleteAnnouncements}{id}";

                // Create the HTTP request
                var request = new HttpRequestMessage(HttpMethod.Delete, apiUrl);

                // Add Bearer token if provided
                //if (!string.IsNullOrEmpty(authToken))
                //{
                //    request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", authToken);
                //}

                // Send the request
                var response = await _httpClient.SendAsync(request); if (response.IsSuccessStatusCode)
                {
                    var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<object>>();
                    return ApiResponseDto<object>.SuccessResponse(apiResult?.Message ?? "Announcement deleted successfully.", (int)response.StatusCode);
                }
                else
                {
                    return ApiResponseDto<object>.ErrorResponse("Failed to delete group.", (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse("Failed to delete group.", 500, new List<HSSE_Service.ServiceResponce.Error> {
                    new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
                });
            }
        }

        public async Task<ApiResponseDto<object>> ToggleAnnouncementStatusAsync(int announcementId, int statusId)
        {
            try
            {
                var apiUrl = $"{_apiBaseUrl}{ApiEndpoints.ToggleAnnouncementStatus}?announcementId={announcementId}&statusId={statusId}";

                var request = new HttpRequestMessage(HttpMethod.Post, apiUrl);

                // Add bearer token if required
                // request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", authToken);

                var response = await _httpClient.SendAsync(request);

                if (response.IsSuccessStatusCode)
                {
                    var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<object>>();
                    return ApiResponseDto<object>.SuccessResponse(apiResult?.Message ?? "Status updated successfully.", (int)response.StatusCode, apiResult.Data);
                }
                else
                {
                    return ApiResponseDto<object>.ErrorResponse("Failed to update status.", (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse("Something went wrong while updating status.", 500,
                    new List<HSSE_Service.ServiceResponce.Error>
                    {
                new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
                    });
            }
        }

        public async Task<ApiResponseDto<object>> InsertOrUpdateAnnouncementCategoryAsync(MstAnnoucementCategoryDto dto)
        {
            try
            {
                var apiUrl = $"{_apiBaseUrl}{ApiEndpoints.InsertOrUpdateAnnouncementCategory}";
                var response = await _httpClient.PostAsJsonAsync(apiUrl, dto);
                if (response.IsSuccessStatusCode)
                {
                    var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<object>>();
                    return ApiResponseDto<object>.SuccessResponse(apiResult?.Message ?? "Category saved successfully.", (int)response.StatusCode, apiResult?.Data);
                }
                else
                {
                    return ApiResponseDto<object>.ErrorResponse("Failed to save category.", (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse("Failed to save category.", 500, new List<HSSE_Service.ServiceResponce.Error> {
                    new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
                });
            }
        }

        public async Task<ApiResponseDto<List<MstAnnoucementCategoryDto>>> GetAnnouncementCategoriesByUserIdAsync()
        {
            try
            {
                var apiUrl = $"{_apiBaseUrl}{ApiEndpoints.GetAnnouncementCategoriesByUserId}";
                var response = await _httpClient.GetAsync(apiUrl);
                if (response.IsSuccessStatusCode)
                {
                    var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<List<MstAnnoucementCategoryDto>>>();
                    return ApiResponseDto<List<MstAnnoucementCategoryDto>>.SuccessResponse(apiResult?.Message ?? "Categories fetched successfully.", (int)response.StatusCode, apiResult?.Data ?? new List<MstAnnoucementCategoryDto>());
                }
                else
                {
                    return ApiResponseDto<List<MstAnnoucementCategoryDto>>.ErrorResponse("Failed to fetch categories.", (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<List<MstAnnoucementCategoryDto>>.ErrorResponse("Failed to fetch categories.", 500, new List<HSSE_Service.ServiceResponce.Error> {
                    new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
                });
            }
        }

        public async Task<ApiResponseDto<MstAnnoucementCategoryDto>> GetAnnouncementCategoryByIdAsync(int id)
        {
            try
            {
                var apiUrl = $"{_apiBaseUrl}{ApiEndpoints.GetAnnouncementCategoryById}{id}";
                var response = await _httpClient.GetAsync(apiUrl);
                if (response.IsSuccessStatusCode)
                {
                    var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<MstAnnoucementCategoryDto>>();
                    return ApiResponseDto<MstAnnoucementCategoryDto>.SuccessResponse(apiResult?.Message ?? "Category fetched successfully.", (int)response.StatusCode, apiResult?.Data);
                }
                else
                {
                    return ApiResponseDto<MstAnnoucementCategoryDto>.ErrorResponse("Failed to fetch category.", (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<MstAnnoucementCategoryDto>.ErrorResponse("Failed to fetch category.", 500, new List<HSSE_Service.ServiceResponce.Error> {
                    new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
                });
            }
        }

        public async Task<ApiResponseDto<object>> ToggleAnnouncementCategoryStatusAsync(int categoryId)
        {
            try
            {
                var apiUrl = $"{_apiBaseUrl}{ApiEndpoints.ToggleAnnouncementCategoryStatus}?id={categoryId}";
                var response = await _httpClient.PostAsync(apiUrl, null);
                if (response.IsSuccessStatusCode)
                {
                    var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<object>>();
                    return ApiResponseDto<object>.SuccessResponse(apiResult?.Message ?? "Category status toggled successfully.", (int)response.StatusCode, apiResult?.Data);
                }
                else
                {
                    return ApiResponseDto<object>.ErrorResponse("Failed to toggle category status.", (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse("Failed to toggle category status.", 500, new List<HSSE_Service.ServiceResponce.Error> {
                    new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
                });
            }
        }

        public async Task<ApiResponseDto<List<MstAnnoucementCategoryDto>>> GetAnnouncementCategoriesAsync()
        {
            try
            {
                var apiUrl = $"{_apiBaseUrl}{ApiEndpoints.GetAnnouncementCategories}";
                var response = await _httpClient.GetAsync(apiUrl);
                if (response.IsSuccessStatusCode)
                {
                    var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<List<MstAnnoucementCategoryDto>>>();
                    return ApiResponseDto<List<MstAnnoucementCategoryDto>>.SuccessResponse(apiResult?.Message ?? "Categories fetched successfully.", (int)response.StatusCode, apiResult?.Data ?? new List<MstAnnoucementCategoryDto>());
                }
                else
                {
                    return ApiResponseDto<List<MstAnnoucementCategoryDto>>.ErrorResponse("Failed to fetch categories.", (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<List<MstAnnoucementCategoryDto>>.ErrorResponse("Failed to fetch categories.", 500, new List<HSSE_Service.ServiceResponce.Error> {
                    new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
                });
            }
        }

    }
} 