﻿using HSSE_Models_Dto.ModelsDto;
using HSSE_Models_Dto.ViewModels;
using HSSE_Service.DataManager;
using HSSE_Service.InterfaceService;
using HSSE_Service.ServiceResponce;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System.Text.Json;
using System.Threading.Tasks;

namespace HSSE_WEB.Controllers
{
    public class AnnouncementController : Controller
    {
        private readonly IAnnouncementService _announcementService;
        private readonly IGroupService _groupService;
        private readonly IUserService _userService;
        private readonly IPostService _postService;
        private readonly IFacilityService _facilityService;

        public AnnouncementController(IAnnouncementService announcementService, IGroupService groupService, IUserService userService, IPostService postService, IFacilityService facilityService)
        {
            _announcementService = announcementService;
            _groupService = groupService;
            _userService=userService;
            _postService=postService;
            _facilityService = facilityService;

        }

        //[HttpGet]
        //public async Task<IActionResult> Index()
        //{
        //    var announcements = await _announcementService.GetAllAnnouncementsAsync();
        //    return View(announcements);
        //}

        [HttpGet]
        public async Task<IActionResult> Details(int id)
        {
            var announcement = await _announcementService.GetAnnouncementByIdAsync(id);
            if (announcement == null)
                return NotFound();
            return Json(announcement);
        }
        public async Task<IActionResult> CreateAnnouncement()
        {
            var userId = int.Parse(HttpContext?.Session?.GetString("UserId"));

            // Get session flags
            bool isAppAdmin = bool.TryParse(HttpContext.Session.GetString("IsAppAdmin"), out var appAdmin) && appAdmin;
            bool isDepartmentAdmin = bool.TryParse(HttpContext.Session.GetString("IsDepartmentAdmin"), out var deptAdmin) && deptAdmin;
            bool isFacilityAdmin = bool.TryParse(HttpContext.Session.GetString("IsFacilityAdmin"), out var facAdmin) && facAdmin;
            bool isManager = bool.TryParse(HttpContext.Session.GetString("IsManager"), out var manager) && manager;

            // Prepare facility list
            List<MstFacilityDto> facilities;

            if (isAppAdmin || isDepartmentAdmin)
            {
                var userFacility = await _userService.GetUserFacilitiesByFacilityId(null);
                facilities = userFacility;
            }
            else if (isFacilityAdmin || isManager)
            {
                var facAdminFacilityStr = HttpContext.Session.GetString("FacilityAdminFacilityIds");
                var managerFacilityStr = HttpContext.Session.GetString("ManagerFacilityIds");

                var facAdminFacilityIds = facAdminFacilityStr?.Split(',', StringSplitOptions.RemoveEmptyEntries)
                    .Select(id => int.TryParse(id, out var fid) ? fid : 0)
                    .Where(fid => fid > 0)
                    .ToList() ?? new List<int>();

                var managerFacilityIds = managerFacilityStr?.Split(',', StringSplitOptions.RemoveEmptyEntries)
                    .Select(id => int.TryParse(id, out var fid) ? fid : 0)
                    .Where(fid => fid > 0)
                    .ToList() ?? new List<int>();

                var mergedFacilityIds = facAdminFacilityIds
                    .Union(managerFacilityIds)
                    .Distinct()
                    .ToList();

                facilities = await _userService.GetUserFacilitiesByFacilityId(mergedFacilityIds);
            }
            else if (isFacilityAdmin || isManager)
            {
                var facAdminFacilityStr = HttpContext.Session.GetString("FacilityAdminFacilityIds");
                var managerFacilityStr = HttpContext.Session.GetString("ManagerFacilityIds");

                var facAdminFacilityIds = facAdminFacilityStr?.Split(',', StringSplitOptions.RemoveEmptyEntries)
                    .Select(id => int.TryParse(id, out var fid) ? fid : 0)
                    .Where(fid => fid > 0)
                    .ToList() ?? new List<int>();

                var managerFacilityIds = managerFacilityStr?.Split(',', StringSplitOptions.RemoveEmptyEntries)
                    .Select(id => int.TryParse(id, out var fid) ? fid : 0)
                    .Where(fid => fid > 0)
                    .ToList() ?? new List<int>();

                var mergedFacilityIds = facAdminFacilityIds
                    .Union(managerFacilityIds)
                    .Distinct()
                    .ToList();

                facilities = await _userService.GetUserFacilitiesByFacilityId(mergedFacilityIds);
            }
            else
            {
                facilities = new List<MstFacilityDto>(); // No access
            }

            var announcementCategoryResponse = await _announcementService.GetAnnouncementCategoriesAsync();
            var announcementCategories = announcementCategoryResponse.Data ?? new List<MstAnnoucementCategoryDto>();

            var groupResponse = await _groupService.GetAllGroupsAsync();
            var users = await _userService.GetAllUserAsync();
            var filteredUsers = users.Where(u => u.UserId != userId).ToList();

            var viewModel = new AddUserViewModel
            {
                Group = groupResponse.Data ?? new List<GroupDto>(),
                ExistingUsers = filteredUsers,
                AnnouncementsCategory = announcementCategories,
                Facilities = facilities
            };

            return View(viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> Create([FromBody] CreateAnnouncementViewModel dto)
        {
            var userId = HttpContext?.Session?.GetString("UserId");
            dto.CreatedBy = int.Parse(userId);
            var result = await _announcementService.CreateAnnouncementAsync(dto);
            return StatusCode(result.Status, result);
        }
        public async Task<IActionResult> ViewAnnouncements()
        {
            //var token = HttpContext?.Session?.GetString("Token");

            //var groupResponse = await _announcementService.GetAllAnnouncementsAsync(token);

            //var viewModel = new AddUserViewModel
            //{
            //    Announcements = groupResponse.Data ?? new List<MstAnnouncementDto>(),
            //};

            return View();
        }
        public async Task<IActionResult> CreateAnnouncementCategory()
        {
            //var token = HttpContext?.Session?.GetString("Token");

            //var groupResponse = await _announcementService.GetAllAnnouncementsAsync(token);

            //var viewModel = new AddUserViewModel
            //{
            //    Announcements = groupResponse.Data ?? new List<MstAnnouncementDto>(),
            //};

            return View();
        }

        [HttpGet]
        public async Task<IActionResult> GetAllAnnouncements()
        {
            try
            {
                var userIdStr = HttpContext?.Session?.GetString("UserId");
                var facilityIdsStr = HttpContext?.Session?.GetString("FacilityIds"); // For multi-facility support
                var isAppAdminStr = HttpContext?.Session?.GetString("IsAppAdmin");
                var isDepartmentAdminStr = HttpContext?.Session?.GetString("IsDepartmentAdmin");
                var isFacilityAdminStr = HttpContext?.Session?.GetString("IsFacilityAdmin");

                if (!int.TryParse(userIdStr, out int userId))
                    return Unauthorized("User not logged in");

                bool isAppAdmin = bool.TryParse(isAppAdminStr, out var appAdmin) && appAdmin;
                bool isDepartmentAdmin = bool.TryParse(isDepartmentAdminStr, out var deptAdmin) && deptAdmin;
                bool isFacilityAdmin = bool.TryParse(isFacilityAdminStr, out var facAdmin) && facAdmin;

                ApiResponseDto<object> result;

                if (isAppAdmin || isDepartmentAdmin)
                {
                    // App/Department Admin → get all announcements
                    result = await _announcementService.GetAnnouncementByUserIdAsync(null, null);
                }
                else if (isFacilityAdmin)
                {
                    // Facility Admin → filter by multiple facility IDs if present
                    List<int> facilityIds = new();

                    if (!string.IsNullOrEmpty(facilityIdsStr))
                    {
                        facilityIds = facilityIdsStr
                            .Split(',', StringSplitOptions.RemoveEmptyEntries)
                            .Select(fid => int.TryParse(fid, out var id) ? id : 0)
                            .Where(id => id > 0)
                            .ToList();
                    }

                    result = await _announcementService.GetAnnouncementByUserIdAsync(null, facilityIds);
                }
                else
                {
                    // Normal user → filter by user ID only
                    result = await _announcementService.GetAnnouncementByUserIdAsync(userId, null);
                }

                return StatusCode(result.Status, result);
            }
            catch (Exception ex)
            {
                return StatusCode(500, ApiResponseDto<object>.ErrorResponse("Internal server error", 500, new List<HSSE_Service.ServiceResponce.Error>
        {
            new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
        }));
            }
        }


        public async Task<IActionResult> EditAnnouncements()
        {
            var userId = int.Parse(HttpContext?.Session?.GetString("UserId"));

            // Get session role flags
            bool isAppAdmin = bool.TryParse(HttpContext.Session.GetString("IsAppAdmin"), out var appAdmin) && appAdmin;
            bool isDepartmentAdmin = bool.TryParse(HttpContext.Session.GetString("IsDepartmentAdmin"), out var deptAdmin) && deptAdmin;
            bool isFacilityAdmin = bool.TryParse(HttpContext.Session.GetString("IsFacilityAdmin"), out var facAdmin) && facAdmin;

            // Prepare facility list
            List<MstFacilityDto> facilities;

            if (isAppAdmin || isDepartmentAdmin)
            {
                var userFacility = await _userService.GetUserFacilitiesByFacilityId(null);
                facilities = userFacility;
            }
            else if (isFacilityAdmin)
            {
                var facilityIdString = HttpContext.Session.GetString("FacilityAdminFacilityIds");
                var facilityIds = facilityIdString?.Split(',').Select(int.Parse).ToList() ?? new List<int>();
                facilities = await _userService.GetUserFacilitiesByFacilityId(facilityIds);
            }
            else
            {
                facilities = new List<MstFacilityDto>();
            }

            // Other data fetch
            var groupResponse = await _groupService.GetAllGroupsAsync();
            var users = await _userService.GetAllUserAsync();
            var announcementCategoryResponse = await _announcementService.GetAnnouncementCategoriesAsync();
            var announcementCategories = announcementCategoryResponse.Data ?? new List<MstAnnoucementCategoryDto>();

            var viewModel = new AddUserViewModel
            {
                Group = groupResponse.Data ?? new List<GroupDto>(),
                ExistingUsers = users,
                AnnouncementsCategory = announcementCategories,
                Facilities = facilities
            };

            return View(viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> UpdateAnnouncement([FromBody] CreateAnnouncementViewModel dto)
        {
            var userId = int.Parse(HttpContext?.Session?.GetString("UserId"));
            dto.CreatedBy = userId;
            var result = await _announcementService.UpdateAnnouncementAsync(dto);
            return StatusCode(result.Status, result);
        }

        [HttpGet]
        public async Task<IActionResult> GetAllAnnouncementsByUserId()
        {
            try
            {
                var userIdStr = HttpContext?.Session?.GetString("UserId");
                var facilityIdStr = HttpContext?.Session?.GetString("FacilityId");
                var facilityIdsStr = HttpContext?.Session?.GetString("FacilityIds"); // New: handle multi-facility
                var isAppAdminStr = HttpContext?.Session?.GetString("IsAppAdmin");
                var isDepartmentAdminStr = HttpContext?.Session?.GetString("IsDepartmentAdmin");
                var isFacilityAdminStr = HttpContext?.Session?.GetString("IsFacilityAdmin");

                if (!int.TryParse(userIdStr, out int userId))
                    return Unauthorized("User not logged in");

                bool isAppAdmin = bool.TryParse(isAppAdminStr, out var appAdmin) && appAdmin;
                bool isDepartmentAdmin = bool.TryParse(isDepartmentAdminStr, out var deptAdmin) && deptAdmin;
                bool isFacilityAdmin = bool.TryParse(isFacilityAdminStr, out var facAdmin) && facAdmin;

                ApiResponseDto<object> result;

                if (isAppAdmin || isDepartmentAdmin)
                {
                    // Admins: Get all announcements
                    result = await _announcementService.GetAnnouncementByUserIdAsync(null, null);
                }
                else
                {
                    // Regular user: filter by userId only
                    result = await _announcementService.GetAnnouncementByUserIdAsync(userId, null);
                }

                return StatusCode(result.Status, result);
            }
            catch (Exception ex)
            {
                return StatusCode(500, ApiResponseDto<object>.ErrorResponse("Internal server error", 500, new List<HSSE_Service.ServiceResponce.Error>
        {
            new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
        }));
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetAnnouncementDetailsById(int id)
        {
            var result = await _announcementService.GetAnnouncementDetailsByIdAsync(id);
            return StatusCode(result.Status, result);
        }


        public async Task<IActionResult> DisplayUserAnnouncements()
        {
            var announcements = new List<AnnouncementViewModel>();

            var userIdStr = HttpContext.Session.GetString("UserId");
            var facilityIdsStr = HttpContext.Session.GetString("FacilityIds"); // New: multi-facility support
            var isAppAdminStr = HttpContext.Session.GetString("IsAppAdmin");
            var isDepartmentAdminStr = HttpContext.Session.GetString("IsDepartmentAdmin");
            var isFacilityAdminStr = HttpContext.Session.GetString("IsFacilityAdmin");

            if (string.IsNullOrEmpty(userIdStr) || !int.TryParse(userIdStr, out int userId))
            {
                // User not logged in or session expired
                return View(announcements); // Return empty list to view
            }

            bool isAppAdmin = bool.TryParse(isAppAdminStr, out var appAdmin) && appAdmin;
            bool isDepartmentAdmin = bool.TryParse(isDepartmentAdminStr, out var deptAdmin) && deptAdmin;
            bool isFacilityAdmin = bool.TryParse(isFacilityAdminStr, out var facAdmin) && facAdmin;
            // Facility Admin → handle multiple facility IDs
            List<int> facilityIds = new();

            if (!string.IsNullOrEmpty(facilityIdsStr))
            {
                facilityIds = facilityIdsStr
                    .Split(',', StringSplitOptions.RemoveEmptyEntries)
                    .Select(fid => int.TryParse(fid, out var id) ? id : 0)
                    .Where(id => id > 0)
                    .ToList();
            }
            ApiResponseDto<object> apiResponse;

            if (isAppAdmin || isDepartmentAdmin)
            {
                // App or Department Admin → get all announcements
                apiResponse = await _announcementService.GetUsersAnnouncementsByUserId(null, null);
            }
            //else if (isFacilityAdmin)
            //{
            //    if (facilityIds.Count == 0)
            //    {
            //        // Invalid or no facilities found, return empty list
            //        return View(announcements);
            //    }

            //    apiResponse = await _announcementService.GetUsersAnnouncementsByUserId(null, facilityIds);
            //}
            else
            {
                // Normal User → filter only by UserId
                apiResponse = await _announcementService.GetUsersAnnouncementsByUserId(userId);
            }

            if (apiResponse.Success &&
                apiResponse.Data is JsonElement jsonElement &&
                jsonElement.ValueKind == JsonValueKind.Array &&
                jsonElement.GetArrayLength() > 0)
            { 
                var jsonString = jsonElement.GetRawText();
                announcements = JsonConvert.DeserializeObject<List<AnnouncementViewModel>>(jsonString)
                                ?? new List<AnnouncementViewModel>();
            }

            ViewData["UserName"] = HttpContext.Session.GetString("Username");
            return View(announcements);
        }

        public async Task<IActionResult> DetailAnnouncement(int announcementId)
        {
            var announcements = new AnnouncementViewModel();

            var userIdStr = HttpContext.Session.GetString("UserId");

            if (string.IsNullOrEmpty(userIdStr) || !int.TryParse(userIdStr, out int userId))
            {
                // Optionally redirect to login or show an error message
                TempData["ErrorMessage"] = "Session expired. Please log in again.";
                return RedirectToAction("Login", "Account");
            }

            var apiResponse = await _announcementService.GetAnnouncementDetailsByIdAsync(announcementId);

            if (apiResponse.Success && apiResponse.Data is JsonElement jsonElement)
            {
                try
                {
                    // If data is a single object
                    if (jsonElement.ValueKind == JsonValueKind.Object)
                    {
                        announcements = JsonConvert.DeserializeObject<AnnouncementViewModel>(jsonElement.GetRawText())
                                        ?? new AnnouncementViewModel();
                    }
                    // If data is an array, and you're expecting the first item
                    else if (jsonElement.ValueKind == JsonValueKind.Array && jsonElement.GetArrayLength() > 0)
                    {
                        var firstItem = jsonElement[0].GetRawText();
                        announcements = JsonConvert.DeserializeObject<AnnouncementViewModel>(firstItem)
                                        ?? new AnnouncementViewModel();
                    }
                }
                catch (Exception ex)
                {
                    // Log the exception (if you have a logger)
                    TempData["ErrorMessage"] = "An error occurred while processing announcement data.";
                }
            }

            return View(announcements);
        }

        [HttpDelete]
        public async Task<IActionResult> DeleteAnnouncement(int id)
        {
            var result = await _announcementService.DeleteAnnouncementAsync(id);
            return StatusCode(result.Status, result);
        }
        [HttpPost]
        public async Task<IActionResult> ToggleAnnouncementStatus(int newsLetterId, int statusId)
        {
            var result = await _announcementService.ToggleAnnouncementStatusAsync(newsLetterId, statusId);
            return StatusCode(result.Status, result);
        }
        [HttpPost]
        public async Task<IActionResult> InsertOrUpdateAnnouncementCategory([FromBody] MstAnnoucementCategoryDto dto)
        {
            var userId = int.Parse(HttpContext?.Session?.GetString("UserId"));

            dto.CreatedBy = userId;
            var result = await _announcementService.InsertOrUpdateAnnouncementCategoryAsync(dto);
            return StatusCode(result.Status, result);
        }
        [HttpGet]
        public async Task<IActionResult> GetAnnouncementCategoriesByUserId()
        {
            var result = await _announcementService.GetAnnouncementCategoriesByUserIdAsync();
            return StatusCode(result.Status, result);
        }
        [HttpGet]
        public async Task<IActionResult> GetAnnouncementCategoryById(int id)
        {
            var result = await _announcementService.GetAnnouncementCategoryByIdAsync(id);
            return StatusCode(result.Status, result);
        }
        [HttpPost]
        public async Task<IActionResult> ToggleAnnouncementCategoryStatus(int categoryId)
        {
            var result = await _announcementService.ToggleAnnouncementCategoryStatusAsync(categoryId);
            return StatusCode(result.Status, result);
        }
    }
} 