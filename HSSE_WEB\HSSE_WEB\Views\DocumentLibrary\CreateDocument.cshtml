@model HSSE_Models_Dto.ViewModels.AddUserViewModel
@{
    ViewData["Title"] = "Create Document";
}
<div class="card mt-2">
    <div class="card-body">
        <h4 class="card-title">Upload Document</h4>

        <form id="documentForm" data-action="@Url.Action("CreateOrUpdateDocument", "DocumentLibrary")" enctype="multipart/form-data">
            <div class="row">
                <!-- Title -->
                <div class="col-md-6">
                    <div class="form-group row">
                        <label class="col-sm-3 col-form-label">Title <span class="text-danger">*</span></label>
                        <div class="col-sm-9">
                            <input type="text" class="form-control" id="Title" name="Title" required />
                        </div>
                    </div>
                </div>

                <!-- Category -->
                <div class="col-md-6">
                    <div class="form-group row">
                        <label class="col-sm-3 col-form-label">Category</label>
                        <div class="col-sm-9">
                            <input type="text" class="form-control" id="Category" name="Category" />
                        </div>
                    </div>
                </div>

                <!-- Version -->
                <div class="col-md-6">
                    <div class="form-group row">
                        <label class="col-sm-3 col-form-label">Version</label>
                        <div class="col-sm-9">
                            <input type="text" class="form-control" id="Version" name="Version" />
                        </div>
                    </div>
                </div>

                <!-- Parent Document -->
                <div class="col-md-6">
                    <div class="form-group row">
                        <label class="col-sm-3 col-form-label">Parent Document</label>
                        <div class="col-sm-9">
                            <select class="form-control" id="ParentDocumentId" name="ParentDocumentId">
                                <option value="">-- Select Parent (Optional) --</option>
                                @* Dynamically bind document list from ViewBag *@

                                @foreach (var doc in Model.DocumentLibrary)
                                    {
                                    <option value="@doc.DocumentId">@doc.Title</option>
                                    }

                            </select>
                        </div>
                    </div>
                </div>

                <!-- File Upload -->
                <div class="col-md-6">
                    <div class="form-group row">
                        <label class="col-sm-3 col-form-label">File upload</label>
                        <div class="col-sm-9">
                            <div class="input-group">
                                <input type="file" name="DocumentFile" class="file-upload-default d-none" id="DocumentFile">
                                <input type="text" class="form-control file-upload-info" disabled placeholder="Upload Document">
                                <div class="input-group-append">
                                    <button class="file-upload-browse btn btn-primary py-0 px-2" type="button">Upload</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Submit Button -->
            <div class="form-group text-right">
                <button type="submit" class="btn btn-outline-primary btn-icon-text">
                    <i class="mdi mdi-file-check btn-icon-prepend"></i>Submit
                </button>
            </div>
        </form>
    </div>
</div>

<script>
    var getDocuments = '@Url.Action("GetDocuments", "DocumentLibrary")';

            var getUserDocuments = '@Url.Action("GetUserDocuments", "DocumentLibrary")';
                        var removeDocument = '@Url.Action("RemoveDocument", "DocumentLibrary")&documentId=';
                                 var getDocumentById =  '@Url.Action("RemoveDocument", "GetDocumentById")?documentId=';
    var createOrUpdateDocument =  '@Url.Action("CreateOrUpdateDocument", "DocumentLibrary")';
                                                var defaultIconUrl = '@Url.Content("~/Content/images/document-icon.png")';


</script>
@section Scripts {
    <script src="~/js/DocumentLibrary/document-create.js"></script>
} 