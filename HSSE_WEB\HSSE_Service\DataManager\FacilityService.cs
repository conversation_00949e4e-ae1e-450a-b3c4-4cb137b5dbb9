﻿using AutoMapper;
using HSSE_Domain.Models;
using HSSE_Models_Dto.ModelsDto;
using HSSE_Service.InterfaceService;
using HSSE_Service.ServiceResponce;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http.Json;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace HSSE_Service.DataManager
{
    public class FacilityService : IFacilityService
    {
        private readonly HsseDbLatestContext _context;
        private IConfiguration _configuration;
        private readonly IMapper _mapper;
        private readonly HttpClient _httpClient;


        public FacilityService(HsseDbLatestContext context, IConfiguration configuration, IMapper mapper, IHttpClientFactory httpClientFactory)
        {
            _context = context;
            _configuration = configuration;
            _mapper = mapper;
            _httpClient = httpClientFactory.CreateClient("AuthenticatedClient");


        }

        public async Task<List<MstFacilityDto>> GetAllFacilityAsync()
        {
            var facilities = await _context.MstFacilities.Where(x => x.IsActive)
                .Include(x => x.Org)
                .ToListAsync();
            return _mapper.Map<List<MstFacilityDto>>(facilities);
        }
        public async Task<List<MstFacilityDto>> GetAllMasterFacilityAsync()
        {
            var facilities = await _context.MstFacilities
                .Include(x => x.Org)
                .ToListAsync();
            return _mapper.Map<List<MstFacilityDto>>(facilities);
        }
        public async Task<List<MstOrganisationDto>> GetAllOrgAsync()
        {
            var facilities = await _context.MstOrganisations.ToListAsync();
            return _mapper.Map<List<MstOrganisationDto>>(facilities);
        }

        public async Task<List<MstFacilityDto>> GetAllFacilities()
        {
            var facilities = await _context.MstFacilities.ToListAsync();
            return _mapper.Map<List<MstFacilityDto>>(facilities);
        }

        public async Task<List<FacilityRoleDto>> GetUserRolesForFacility(int facilityId, int userId)
        {
            var roles = await _context.MstUserRolesConfigs
                .Include(x => x.User)
                .Include(x => x.Role)
                .Where(x => x.FacilityId == facilityId &&
                           x.User.UserId == userId &&
                           x.User.IsActive == true)
                .Select(x => new FacilityRoleDto
                {
                    FacilityId = x.FacilityId ?? 0,
                    FacilityName = x.Facility.FacilityName ?? "N/A",
                    RoleId = x.RoleId,
                    RoleName = x.Role.RoleName
                })
                .ToListAsync();

            return roles;
        }

        public async Task<MstFacilityDto> GetFacilityByIdAsync(int id)
        {
            var facility = await _context.MstFacilities
                .Include(x => x.Org)
                .FirstOrDefaultAsync(x => x.FacilityId == id);

            if (facility == null)
                return null;

            var facilityDto = _mapper.Map<MstFacilityDto>(facility);
            return facilityDto;
        }

        public async Task<bool> UpdateFacilityAsync(MstFacilityDto facilityDto)
        {
            try
            {
                var facility = await _context.MstFacilities
                    .FirstOrDefaultAsync(x => x.FacilityId == facilityDto.FacilityId);

                if (facility == null)
                    return false;

                facility.FacilityName = facilityDto.FacilityName;
                facility.FacilityCode = facilityDto.FacilityCode;
                facility.OrgId = facilityDto.OrgId;

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<bool> ToggleFacilityStatusAsync(int id)
        {
            try
            {
                var facility = await _context.MstFacilities
                    .FirstOrDefaultAsync(x => x.FacilityId == id);

                if (facility == null)
                    return false;

                facility.IsActive = !facility.IsActive;
                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<bool> SaveFacilityAsync(MstFacilityDto facilityDto)
        {
            try
            {
                var facility = new MstFacility
                {
                    FacilityName = facilityDto.FacilityName,
                    FacilityCode = facilityDto.FacilityCode,
                    OrgId = facilityDto.OrgId,
                    CreatedAt = DateTime.Now,
                    IsActive = true
                };

                _context.MstFacilities.Add(facility);
                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }
        public async Task<ApiResponseDto<List<MstFacilityDto>>> GetFacilityListAsync()
        {
            try
            {
                var apiUrl = _configuration["SSO:SSO_Facility_API"];

                var staticPayload = new
                {
                    data = new
                    {
                        facilityId = 1,
                        userLanguage = "en",
                        requestObj = new
                        {
                            page = 0,
                            pageSize = 10000,
                            searchString = "",
                            UserEmail = "<EMAIL>"
                        }
                    }
                };

                var requestContent = JsonContent.Create(staticPayload);
                var response = await _httpClient.PostAsync(apiUrl, requestContent);

                if (!response.IsSuccessStatusCode)
                {
                    return ApiResponseDto<List<MstFacilityDto>>.ErrorResponse("Failed to retrieve facility list.", (int)response.StatusCode);
                }

                var rawApiResult = await response.Content.ReadFromJsonAsync<FacilityApiResponse>();

                if (string.IsNullOrEmpty(rawApiResult?.Obj))
                {
                    return ApiResponseDto<List<MstFacilityDto>>.ErrorResponse("No facility data found.", (int)response.StatusCode);
                }

                // Step 1: Deserialize the nested 'obj'
                var facilityData = JsonConvert.DeserializeObject<FacilityData>(rawApiResult.Obj);
                var incomingFacilities = facilityData?.GridData ?? new List<MstFacilityDto>();

                // Step 2: Get existing facility codes from DB
                var existingFacilityCodes = await _context.MstFacilities
                    .Select(f => f.FacilityCode)
                    .ToListAsync();

                // Step 3: Filter only new facilities not in DB
                var newFacilities = incomingFacilities
              .Where(f => !existingFacilityCodes.Contains(f.FacilityCode))
              .GroupBy(f => new { f.FacilityCode, f.FacilityName, f.FacilityId, f.IsActive })
              .Select(g => new MstFacility
              {
                  OrgId = 1,
                  FacilityName = g.Key.FacilityName,
                  FacilityCode = g.Key.FacilityCode,
                  CreatedAt = DateTime.UtcNow,
                  IsActive = true
              })
              .ToList();

                if (newFacilities.Any())
                {
                    await _context.MstFacilities.AddRangeAsync(newFacilities);
                    await _context.SaveChangesAsync();
                }

                return ApiResponseDto<List<MstFacilityDto>>.SuccessResponse("Filtered facility list retrieved.", 200, null);
            }
            catch (Exception ex)
            {
                return ApiResponseDto<List<MstFacilityDto>>.ErrorResponse("Exception occurred while retrieving facility list.", 500,
                    new List<HSSE_Service.ServiceResponce.Error>
                    {
                new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
                    });
            }
        }

    }
}
