@{
    ViewData["Title"] = "Edit Permissions";
}

<div class="row">
    <div class="col-12 grid-margin">
        <div class="card">
            <div class="card-body">
                <h4 class="card-title">Manage Permissions</h4>

                <div class="form-group">
@*                     <label for="permissionFilter"><strong>Filter Permissions</strong></label>
 *@                    <select id="permissionFilter" class="form-control w-25">
                        <option value="all">All Permissions</option>
                        <option value="parent">Parent Menus</option>
                        <option value="child">Child Menus</option>
                    </select>
                </div>

                <div class="table-responsive">
                    <table class="table table-striped" id="permissionsTable">
                        <thead>
                            <tr>
                                <th>Menu Name</th>
                                <th>Controller</th>
                                <th>Action</th>
                                <th>Parent Menu</th>
                                <th>Order</th>
                                <th>Is Active</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Edit Permission Modal -->
<div class="modal fade" id="editPermissionModal" tabindex="-1" role="dialog" aria-labelledby="editPermissionModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editPermissionModalLabel">Edit Permission</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="editPermissionForm">
                    <input type="hidden" id="editPermissionId" name="permissionId">
                    <div class="form-row">
                        <div class="form-group col-md-6">
                            <label for="editMenuName">Menu Name</label>
                            <input type="text" class="form-control" id="editMenuName" name="menuName" required>
                        </div>
                        <div class="form-group col-md-6">
                            <label for="editControllerName">Controller Name</label>
                            <input type="text" class="form-control" id="editControllerName" name="controllerName" required>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group col-md-6">
                            <label for="editActionName">Action Name</label>
                            <input type="text" class="form-control" id="editActionName" name="actionName" required>
                        </div>
                        <div class="form-group col-md-6">
                            <label for="editIcon">Icon</label>
                            <input type="text" class="form-control" id="editIcon" name="icon">
                        </div>
                    </div>
   
                    <div class="form-row">
                        <div class="form-group col-md-6">
                            <label for="editParentMenuId">Parent Menu</label>
                            <select class="form-control" id="editParentMenuId" name="parentMenuId">
                                <option value="">-- No Parent --</option>
                            </select>
                        </div>
                        <div class="form-group col-md-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="editIsActive" name="isActive">
                                <label class="form-check-label" for="editIsActive">
                                    Is Active
                                </label>
                            </div>
                        </div>
                    </div>
                    <button type="submit" class="btn btn-primary">Save Changes</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deletePermissionModal" tabindex="-1" role="dialog" aria-labelledby="deletePermissionModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deletePermissionModalLabel">Confirm Deletion</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete this permission?
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDeletePermissionBtn">Delete</button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="~/js/permission/editPermissions.js"></script>
} 