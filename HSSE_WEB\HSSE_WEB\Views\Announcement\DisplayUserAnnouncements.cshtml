﻿@using HSSE_Models_Dto.ViewModels
@model List<AnnouncementViewModel>

@{
    ViewBag.Title = "User Dashboard";
    var imgBasePath = Url.Content("~/"); // returns '/UEMS/HSSEWeb/' in production
    var today = DateTime.Today;
}

<div class="card mt-4">
    <div class="card-body">
        <h4 class="card-title">Announcements</h4>
        <div class="py-5">


            @if (Model == null || !Model.Any())
            {
                <div class="col-12 text-center text-muted">No announcements available at this time.</div>
            }
            else
            {
                <div class="row g-4">
                    @foreach (var announcement in Model)
                    {
                        var plainText = System.Text.RegularExpressions.Regex.Replace(announcement.Description ?? "", "<[^>]+>", " ").Trim();
                        var maxLength = 150;
                        var truncatedText = plainText.Length > maxLength
                        ? plainText.Substring(0, maxLength).Trim() + "..."
                        : plainText;

                        var isNew = (announcement.ScheduleAt?.Date == DateTime.Today || (!announcement.ScheduleAt.HasValue && announcement.CreatedAt?.Date == DateTime.Today));
                        var route = Url.Action("DetailAnnouncement", "Announcement", new { announcementId = announcement.AnnouncementsId });

                        <div class="col-md-4 pb-3 mt-5">
                            <h6 class="fw-semibold mb-2 text-primary">@announcement.Title</h6>
                            <div class="card h-100 shadow-sm border-0 rounded-4 position-relative announcement-card-hover">
                                <a href="@route" class="stretched-link"></a>

                                @if (!string.IsNullOrEmpty(announcement.DocumentPath))
                                {
                                    <img src="@announcement.DocumentPath"
                                         class="card-img-top rounded-top-4"
                                         style="height: 160px; object-fit: cover;"
                                         alt="Announcement Image" />
                                }
                                else
                                {
                                    <img src="@Url.Content("~/Content/images/Image_not_available.jpg")"
                                         class="card-img-top rounded-top-4"
                                         style="height: 160px; object-fit: cover;"
                                         alt="Announcement Image" />
                                }

                                <div class="card-body p-3 d-flex flex-column">
                                    <p class="text-muted small mb-3" style="min-height: 60px;">@truncatedText</p>

                                    <div class="d-flex flex-wrap gap-2 mt-auto">
                                        <span class="badge bg-light border text-muted">
                                            <i class="mdi mdi-account"></i> @announcement.PublishedBy
                                        </span>
                                 @*        @if (announcement.ScheduleAt.HasValue)
                                        {
                                            <span class="badge bg-light border text-muted">
                                                <i class="bi bi-calendar-event me-1"></i> @announcement.ScheduleAt.Value.ToString("MMM dd, yyyy")
                                            </span>
                                        } *@
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                </div>
            }
        </div>
    </div>
</div>


