using HSSE_Domain.Models;
using HSSE_Model_Dto.ModelDto;
using HSSE_ModelDto.ModelDto;
using HSSE_Models_Dto.ModelsDto;
using HSSE_Models_Dto.ViewModels;
using HSSE_Service.Constant;
using HSSE_Service.DataManager;
using HSSE_Service.InterfaceService;
using HSSE_Service.ServiceResponce;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace HSSE_WEB.Controllers
{
    public class PostController : Controller
    {
        private readonly IPostService _postService;
        private readonly IUserService _userService;

        public PostController(IPostService postService, IUserService userService)
        {
            _postService = postService;
            _userService=userService;
        }
        public async Task<IActionResult> CreatePost()
        {
            var userId = int.Parse(HttpContext?.Session?.GetString("UserId"));

            // Get session flags
            bool isAppAdmin = bool.TryParse(HttpContext.Session.GetString("IsAppAdmin"), out var appAdmin) && appAdmin;
            bool isDepartmentAdmin = bool.TryParse(HttpContext.Session.GetString("IsDepartmentAdmin"), out var deptAdmin) && deptAdmin;
            bool isFacilityAdmin = bool.TryParse(HttpContext.Session.GetString("IsFacilityAdmin"), out var facAdmin) && facAdmin;
            var userFacilities = HttpContext?.Session?.GetString("FacilityIds");
            // Prepare facility list
            List<MstFacilityDto> facilities;

            if (isAppAdmin || isDepartmentAdmin)
            {
                var userFacility = await _userService.GetUserFacilitiesByFacilityId(null);
                facilities = userFacility;
            }
            else
            {
                var userFacilitiesId = userFacilities?.Split(',').Select(int.Parse).ToList() ?? new List<int>();

                facilities = await _userService.GetUserFacilitiesByFacilityId(userFacilitiesId);
            }
            var postCategory = await _postService.GetPostCategoriesAsync();
            var viewModel = new AddUserViewModel
            {
                MstPostCategory = postCategory.Data,
                Facilities = facilities
            };

            return View(viewModel);
        }
        [HttpPost]
        public async Task<IActionResult> Create([FromBody] MstPostDto dto)
        {
            var userId = int.Parse(HttpContext?.Session?.GetString("UserId"));

            // Set UserId and FacilityId from session if needed
            dto.UserId = userId;
            var result = await _postService.CreatePostAsync(dto);
            return StatusCode(result.Status, result);
        }
        [HttpGet]
        public async Task<IActionResult> EditPosts()
        {
            var userId = int.Parse(HttpContext?.Session?.GetString("UserId"));

            var postCategory = await _postService.GetPostCategoriesAsync();
            var userFacility = await _postService.GetUserFacilityAsync(userId);
            if (userFacility.Data == null)
            {
                var facilities = await _userService.GetUserFacilitiesByFacilityId(null);
                userFacility = new ApiResponseDto<List<MstFacilityDto>>
                {
                    Data = facilities,
                    Message = "Success",
                    Status = 200
                };
            }

            var viewModel = new AddUserViewModel
            {
                MstPostCategory = postCategory.Data,
                Facilities = userFacility.Data
            };
            return View(viewModel);
        }
        [HttpGet]
        public async Task<IActionResult> GetAll()
        {
            var result = await _postService.GetAllPostsAsync();
            return StatusCode(result.Status, result);
        }
        [HttpGet]
        public async Task<IActionResult> GetById(int postId)
        {
            var result = await _postService.GetPostByIdAsync(postId);
            return StatusCode(result.Status, result);
        }
        [HttpPost]
        public async Task<IActionResult> Update([FromBody] MstPostDto dto)
        {
            var userId = int.Parse(HttpContext?.Session?.GetString("UserId"));

            // Set UserId and FacilityId from session if needed
            dto.UserId = userId;
            var result = await _postService.UpdatePostAsync(dto);
            return StatusCode(result.Status, result);
        }
        [HttpGet]
        public async Task<IActionResult> ViewPosts()
        {
            var userId = int.Parse(HttpContext?.Session?.GetString("UserId"));

            var postCategory = await _postService.GetPostCategoriesAsync();
            var userFacility = await _postService.GetUserFacilityAsync(userId);
            if (userFacility.Data == null)
            {
                var facilities = await _userService.GetUserFacilitiesByFacilityId(null);
                userFacility = new ApiResponseDto<List<MstFacilityDto>>
                {
                    Data = facilities,
                    Message = "Success",
                    Status = 200
                };
            }

            var viewModel = new AddUserViewModel
            {
                MstPostCategory = postCategory.Data,
                Facilities = userFacility.Data
            };
            return View(viewModel);
        }
        [HttpGet]
        public async Task<IActionResult> GetPostsByUserId(
           int? userId,
           bool isMyPosts,
           int pageNumber = 1,
           int pageSize = 10)
        {
            userId = int.Parse(HttpContext?.Session?.GetString("UserId"));

            // Get session flags
            bool isAppAdmin = bool.TryParse(HttpContext.Session.GetString("IsAppAdmin"), out var appAdmin) && appAdmin;
            bool isDepartmentAdmin = bool.TryParse(HttpContext.Session.GetString("IsDepartmentAdmin"), out var deptAdmin) && deptAdmin;
            bool isFacilityAdmin = bool.TryParse(HttpContext.Session.GetString("IsFacilityAdmin"), out var facAdmin) && facAdmin;

            ApiResponseDto<PaginatedResponseDto<MstPostDto>> usersDetails;

            if (isAppAdmin || isDepartmentAdmin)
            {
                usersDetails = await _postService.GetPostsByUserIdAsync(userId, true, isMyPosts, pageNumber, pageSize);

                // ? Force IsAuthor = true only for returned posts
                if (usersDetails.Data?.Items != null)
                {
                    usersDetails.Data.Items.ForEach(post => post.IsAuthor = true);
                }
            }
            else
            {
                usersDetails = await _postService.GetPostsByUserIdAsync(userId, false, isMyPosts, pageNumber, pageSize);
            }

            return StatusCode(usersDetails.Status, usersDetails);
        }


        [HttpGet]
        public async Task<IActionResult> ToggleLike(int postId)
        {
            var postDto = new MstLikesConfigDto();
            var userId = int.Parse(HttpContext?.Session?.GetString("UserId"));
            postDto.UserId = userId;
            postDto.PostId = postId;
            var result = await _postService.ToggleLikeAsync(postDto);
            return StatusCode(result.Status, result);
        }

        [HttpGet]
        public async Task<IActionResult> GetAllGetPostComments(int postId)
        {
            var result = await _postService.GetPostCommentAsync(postId);
            return StatusCode(result.Status, result);
        }
        [HttpPost]
        public async Task<IActionResult> CreateComment([FromBody] MstPostCommentDto dto)
        {
            var userId = int.Parse(HttpContext?.Session?.GetString("UserId"));

            // Set UserId and FacilityId from session if needed
            dto.UserId = userId;
            var result = await _postService.PostCommentAsync(dto);
            return StatusCode(result.Status, result);
        }

        [HttpPost]
        public async Task<IActionResult> AssignPost([FromBody] MstFollowupPostDto model)
        {
            var userId = int.Parse(HttpContext?.Session?.GetString("UserId"));

            model.CreatedBy = userId;
            var result = await _postService.AssignPostAsync(model);
            return StatusCode(result.Status, result);
        }
        [HttpGet]
        public async Task<IActionResult> GetUserByFacilityId(int facilityId)
        {
                var userId = int.Parse(HttpContext?.Session?.GetString("UserId"));

                // Get session flags
                bool isAppAdmin = bool.TryParse(HttpContext.Session.GetString("IsAppAdmin"), out var appAdmin) && appAdmin;
                bool isDepartmentAdmin = bool.TryParse(HttpContext.Session.GetString("IsDepartmentAdmin"), out var deptAdmin) && deptAdmin;
                bool isFacilityAdmin = bool.TryParse(HttpContext.Session.GetString("IsFacilityAdmin"), out var facAdmin) && facAdmin;

                ApiResponseDto<List<MstUserDto>> usersDetails;

                if (isAppAdmin || isDepartmentAdmin)
                {
                usersDetails = await _postService.GetUserByFacilityIdAndUserIdAsync(new List<int> { facilityId }, null);
            }
            else 
                {
                    //var facilityIdString = HttpContext.Session.GetString("FacilityAdminFacilityIds");
                    //var facilityIds = facilityIdString?.Split(',').Select(int.Parse).ToList() ?? new List<int>();

                    usersDetails = await _postService.GetUserByFacilityIdAndUserIdAsync(new List<int> { facilityId }, userId);
                }


                // Return full response (including success, message, and data)
                return StatusCode(usersDetails.Status, usersDetails);
     }

        [HttpGet]
        public async Task<IActionResult> GetAssignedPosts(int statusId)
        {
            var userId = int.Parse(HttpContext?.Session?.GetString("UserId"));
            var result = await _postService.GetAssignedPostsAsync(userId, statusId);
            return StatusCode(result.Status, result);
        }

        [HttpGet]
        public IActionResult AssignedPosts()
        {
            return View();
        }
        [HttpPost]
        public async Task<IActionResult> UpdateFollowUpStatus([FromBody] FollowupStatusUpdateRequestDto dto)
        {
            dto.UserId = int.Parse(HttpContext?.Session?.GetString("UserId"));
            var result = await _postService.UpdateFollowUpStatusAsync(dto);
            return StatusCode(result.Status, result);
        }
        [HttpDelete]
        public async Task<IActionResult> DeletePost(int postId)
        {
            var deletedBy = int.Parse(HttpContext?.Session?.GetString("UserId"));

            var result = await _postService.DeletePostAsync(postId, deletedBy);
            return StatusCode(result.Status, result);
        }
        [HttpGet]
        public IActionResult CompletePosts()
        {
            return View();
        }
    }
} 