using HSSE_Models_Dto.ModelsDto;
using HSSE_Service.InterfaceService;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using System.Collections.Generic;
using HSSE_Service.DataManager;
using HSSE_Models_Dto.ViewModels;
using HSSE_Service.ServiceResponce;

namespace HSSE_WEB.Controllers
{
    public class InspectionController : Controller
    {
        private readonly IInspectionService _inspectionService;
        private readonly IUserService _userService;
        private readonly IFacilityService _facilityService;


        public InspectionController(IInspectionService inspectionService, IUserService userService, IFacilityService facilityService)
        {
            _inspectionService = inspectionService;
            _userService=userService;
            _facilityService=facilityService;

        }

        public async Task<IActionResult> CreateInspection()
        {

            var userId = int.Parse(HttpContext?.Session?.GetString("UserId"));

            var userResponse = await _userService.GetUserByIdAsync(userId);
            var InspectionCategory = await _inspectionService.GetInspectionCategoryAsync();
            var InspectionDetails = await _inspectionService.GetUserInspectionsAsync();
            var viewModel = new AddUserViewModel
            {
                NewUser = userResponse,
                InspectionCategory = InspectionCategory.Data ?? new List<MstInspectionCategoryDto>(),
                InspectionDetails =  InspectionDetails.Data
            };
            return View(viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> InsertOrUpdateInspection([FromBody] MstNewInspectionDto dto)
        {
            //dto.FacilityId = int.Parse(HttpContext?.Session?.GetString("FacilityId"));
            dto.CreatedBy = int.Parse(HttpContext?.Session?.GetString("UserId"));
            var result = await _inspectionService.InsertOrUpdateInspectionAsync(dto);
            return StatusCode(result.Status, result);
        }

        [HttpGet]
        public async Task<IActionResult> GetInspections()
        {
            int userId = int.Parse(HttpContext?.Session?.GetString("UserId"));
            var result = await _inspectionService.GetInspectionsAsync(userId);
            return StatusCode(result.Status, result);
        }

        [HttpGet]
        public IActionResult ActionParty()
        {
            return View();
        }
        [HttpGet]
        public IActionResult AssignedInspections()
        {
            return View();
        }
        [HttpGet]
        public IActionResult UpdateInspections()
        {
            return View();
        }
        [HttpGet]
        public IActionResult CompletedInspections()
        {
            return View();
        }

        [HttpGet]
        public async Task<IActionResult> ViewUserInspection(int id)
        {
            try
            {
                var userId = int.Parse(HttpContext?.Session?.GetString("UserId"));
                var inspection = await _inspectionService.GetInspectionByIdAsync(id); // Replace with your logic

                var userResponse = await _userService.GetUserByIdAsync(userId);
                var InspectionCategory = await _inspectionService.GetInspectionCategoryAsync();
                var viewModel = new AddUserViewModel
                {   inspectionDetails =  inspection.Data,
                    NewUser = userResponse,
                    InspectionCategory = InspectionCategory.Data ?? new List<MstInspectionCategoryDto>(),
                };
                ViewBag.FacilityList = await _facilityService.GetAllFacilityAsync(); 
                if (inspection == null)
                    return NotFound();

                return View(viewModel); // Pass to strongly typed view
            }
            catch (Exception ex)
            {

                throw;
            }
        }


        [HttpGet]
        public async Task<IActionResult> AssignedUserInspection(int id)
        {
            try
            {
                var userId = int.Parse(HttpContext?.Session?.GetString("UserId"));
                var inspection = await _inspectionService.GetInspectionByIdAsync(id); // Replace with your logic

                var userResponse = await _userService.GetUserByIdAsync(userId);
                var InspectionCategory = await _inspectionService.GetInspectionCategoryAsync();
                var viewModel = new AddUserViewModel
                {
                    inspectionDetails =  inspection.Data,
                    NewUser = userResponse,
                    InspectionCategory = InspectionCategory.Data ?? new List<MstInspectionCategoryDto>(),
                    Facilities = await _facilityService.GetAllFacilityAsync()
                };
                if (inspection == null)
                    return NotFound();

                return View(viewModel); // Pass to strongly typed view
            }
            catch (Exception ex)
            {

                throw;
            }
        }
        [HttpGet]
        public async Task<IActionResult> UpdateUserInspection(int id)
        {
            try
            {
                var userId = int.Parse(HttpContext?.Session?.GetString("UserId"));
                var inspection = await _inspectionService.GetInspectionByIdAsync(id); // Replace with your logic

                var userResponse = await _userService.GetUserByIdAsync(userId);
                var InspectionCategory = await _inspectionService.GetInspectionCategoryAsync();
                var viewModel = new AddUserViewModel
                {
                    inspectionDetails =  inspection.Data,
                    NewUser = userResponse,
                    InspectionCategory = InspectionCategory.Data ?? new List<MstInspectionCategoryDto>(),
                    Facilities = await _facilityService.GetAllFacilityAsync()
                };
                if (inspection == null)
                    return NotFound();

                return View(viewModel); // Pass to strongly typed view
            }
            catch (Exception ex)
            {

                throw;
            }
        }
        [HttpPost]
        public async Task<IActionResult> InsertInspectionObservationItems([FromBody] MstNewInspectionItemDto dto)
        {
            var result = await _inspectionService.InsertInspectionObservationItemsAsync(dto);
            return StatusCode(result.Status, result);
        }
        [HttpPost]
        public async Task<IActionResult> CreateOrUpdateActionParty([FromBody] MstActionPartyDto dto)
        {
            dto.CreatedBy = int.Parse(HttpContext?.Session?.GetString("UserId"));
            var result = await _inspectionService.CreateOrUpdateActionPartyAsync(dto);
            return StatusCode(result.Status, result);
        }

        [HttpGet]
        public async Task<IActionResult> GetActionPartyByUserId()
        {
            int userId = int.Parse(HttpContext?.Session?.GetString("UserId"));
            var result = await _inspectionService.GetActionPartyByUserIdAsync(userId);
            return StatusCode(result.Status, result);
        }

        [HttpGet]
        public async Task<IActionResult> GetActionPartyById(int id)
        {
            var result = await _inspectionService.GetActionPartyByIdAsync(id);
            return StatusCode(result.Status, result);
        }

        [HttpGet]
        public async Task<IActionResult> GetAllActionPartyByFacilityId(int facilityId)
        {
            var result = await _inspectionService.GetAllActionPartyByFacilityIdAsync(facilityId);
            return StatusCode(result.Status, result);
        }
        [HttpPost]
        public async Task<IActionResult> ToggleEventActivation(int actionPartyId)
        {
            var result = await _inspectionService.ToggleActionPartiesActivationAsync(actionPartyId);
            return StatusCode(result.Status, result);
        }

        [HttpGet]
        public async Task<IActionResult> GetInspectionsByActionParty(int? status)
        {
            int userId = int.Parse(HttpContext?.Session?.GetString("UserId"));

            var result = await _inspectionService.GetInspectionsByActionPartyAsync(userId, status);
            return StatusCode(result.Status, result);
        }
        public async Task<IActionResult> CompleteInspection([FromBody] UpdateInspectionStatusDto dto)
        {
            int userId = int.Parse(HttpContext?.Session?.GetString("UserId"));
            dto.isVerified = false;
            dto.ChangedBy = userId;
            var result = await _inspectionService.UpdateInspectionItemStatusAsync(dto);
            return StatusCode(result.Status, result);
        }

        [HttpGet]
        public async Task<IActionResult> UserInspections()
        {
            var userId = int.Parse(HttpContext?.Session?.GetString("UserId"));

            var userResponse = await _userService.GetUserByIdAsync(userId);
            var InspectionCategory = await _inspectionService.GetInspectionCategoryAsync();
            var viewModel = new AddUserViewModel
            {
                NewUser = userResponse,
                InspectionCategory = InspectionCategory.Data ?? new List<MstInspectionCategoryDto>(),
            };
            return View(viewModel);
        }

        [HttpGet]
        public async Task<IActionResult> GetUserInspections()
        {
            var userIdStr = HttpContext.Session.GetString("UserId");
            var facilityIdsStr = HttpContext.Session.GetString("FacilityIds");
            var isAppAdminStr = HttpContext.Session.GetString("IsAppAdmin");
            var isDepartmentAdminStr = HttpContext.Session.GetString("IsDepartmentAdmin");
            var isFacilityAdminStr = HttpContext.Session.GetString("IsFacilityAdmin");

            if (string.IsNullOrEmpty(userIdStr) || !int.TryParse(userIdStr, out int userId))
            {
                return View(); // Session expired or not logged in
            }

            bool isAppAdmin = bool.TryParse(isAppAdminStr, out var appAdmin) && appAdmin;
            bool isDepartmentAdmin = bool.TryParse(isDepartmentAdminStr, out var deptAdmin) && deptAdmin;
            bool isFacilityAdmin = bool.TryParse(isFacilityAdminStr, out var facAdmin) && facAdmin;

            List<int> facilityIds = new();
            if (!string.IsNullOrEmpty(facilityIdsStr))
            {
                facilityIds = facilityIdsStr
                    .Split(',', StringSplitOptions.RemoveEmptyEntries)
                    .Select(fid => int.TryParse(fid, out var id) ? id : 0)
                    .Where(id => id > 0)
                    .ToList();
            }

            ApiResponseDto<List<MstInspectionDto>> apiResponse;

            if (isAppAdmin || isDepartmentAdmin)
            {
                apiResponse = await _inspectionService.GetUserInspectionsAsync();
            }
            else if (isFacilityAdmin)
            {
                apiResponse = await _inspectionService.GetUserInspectionsAsync(null, facilityIds);
            }
            else
            {
                // Non-admin user: fetch only their own inspections
                apiResponse = await _inspectionService.GetUserInspectionsAsync(null, facilityIds);
            }

            return StatusCode(apiResponse.Status, apiResponse);
        }


        [HttpPost]
        public async Task<IActionResult> UpdateVerificationStatus([FromBody] UpdateInspectionStatusDto dto)
        {
            int userId = int.Parse(HttpContext?.Session?.GetString("UserId"));
            dto.isVerified = true;
            dto.ChangedBy = userId;
            var result = await _inspectionService.UpdateInspectionItemStatusAsync(dto);
            return StatusCode(result.Status, result);
        }

        [HttpDelete]
        public async Task<IActionResult> DeleteObservationItem(int itemId)
        {
            var result = await _inspectionService.DeleteObservationItemAsync(itemId);
            return StatusCode(result.Status, result);
        }

        public async Task<IActionResult> GetInspectionItemById(int itemId)
        {
            var result = await _inspectionService.GetInspectionItemByIdAsync(itemId);
            return StatusCode(result.Status, result);
        }
    }
} 