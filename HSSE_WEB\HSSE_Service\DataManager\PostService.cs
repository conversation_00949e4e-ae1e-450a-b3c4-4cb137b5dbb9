using HSSE_Domain.Models;
using HSSE_Model_Dto.ModelDto;
using HSSE_ModelDto.ModelDto;
using HSSE_Models_Dto.ModelsDto;
using HSSE_Service.Constant;
using HSSE_Service.InterfaceService;
using HSSE_Service.ServiceResponce;
using Microsoft.Extensions.Configuration;
using System;
using System.Net.Http;
using System.Net.Http.Json;
using System.Threading.Tasks;

namespace HSSE_Service.DataManager
{
    public class PostService : IPostService
    {
        private readonly HttpClient _httpClient;
        private readonly string _apiBaseUrl;

        public PostService(IHttpClientFactory httpClientFactory, IConfiguration configuration)
        {
            _httpClient = httpClientFactory.CreateClient("AuthenticatedClient");
            var baseUrl = configuration["ExternalApi:BaseUrl"];
            if (!string.IsNullOrEmpty(baseUrl))
            {
                _httpClient.BaseAddress = new Uri(baseUrl);
            }
            _apiBaseUrl = configuration["ExternalApi:BaseUrl"];
        }

        public async Task<ApiResponseDto<object>> CreatePostAsync(MstPostDto dto)
        {
            try
            {
                var apiUrl = $"{_apiBaseUrl}/api/Posts/CreatePost";
                var response = await _httpClient.PostAsJsonAsync(apiUrl, dto);
                if (response.IsSuccessStatusCode)
                {
                    var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<object>>();
                    return ApiResponseDto<object>.SuccessResponse(apiResult?.Message ?? "Post created successfully.", (int)response.StatusCode, apiResult?.Data);
                }
                else
                {
                    return ApiResponseDto<object>.ErrorResponse("Failed to create post.", (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse("Failed to create post.", 500, new System.Collections.Generic.List<HSSE_Service.ServiceResponce.Error> {
                    new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
                });
            }
        }

        public async Task<ApiResponseDto<object>> InsertOrUpdatePostCategoryAsync(MstPostCategoryDto dto)
        {
            try
            {
                var apiUrl = $"{_apiBaseUrl}/api/Posts/InsertOrUpdatePostCategory";
                var response = await _httpClient.PostAsJsonAsync(apiUrl, dto);
                if (response.IsSuccessStatusCode)
                {
                    var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<object>>();
                    return ApiResponseDto<object>.SuccessResponse(apiResult?.Message ?? "Category saved successfully.", (int)response.StatusCode, apiResult?.Data);
                }
                else
                {
                    return ApiResponseDto<object>.ErrorResponse("Failed to save category.", (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse("Failed to save category.", 500, new System.Collections.Generic.List<HSSE_Service.ServiceResponce.Error> {
                    new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
                });
            }
        }

        public async Task<ApiResponseDto<List<MstPostCategoryDto>>> GetPostCategoriesAsync()
        {
            try
            {
                var apiUrl = $"{_apiBaseUrl}/api/Posts/GetPostCategories";
                var response = await _httpClient.GetAsync(apiUrl);
                if (response.IsSuccessStatusCode)
                {
                    var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<List<MstPostCategoryDto>>>();
                    return ApiResponseDto<List<MstPostCategoryDto>>.SuccessResponse(apiResult?.Message ?? "Categories fetched successfully.", (int)response.StatusCode, apiResult?.Data ?? new List<MstPostCategoryDto>());
                }
                else
                {
                    return ApiResponseDto<List<MstPostCategoryDto>>.ErrorResponse("Failed to fetch categories.", (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<List<MstPostCategoryDto>>.ErrorResponse("Failed to fetch categories.", 500, new System.Collections.Generic.List<HSSE_Service.ServiceResponce.Error> {
                    new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
                });
            }
        }

        public async Task<ApiResponseDto<MstPostCategoryDto>> GetPostCategoryByIdAsync(int catId)
        {
            try
            {
                var apiUrl = $"{_apiBaseUrl}/api/Posts/GetPostCategoryById?id={catId}";
                var response = await _httpClient.GetAsync(apiUrl);
                if (response.IsSuccessStatusCode)
                {
                    var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<MstPostCategoryDto>>();
                    return ApiResponseDto<MstPostCategoryDto>.SuccessResponse(apiResult?.Message ?? "Category fetched successfully.", (int)response.StatusCode, apiResult?.Data);
                }
                else
                {
                    return ApiResponseDto<MstPostCategoryDto>.ErrorResponse("Failed to fetch category.", (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<MstPostCategoryDto>.ErrorResponse("Failed to fetch category.", 500, new System.Collections.Generic.List<HSSE_Service.ServiceResponce.Error> {
                    new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
                });
            }
        }

        public async Task<ApiResponseDto<object>> DeletePostCategoryAsync(int catId)
        {
            try
            {
                var apiUrl = $"{_apiBaseUrl}/api/Posts/DeletePostCategory?id={catId}";
                var response = await _httpClient.DeleteAsync(apiUrl);
                if (response.IsSuccessStatusCode)
                {
                    var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<object>>();
                    return ApiResponseDto<object>.SuccessResponse(apiResult?.Message ?? "Category deleted successfully.", (int)response.StatusCode, apiResult?.Data);
                }
                else
                {
                    return ApiResponseDto<object>.ErrorResponse("Failed to delete category.", (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse("Failed to delete category.", 500, new System.Collections.Generic.List<HSSE_Service.ServiceResponce.Error> {
                    new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
                });
            }
        }


        public async Task<ApiResponseDto<List<MstFacilityDto>>> GetUserFacilityAsync(int userId)
        {
            try
            {
                var apiUrl = $"{_apiBaseUrl}/api/Login/GetUserFacility?userId={userId}";
                var response = await _httpClient.GetAsync(apiUrl);
                if (response.IsSuccessStatusCode)
                {
                    var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<List<MstFacilityDto>>>();
                    return ApiResponseDto<List<MstFacilityDto>>.SuccessResponse(apiResult?.Message ?? "Newsletters fetched successfully.", (int)response.StatusCode, apiResult?.Data ?? new List<MstFacilityDto>());
                }
                else
                {
                    return ApiResponseDto<List<MstFacilityDto>>.ErrorResponse("Failed to fetch newsletters.", (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<List<MstFacilityDto>>.ErrorResponse("Failed to fetch newsletters.", 500, new List<HSSE_Service.ServiceResponce.Error> {
                    new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
                });
            }
        }

        public async Task<ApiResponseDto<List<MstPostDto>>> GetAllPostsAsync()
        {
            try
            {
                var apiUrl = $"{_apiBaseUrl}/api/Posts/GetPostDetails";
                var response = await _httpClient.GetAsync(apiUrl);
                if (response.IsSuccessStatusCode)
                {
                    var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<List<MstPostDto>>>();
                    return ApiResponseDto<List<MstPostDto>>.SuccessResponse(apiResult?.Message ?? "Posts fetched successfully.", (int)response.StatusCode, apiResult?.Data ?? new List<MstPostDto>());
                }
                else
                {
                    return ApiResponseDto<List<MstPostDto>>.ErrorResponse("Failed to fetch posts.", (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<List<MstPostDto>>.ErrorResponse("Failed to fetch posts.", 500, new System.Collections.Generic.List<HSSE_Service.ServiceResponce.Error> {
                    new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
                });
            }
        }

        public async Task<ApiResponseDto<MstPostDto>> GetPostByIdAsync(int postId)
        {
            try
            {
                var apiUrl = $"{_apiBaseUrl}/api/Posts/GetPostDetailsById?id={postId}";
                var response = await _httpClient.GetAsync(apiUrl);
                if (response.IsSuccessStatusCode)
                {
                    var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<MstPostDto>>();
                    return ApiResponseDto<MstPostDto>.SuccessResponse(apiResult?.Message ?? "Post fetched successfully.", (int)response.StatusCode, apiResult?.Data);
                }
                else
                {
                    return ApiResponseDto<MstPostDto>.ErrorResponse("Failed to fetch post.", (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<MstPostDto>.ErrorResponse("Failed to fetch post.", 500, new System.Collections.Generic.List<HSSE_Service.ServiceResponce.Error> {
                    new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
                });
            }
        }

        public async Task<ApiResponseDto<object>> UpdatePostAsync(MstPostDto dto)
        {
            try
            {
                var apiUrl = $"{_apiBaseUrl}/api/Posts/CreatePost";
                var response = await _httpClient.PostAsJsonAsync(apiUrl, dto);
                if (response.IsSuccessStatusCode)
                {
                    var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<object>>();
                    return ApiResponseDto<object>.SuccessResponse(apiResult?.Message ?? "Post updated successfully.", (int)response.StatusCode, apiResult?.Data);
                }
                else
                {
                    return ApiResponseDto<object>.ErrorResponse("Failed to update post.", (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse("Failed to update post.", 500, new System.Collections.Generic.List<HSSE_Service.ServiceResponce.Error> {
                    new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
                });
            }
        }

        public async Task<ApiResponseDto<PaginatedResponseDto<MstPostDto>>> GetPostsByUserIdAsync(
            int? userId,
            bool isAdmin,
            bool isMyPosts,
            int pageNumber = 1,
            int pageSize = 10)
        {
            try
            {
                var apiUrl = $"{_apiBaseUrl}/api/Posts/GetPosts" +
                             $"?userId={(userId.HasValue ? userId.Value.ToString() : "")}" +
                             $"&isAdmin={isAdmin}" +
                             $"&showOnlyMine={isMyPosts.ToString().ToLower()}" +
                             $"&pageNumber={pageNumber}" +
                             $"&pageSize={pageSize}";

                var response = await _httpClient.GetAsync(apiUrl);

                if (response.IsSuccessStatusCode)
                {
                    var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<PaginatedResponseDto<MstPostDto>>>();

                    return ApiResponseDto<PaginatedResponseDto<MstPostDto>>.SuccessResponse(
                        apiResult?.Message ?? "Posts fetched successfully.",
                        (int)response.StatusCode,
                        apiResult?.Data ?? new PaginatedResponseDto<MstPostDto>
                        {
                            Items = new List<MstPostDto>(),
                            PageNumber = pageNumber,
                            PageSize = pageSize,
                            TotalRecords = 0
                        });
                }
                else
                {
                    return ApiResponseDto<PaginatedResponseDto<MstPostDto>>.ErrorResponse(
                        "Failed to fetch posts.",
                        (int)response.StatusCode
                    );
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<PaginatedResponseDto<MstPostDto>>.ErrorResponse(
                    "Failed to fetch posts.",
                    500,
                    new List<HSSE_Service.ServiceResponce.Error>
                    {
                new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
                    });
            }
        }


        public async Task<ApiResponseDto<object>> ToggleLikeAsync(MstLikesConfigDto dto)
        {
            try
            {
                var apiUrl = $"{_apiBaseUrl}/api/Posts/ToggleLike";
                var response = await _httpClient.PostAsJsonAsync(apiUrl, dto);
                if (response.IsSuccessStatusCode)
                {
                    var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<object>>();
                    return ApiResponseDto<object>.SuccessResponse(apiResult?.Message ?? "Toggled like successfully.", (int)response.StatusCode, apiResult?.Data);
                }
                else
                {
                    return ApiResponseDto<object>.ErrorResponse("Failed to toggle like.", (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse("Failed to toggle like.", 500, new System.Collections.Generic.List<HSSE_Service.ServiceResponce.Error> {
                    new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
                });
            }
        }

        public async Task<ApiResponseDto<object>> PostCommentAsync(MstPostCommentDto dto)
        {
            try
            {
                var apiUrl = $"{_apiBaseUrl}/api/Posts/InsertOrUpdatePostComment";
                var response = await _httpClient.PostAsJsonAsync(apiUrl, dto);
                if (response.IsSuccessStatusCode)
                {
                    var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<object>>();
                    return ApiResponseDto<object>.SuccessResponse(apiResult?.Message , (int)response.StatusCode, apiResult?.Data);
                }
                else
                {
                    return ApiResponseDto<object>.ErrorResponse("Failed to Post Comment.", (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse("Failed to Post Comment.", 500, new System.Collections.Generic.List<HSSE_Service.ServiceResponce.Error> {
                    new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
                });
            }
        }
        public async Task<ApiResponseDto<List<MstPostCommentDto>>> GetPostCommentAsync(int postId)
        {
            try
            {
                var apiUrl = $"{_apiBaseUrl}/api/Posts/GetPostCommentByPostId?postId={postId}";
                var response = await _httpClient.GetAsync(apiUrl);
                if (response.IsSuccessStatusCode)
                {
                    var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<List<MstPostCommentDto>>>();
                    return ApiResponseDto<List<MstPostCommentDto>>.SuccessResponse(apiResult?.Message ?? "Posts fetched successfully.", (int)response.StatusCode, apiResult?.Data ?? new List<MstPostCommentDto>());
                }
                else
                {
                    return ApiResponseDto<List<MstPostCommentDto>>.ErrorResponse("Failed to fetch posts.", (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<List<MstPostCommentDto>>.ErrorResponse("Failed to fetch posts.", 500, new System.Collections.Generic.List<HSSE_Service.ServiceResponce.Error> {
                    new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
                });
            }
        }

        public async Task<ApiResponseDto<object>> AssignPostAsync(MstFollowupPostDto dto)
        {
            try
            {
                var apiUrl = $"{_apiBaseUrl}/api/Posts/InsertOrUpdateFollowupPost";
                var response = await _httpClient.PostAsJsonAsync(apiUrl, dto);
                if (response.IsSuccessStatusCode)
                {
                    var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<object>>();
                    return ApiResponseDto<object>.SuccessResponse(apiResult?.Message, (int)response.StatusCode, apiResult?.Data);
                }
                else
                {
                    return ApiResponseDto<object>.ErrorResponse("Failed to Post Comment.", (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse("Failed to Post Comment.", 500, new System.Collections.Generic.List<HSSE_Service.ServiceResponce.Error> {
                    new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
                });
            }
        }
        public async Task<ApiResponseDto<List<MstUserDto>>> GetUserByFacilityIdAsync(int facilityId)
        {
            try
            {
                var apiUrl = $"{_apiBaseUrl}/api/Login/GetUserByFacilityId?facilityId={facilityId}";
                var response = await _httpClient.GetAsync(apiUrl);
                if (response.IsSuccessStatusCode)
                {
                    var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<List<MstUserDto>>>();
                    return ApiResponseDto<List<MstUserDto>>.SuccessResponse(apiResult?.Message ?? "Newsletters fetched successfully.", (int)response.StatusCode, apiResult?.Data ?? new List<MstUserDto>());
                }
                else
                {
                    return ApiResponseDto<List<MstUserDto>>.ErrorResponse("Failed to fetch newsletters.", (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<List<MstUserDto>>.ErrorResponse("Failed to fetch newsletters.", 500, new List<HSSE_Service.ServiceResponce.Error> {
                    new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
                });
            }
        }
        public async Task<ApiResponseDto<List<MstUserDto>>> GetUserByFacilityIdAndUserIdAsync(List<int> facilityIds, int? userId = null)
        {
            try
            {
                string apiUrl = $"{_apiBaseUrl}/api/Login/GetUserByFacilityIdAndUserId";

                var queryParams = new List<string>();

                if (facilityIds != null && facilityIds.Any())
                {
                    queryParams.Add($"facilityIds={string.Join(",", facilityIds)}");
                }

                if (userId.HasValue)
                {
                    queryParams.Add($"userId={userId.Value}");
                }

                if (queryParams.Any())
                {
                    apiUrl += "?" + string.Join("&", queryParams);
                }

                var response = await _httpClient.GetAsync(apiUrl);

                if (response.IsSuccessStatusCode)
                {
                    var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<List<MstUserDto>>>();
                    return ApiResponseDto<List<MstUserDto>>.SuccessResponse(
                        apiResult?.Message ?? "Users fetched successfully.",
                        (int)response.StatusCode,
                        apiResult?.Data ?? new List<MstUserDto>()
                    );
                }
                else
                {
                    return ApiResponseDto<List<MstUserDto>>.ErrorResponse("Failed to fetch users.", (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<List<MstUserDto>>.ErrorResponse("Failed to fetch users.", 500, new List<HSSE_Service.ServiceResponce.Error> {
            new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
        });
            }
        }

        public async Task<ApiResponseDto<List<MstPostDto>>> GetAssignedPostsAsync(int? userId, int statusId)
        {
            try
            {
                var apiUrl = $"{_apiBaseUrl}/api/Posts/GetAssignedPosts?userId={(userId.HasValue ? userId.Value.ToString() : "")}&status={statusId}";
                var response = await _httpClient.GetAsync(apiUrl);
                if (response.IsSuccessStatusCode)
                {
                    var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<List<MstPostDto>>>();
                    return ApiResponseDto<List<MstPostDto>>.SuccessResponse(apiResult?.Message ?? "Posts fetched successfully.", (int)response.StatusCode, apiResult?.Data ?? new List<MstPostDto>());
                }
                else
                {
                    return ApiResponseDto<List<MstPostDto>>.ErrorResponse("Failed to fetch posts.", (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<List<MstPostDto>>.ErrorResponse("Failed to fetch posts.", 500, new System.Collections.Generic.List<HSSE_Service.ServiceResponce.Error> {
                    new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
                });
            }
        }

        public async Task<ApiResponseDto<object>> UpdateFollowUpStatusAsync(FollowupStatusUpdateRequestDto dto)
        {
            try
            {
                var apiUrl = $"{_apiBaseUrl}/api/Posts/UpdateFollowUpStatus";
                var response = await _httpClient.PostAsJsonAsync(apiUrl, dto);
                if (response.IsSuccessStatusCode)
                {
                    var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<object>>();
                    return ApiResponseDto<object>.SuccessResponse(apiResult?.Message, (int)response.StatusCode, apiResult?.Data);
                }
                else
                {
                    return ApiResponseDto<object>.ErrorResponse("Failed to Post Comment.", (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse("Failed to Post Comment.", 500, new System.Collections.Generic.List<HSSE_Service.ServiceResponce.Error> {
                    new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
                });
            }
        }
        public async Task<ApiResponseDto<object>> DeletePostAsync(int postId, int deletedBy)
        {
            try
            {
                var apiUrl = $"{_apiBaseUrl}/api/Posts/DeletePost?postId={postId}&deletedBy={deletedBy}";
                var response = await _httpClient.DeleteAsync(apiUrl);
                if (response.IsSuccessStatusCode)
                {
                    var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<object>>();
                    return ApiResponseDto<object>.SuccessResponse(apiResult?.Message ?? "Post deleted successfully.", (int)response.StatusCode, apiResult?.Data);
                }
                else
                {
                    return ApiResponseDto<object>.ErrorResponse("Failed to delete post.", (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse("Failed to delete post.", 500, new System.Collections.Generic.List<HSSE_Service.ServiceResponce.Error> {
                    new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
                });
            }
        }


    }
} 