using HSSE_Models_Dto.ModelsDto;
using HSSE_Service.ServiceResponce;
using System.Threading.Tasks;

namespace HSSE_Service.InterfaceService
{
    public interface IFeedbackService
    {
        Task<ApiResponseDto<object>> CreateFeedbackAsync(MstFeedbackDto feedbackDto);
        Task<ApiResponseDto<List<MstFeedbackDto>>> GetFeedbackAsync(int? userId, int? facilityId);
        Task<ApiResponseDto<MstFeedbackDto>> GetFeedbackByIdAsync(int id);
        Task<ApiResponseDto<object>> GetFeedbackByFacilityAndUserAsync(int? facilityId, int? userId);
        Task<ApiResponseDto<object>> UpdateFeedbackWithResponseAsync(MstFeedbackResponseDto feedbackDto);

    }
} 