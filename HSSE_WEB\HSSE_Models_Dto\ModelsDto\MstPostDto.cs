using System;

namespace HSSE_Models_Dto.ModelsDto
{
    public class MstPostDto
    {
        public int PostId { get; set; }
        public int UserId { get; set; }
        public int? FacilityId { get; set; }
        public string Title { get; set; } = null!;
        public string? Description { get; set; }
        public string PostType { get; set; } = null!;
        public string? Location { get; set; }
        public int? TaggedCategoryId { get; set; }
        public string? RequiresFollowup { get; set; }
        public int? Status { get; set; }
        public DateTime? CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public int? ClosedBy { get; set; }
        public int? DeletedBy { get; set; }
        public string? ImageBase64 { get; set; }
        public string? FileName { get; set; }
        public bool IsLikedByUser { get; set; }
        public int? LikeCount { get; set; }
        public int? PostCommentsCount { get; set; }
        public bool IsAuthor { get; set; }
        public bool IsAssigned { get; set; }
        public List<string>? AssignedToUsername { get; set; }
        public string? FacilityName { get; set; }
        public string? AssignedByUsername { get; set; }
        public string? CompletedBy { get; set; }
        public string? CompletedComments { get; set; }
        public List<string> AfterMediaUrls { get; set; } = new List<string>();

    }
} 