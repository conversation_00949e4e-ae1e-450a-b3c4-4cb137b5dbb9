using HSSE_Models_Dto.ModelsDto;
using HSSE_Service.Constant;
using HSSE_Service.DataManager;
using HSSE_Service.InterfaceService;
using HSSE_Service.ServiceResponce;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace HSSE_WEB.Controllers
{
    public class FeedbackController : Controller
    {
        private readonly IFeedbackService _feedbackService;
        private readonly IFacilityService _facilityService;

        public FeedbackController(IFeedbackService feedbackService, IFacilityService facilityService)
        {
            _feedbackService = feedbackService;
            _facilityService = facilityService;
        }

        [HttpGet]
        public async Task<IActionResult> CreateFeedback()
        {
            // You might need to pass data to the view, e.g., list of facilities
            return View();
        }
        public async Task<IActionResult> UpdateFeedback()
        {
            // You might need to pass data to the view, e.g., list of facilities
            return View();
        }
        [HttpPost]
        public async Task<IActionResult> CreateFeedback([FromBody] MstFeedbackDto feedbackDto)
        {
            if (feedbackDto == null)
            {
                return BadRequest(ApiResponseDto<bool>.ErrorResponse("Invalid feedback data", StatusCodes.Status400BadRequest));
            }

            var userId = int.Parse(HttpContext?.Session?.GetString("UserId"));
            feedbackDto.Date = DateTime.Now;
            feedbackDto.Status = 0;
            feedbackDto.CreatedBy = userId; 

            var result = await _feedbackService.CreateFeedbackAsync(feedbackDto);

            return StatusCode(result.Status, result);
        }

         // Action to get facilities for dropdown
        [HttpGet]
        public async Task<IActionResult> GetAllFacilities()
        {
            var facilities = await _facilityService.GetAllFacilityAsync(); // Assuming this method exists
            return Json(facilities);
        }
        [HttpGet]
        public async Task<IActionResult> GetFeedbackByFacilityAndUser()
        {
            var facilities = await _feedbackService.GetFeedbackByFacilityAndUserAsync(null, null); // Assuming this method exists
            return Json(facilities);
        }
        [HttpGet]
        public async Task<IActionResult> GetFeedback()
        {
            var userId = int.Parse(HttpContext?.Session?.GetString("UserId"));

            var facilities = await _feedbackService.GetFeedbackByFacilityAndUserAsync(null, userId); // Assuming this method exists
            return Json(facilities);
        }

        [HttpGet]
        public async Task<IActionResult> GetFeedbackById(int id)
        {
            var result = await _feedbackService.GetFeedbackByIdAsync(id);
            return StatusCode(result.Status, result);
        }
        [HttpPost]
        public async Task<IActionResult> UpdateFeedbackWithResponse([FromBody] MstFeedbackResponseDto data)
        {
            try
            {
                data.userId =  int.Parse(HttpContext?.Session?.GetString("UserId"));
                var result = await _feedbackService.UpdateFeedbackWithResponseAsync(data);
                return StatusCode(result.Status, result);
            }
            catch (Exception ex)
            {
                return StatusCode(500, ApiResponseDto<object>.ErrorResponse(ApiMessages.GeneralError, 500));
            }
        }
    }
} 