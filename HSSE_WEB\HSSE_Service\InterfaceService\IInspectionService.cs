using HSSE_Models_Dto.ModelsDto;
using HSSE_Service.ServiceResponce;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace HSSE_Service.InterfaceService
{
    public interface IInspectionService
    {
        Task<ApiResponseDto<object>> InsertOrUpdateInspectionAsync(MstNewInspectionDto dto);
        Task<ApiResponseDto<List<MstInspectionDto>>> GetInspectionsAsync(int userId);
        Task<ApiResponseDto<MstNewInspectionDto>> GetInspectionByIdAsync(int inspectionId);
        Task<ApiResponseDto<object>> CreateOrUpdateActionPartyAsync(MstActionPartyDto dto);
        Task<ApiResponseDto<object>> GetActionPartyByIdAsync(int id);
        Task<ApiResponseDto<List<MstActionPartyDto>>> GetAllActionPartyByFacilityIdAsync(int facilityId);
        Task<ApiResponseDto<List<ActionPartyViewDto>>> GetActionPartyByUserIdAsync(int userId);
        Task<ApiResponseDto<object>> ToggleActionPartiesActivationAsync(int actionPartyId);
        Task<ApiResponseDto<List<MstInspectionDto>>> GetInspectionsByActionPartyAsync(int actionPartyId, int? status);
        Task<ApiResponseDto<object>> UpdateInspectionItemStatusAsync(UpdateInspectionStatusDto dto);
        Task<ApiResponseDto<List<MstInspectionCategoryDto>>> GetInspectionCategoryAsync();
        Task<ApiResponseDto<List<MstInspectionDto>>> GetUserInspectionsAsync(int? userId = null, List<int>? facilityId = null);
        Task<ApiResponseDto<object>> InsertInspectionObservationItemsAsync(MstNewInspectionItemDto dto);
        Task<ApiResponseDto<object>> DeleteObservationItemAsync(int itemId);
        Task<ApiResponseDto<MstNewInspectionItemDto>> GetInspectionItemByIdAsync(int itemId);
    }
} 