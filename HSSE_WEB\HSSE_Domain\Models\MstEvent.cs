﻿using System;
using System.Collections.Generic;

namespace HSSE_Domain.Models;

public partial class MstEvent
{
    public int EventId { get; set; }

    public string Title { get; set; } = null!;

    public string? Description { get; set; }

    public string? MediaUrl { get; set; }

    public DateTime? EventDateTime { get; set; }

    public string? Location { get; set; }

    public string? ExternalLink { get; set; }

    public DateTime? CreatedAt { get; set; }

    public int? CreatedBy { get; set; }

    public int? FacilityId { get; set; }

    public bool IsActive { get; set; }

    public DateTime? ScheduleAt { get; set; }

    public DateTime? ExpiryAt { get; set; }

    public bool IsRsvp { get; set; }

    public int? ModifiedBy { get; set; }

    public DateTime? ModifiedAt { get; set; }

    public virtual MstFacility? Facility { get; set; }

    public virtual ICollection<MstAnnouncementReceiver> MstAnnouncementReceivers { get; set; } = new List<MstAnnouncementReceiver>();

    public virtual ICollection<MstEventResponse> MstEventResponses { get; set; } = new List<MstEventResponse>();

    public virtual ICollection<MstLikesConfig> MstLikesConfigs { get; set; } = new List<MstLikesConfig>();
}
