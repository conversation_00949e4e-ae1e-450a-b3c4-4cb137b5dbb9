﻿using System;
using System.Collections.Generic;

namespace HSSE_Domain.Models;

public partial class MstPost
{
    public int PostId { get; set; }

    public int UserId { get; set; }

    public int? FacilityId { get; set; }

    public string Title { get; set; } = null!;

    public string? Description { get; set; }

    public string PostType { get; set; } = null!;

    public string? Location { get; set; }

    public int? TaggedCategoryId { get; set; }

    public string? RequiresFollowup { get; set; }

    public int? Status { get; set; }

    public DateTime? CreatedAt { get; set; }

    public DateTime? UpdatedAt { get; set; }

    public int? ClosedBy { get; set; }

    public int? DeletedBy { get; set; }

    public bool IsDeleted { get; set; }

    public string? ClosedDescription { get; set; }

    public DateTime? ModifiedAt { get; set; }

    public int? ModifiedBy { get; set; }

    public virtual MstFacility? Facility { get; set; }

    public virtual ICollection<MstFollowupPost> MstFollowupPosts { get; set; } = new List<MstFollowupPost>();

    public virtual ICollection<MstLikesConfig> MstLikesConfigs { get; set; } = new List<MstLikesConfig>();

    public virtual ICollection<MstPostComment> MstPostComments { get; set; } = new List<MstPostComment>();

    public virtual ICollection<MstPostMedium> MstPostMedia { get; set; } = new List<MstPostMedium>();

    public virtual MstPostCategory? TaggedCategory { get; set; }

    public virtual MstUser User { get; set; } = null!;
}
