﻿@model HSSE_Models_Dto.ViewModels.AddUserViewModel
@{
    ViewBag.Title = "Add User";
    Layout = "~/Views/Shared/_Layout.cshtml"; // Or your correct layout path
}
<div class="col-12 grid-margin" data-permission="create">
    <div class="card">
        <div class="card-body">
            <h4 class="card-title">Add New User</h4>
            <form class="form-sample" method="post" enctype="multipart/form-data">
                <p class="card-description">User Details</p>

               <div class="row">
                    <div class="col-md-6">
                        <div class="form-group row">
                            <label class="col-sm-3 col-form-label">User's Email<span class="text-danger">*</span></label>
                            <div class="col-sm-9">
                                <input type="email" id="usersEmail" name="Email" class="form-control" data-required="true" data-permission="create" autocomplete="off" />
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group row">
                            <label class="col-sm-3 col-form-label">Password<span class="text-danger">*</span></label>
                            <div class="col-sm-9 ">
                                <input type="password" id="password" name="Password" class="form-control file-upload-info" data-required="true" data-permission="create" autocomplete="new-password" />
                                <span class="input-group-append">
                                    <i class="fa fa-eye-slash position-absolute" id="togglePassword"
                                       style="top: 34%; right: 19px; transform: translateY(-50%); cursor: pointer;"></i>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
                <br />   <br />

                <!-- Existing Form Fields -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group row">
                            <label class="col-sm-3 col-form-label">First Name<span class="text-danger">*</span></label>
                            <div class="col-sm-9">
                                <input type="text" id="FirstName" name="FirstName" class="form-control" data-required="true" data-permission="create" />
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group row">
                            <label class="col-sm-3 col-form-label">Last Name<span class="text-danger">*</span></label>
                            <div class="col-sm-9">
                                <input type="text" id="lastName" name="LastName" class="form-control" data-permission="create" />
                            </div>
                        </div>
                    </div>
                </div>

                <!-- New Dropdown for Role -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group row">
                            <label class="col-sm-3 col-form-label">Select Role<span class="text-danger">*</span></label>
                            <div class="col-sm-9">
                                <select name="RoleId" id="RoleSelect" class="form-control" data-permission="create">
                                    <option value="">-- Select Role --</option>
                                    @foreach (var role in Model.Roles)
                                    {
                                        <option value="@role.RoleId">@role.RoleName</option>
                                    }
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- New Dropdown for Facility -->
                    <div class="col-md-6">
                       <div class="form-group row">
                            <label class="col-sm-3 col-form-label">Select Facility<span class="text-danger">*</span></label>
                            <div class="col-sm-9">
                                <select name="FacilityIds" id="selectFacility" class="js-example-basic-multiple w-100" multiple="multiple" data-permission="create">
                                    <option value="">--Select Facility--</option>

                                    @foreach (var fac in Model.Facilities)
                                    {
                                        <option value="@fac.FacilityId">@fac.FacilityName</option>
                                    }
                                </select>

                                </div>
                        </div>
                    </div>

                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group row">
                            <label class="col-sm-3 col-form-label">Bio</label>
                            <div class="col-sm-9">
                                <textarea name="Bio" class="form-control" style="resize: vertical;" data-permission="create"></textarea>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group row">
                            <label class="col-sm-3 col-form-label">Select Language<span class="text-danger">*</span></label>
                            <div class="col-sm-9">
                                <select name="language" id="language" class="form-control" data-permission="create">
                                    <option value="">--Select Language--</option>

                                    @foreach (var fac in Model.Languages)
                                    {
                                        <option value="@fac.Id">@fac.Language</option>
                                    }
                                </select>

                            </div>
                        </div>
                    </div>
               
                </div>
                <div class="row">
                    <div class="col-md-6">
                        @*   <div class="form-group row">
                            <label class="col-sm-3 col-form-label">Is SsoUser</label>
                            <div class="form-check">
                                <label class="form-check-label">
                                    <input type="checkbox" class="form-check-input" name="IsSsoUser" data-permission="create" />
                                </label>
                            </div>
                         </div> *@
                        <div class="form-group row">
                            <label class="col-sm-3 col-form-label">Is Active</label>
                            <div class="form-check">
                                <label class="form-check-label">
                                    <input type="checkbox" class="form-check-input" name="IsActive" data-permission="create" checked />
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group row">
                            <label class="col-sm-3 col-form-label">Profile Image</label>

                            <div class="col-sm-9">
                                <div class="custom-file-upload">
                                    <input type="file" class="d-none" id="ProfileImageUpload" name="ProfileImage" accept="image/*" data-permission="create" />
                                    <div class="input-group">
                                        <input type="text" class="form-control file-upload-info" id="ProfileImageFileName" readonly placeholder="Choose Image" />
                                        <div class="input-group-append">
                                            <button class="btn btn-primary btn-sm" type="button" id="uploadProfileImageBtn">Upload</button>
                                            <button class="btn btn-danger btn-sm ml-1 d-none" type="button" id="removeProfileImageBtn">Remove</button>
                                        </div>
                                    </div>
                                    <div class="mt-2 d-none" id="ProfileImagePreviewContainer">
                                        <img id="ProfileImagePreview" src="" class="img-thumbnail" style="max-width: 250px;" />
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
                <div class="row">

                
                </div>

           
                <div class="row">
                    <div class="col-md-12 text-end">
                        <button type="submit" id="submit-btn" class="btn btn-outline-primary btn-icon-text" data-permission="create">
                            <i class="mdi mdi-file-check btn-icon-prepend"></i>Submit
                        </button>
                        <button type="reset" class="btn btn-light" data-permission="create">Cancel</button>
                    </div>
                </div>

            </form>
        </div>
    </div>
</div>


<!-- User List Table -->
<div class="col-12 grid-margin">
    <div class="card" data-permission="view">
            <div class="card-body">
                <h4 class="card-title">User List</h4>
                <div class="row">
                    <div class="col-12">
                        <div class="table-responsive">
                        <table id="order-listing" class="table">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>Username</th>
                                        <th>First Name</th>
                                        <th>Last Name</th>
                                        <th>User Type</th>
                                        <th>Active</th>
                                    <th data-permission="edit">Actions</th>
                                    </tr>
                                </thead>
                            <tbody>
                                @{
                                    int index = 1;
                                }
                                @foreach (var user in Model.ExistingUsers)
                                {
                                    <tr>
                                        <td>@index</td>
                                        <td>@user.Email</td>
                                        <td>@user.FirstName</td>
                                        <td>@user.LastName</td>
                                        <td>
                                            @if (user.IsSsoUser == true)
                                            {
                                                <label class="badge badge-info">SSO User</label>
                                            }
                                            else
                                            {
                                                <label class="badge badge-secondary">Custom</label>
                                            }
                                        </td>
                                        <td>
                                            @if (user.IsActive != null && user.IsActive == true)
                                            {
                                                <label class="badge badge-success">Active</label>
                                            }
                                            else
                                            {
                                                <label class="badge badge-danger">Inactive</label>
                                            }
                                        </td>
                                        <td>
                                            <button type="button" class="btn btn-sm btn-outline-primary edit-user-btn"
                                                    data-user-id="@user.UserId" data-toggle="modal"
                                                    data-target="#editUserModal" title="Edit"
                                                    data-permission="edit">
                                                <i class="mdi mdi-pencil"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    index++;
                                }
                            </tbody>

                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
</div>

<!-- Edit User Modal -->
<div class="modal fade" id="editUserModal" tabindex="-1" role="dialog" aria-labelledby="editUserLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">

            <div class="modal-header">
                <h5 class="modal-title" id="editUserLabel">Edit User</h5>
                <button type="button" class="close" data-dismiss="modal"><span>&times;</span></button>
            </div>

            <div class="modal-body">
                    <input type="hidden" id="editUserId" name="UserId" />

                <div class="row">
                    <div class="form-group col-md-6">
                            <label>User's Email</label>
                            <input type="email" id="editUserEmail" name="Email" class="form-control" data-permission="edit" />
                        </div>
                        <div class="form-group col-md-6">
                            <label>Password</label>
                        <div class="col-sm-12 p-0">
                        <input type="password" id="editUserPassword" name="Password" class="form-control file-upload-info" data-permission="edit" />
                        <span class="input-group-append">
                            <i class="fa fa-eye-slash position-absolute" id="editTogglePassword"
                               style="top: 50%; right: 19px; transform: translateY(-50%); cursor: pointer;"></i>
                        </span>
                        </div>
                        <small id="editPasswordFeedback" class="text-danger d-block mt-1"></small>

                    </div>

                        </div>
                <div class="row">
                        <div class="form-group col-md-6">
                            <label>First Name</label>
                            <input type="text" id="editFirstName" name="FirstName" class="form-control" data-permission="edit" />
                        </div>
                        <div class="form-group col-md-6">
                            <label>Last Name</label>
                            <input type="text" id="editLastName" name="LastName" class="form-control" data-permission="edit" />
                        </div>
                </div>
                <div class="row">

                    <div class="form-group col-md-6">
                        <label>Select Language</label>

                        <select name="language" id="editLanguage" class="form-control" data-permission="create">
                            <option value="">--Select Language--</option>
                            @foreach (var fac in Model.Languages)
                            {
                                <option value="@fac.Id">@fac.Language</option>
                            }
                        </select>


                    </div>
                    <div class="form-group col-md-6">
                        <label>Contact</label>
                        <input type="text" id="editContact" name="Contact" class="form-control" data-permission="edit" />
                    </div>
                </div>
                <div class="row">
                    <div class="form-group col-md-6">
                        <label>Employee Code</label>
                        <input type="text" id="editEmpCode" name="EmpCode" class="form-control" data-permission="edit" />
                    </div>
                    <div class="form-group col-md-6">
                        <label>Bio</label>
                        <textarea id="editBio" name="Bio" class="form-control" data-permission="edit"></textarea>
                      
                    </div>
                </div>
              <div class="row">
                    <div class="form-group col-md-6">
                        <label>Profile Image</label>
                        <div>
                            <div class="custom-file-upload">
                                <input type="hidden" id="ExistingThumbnailPath" />

                                <input type="file" class="d-none" id="editProfileImageInput" name="ProfileImage" accept="image/*" data-permission="create" />
                                <div class="input-group">
                                    <input type="text" class="form-control file-upload-info" id="editProfileImageFileName" readonly placeholder="Choose Image" />
                                    <div class="input-group-append">
                                        <button class="btn btn-primary btn-sm" type="button" id="editProfileImageBtn">Upload</button>
                                        <button class="btn btn-danger btn-sm ml-1 d-none" type="button" id="removeEditProfileImageBtn">Remove</button>
                                    </div>
                                </div>
                                <div class="mt-2 d-none" id="editProfileImagePreviewContainer">
                                    <img id="editProfileImagePreview" src="" class="img-thumbnail" style="max-width: 250px;" />
                                </div>
                            </div>
                        </div>

                    </div>
                    <div class="row col-md-4 ml-0 mt-4">
                        <div class="form-group col-md-4 form-check">
                            <label class="form-check-label">
                                <input type="checkbox" id="editIsActive" name="IsActive" class="form-check-input" data-permission="edit" /> Is Active
                            </label>
                        </div>
                        <div class="form-group col-md-4 form-check form-check-info">
                            <label class="form-check-label">
                                <input type="checkbox" id="editIsSsoUser" class="form-check-input" name="editIsSsoUser" data-permission="edit"
                                       onkeydown="return false;" onclick="return false;" /> SSO User
                            </label>
                        </div>
                    </div>
                </div>
              
                <div class="row">
                    <div class="col-md-12">
                        <button class="btn btn-success mb-2" id="addRowBtn">Map Roles</button>
                        <table id="facilityRoleTable" class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>Facility</th>
                                    <th>Role</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </div>
                      
                 

                <!-- Add New Button -->

            </div>


            <div class="modal-footer">
                <button type="button" id="saveEditUserBtn" class="btn btn-success" data-permission="edit">Save</button>
                <button type="button" class="btn btn-light" data-dismiss="modal">Cancel</button>
            </div>

        </div>
    </div>
</div>

@* @section Scripts {
    @{
        await Html.RenderPartialAsync("_ValidationScriptsPartial");
    }
} *@
@section Scripts {
    <script>
        const allRoles = @Html.Raw(Json.Serialize(Model.Roles));
        const allFacilities = @Html.Raw(Json.Serialize(Model.Facilities));
         var createUser = '@Url.Action("CreateUser", "User")';
         var getUserById = '@Url.Action("GetUserById", "User")';
                  var saveUserWithRoles = '@Url.Action("SaveUserWithRoles", "User")';
        var baseUrl = '@Url.Content("~/")';
    </script>
    <script src="~/js/User/addUser.js"></script>
}