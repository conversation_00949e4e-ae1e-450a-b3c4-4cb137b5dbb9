using HSSE_Models_Dto.ModelsDto;
using HSSE_Service.DataManager;
using HSSE_Service.Helper;
using HSSE_Service.InterfaceService;
using HSSE_Service.ServiceResponce;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System.Threading.Tasks;

namespace HSSE_WEB.Controllers
{
    public class PermissionController : Controller
{
    private readonly IPermissionService _permissionService;
    private readonly IUserRoleService _userRoleService;

        public PermissionController(IPermissionService permissionService, IUserRoleService userRoleService)
        {
            _permissionService = permissionService;
            _userRoleService = userRoleService;
        }

        // Child action to get sidebar menu
        [HttpGet]
        public async Task<IActionResult> GetPermissions()
        {
            var roleIdsStr = HttpContext.Session.GetString("RoleIds");

            if (string.IsNullOrEmpty(roleIdsStr))
                return Unauthorized("No roles found in session");

            // Parse role IDs from session
            List<int> roleIds = roleIdsStr.Split(',', StringSplitOptions.RemoveEmptyEntries)
                .Select(id => int.TryParse(id, out var fid) ? fid : 0)
                .Where(fid => fid > 0)
                .ToList();


            // Define known roles
            var knownRoles = new List<int> { 1, 2, 5, 6, 7 };

            // Filter out unknown roles and treat them as RoleId = 2 (User)
            var validRoleIds = roleIds.Where(id => knownRoles.Contains(id)).ToList();

            if (!validRoleIds.Any())
            {
                // No known role, default to User role (2)
                validRoleIds.Add(2);
            }

            // Select highest priority role
            var selectedRole = RoleHelper.GetHighestPriorityRole(validRoleIds);
            if (selectedRole == null)
                return NotFound("No valid role found");

            var result = await _permissionService.GetPermissionsByUserIdAsync((int)selectedRole.Value);
            return Json(result.Data);
        }

        [HttpGet]
        public async Task<IActionResult> GetSideBarPermissions(int roleId)
        {
            var result = await _permissionService.GetPermissionsByUserIdAsync(roleId);

            return Json(result.Data); // Only returning the permission list
        }
        [HttpGet]
        public async Task<IActionResult> PermissionDashboard()
        {
            return View();
        }

        [HttpGet]
        public async Task<IActionResult> CreatePermission()
        {
            return View();
        }
        [HttpGet]
        public async Task<IActionResult> MapPermission()
        {
            return View();
        }
        public async Task<IActionResult> UpdatePermissionOrder([FromBody] List<PermissionOrderUpdateDto> updates)
        {
            var result = await _permissionService.UpdatePermissionOrderAsync(updates);
            return StatusCode(result.Status, result);
        }
        [HttpGet]
        public async Task<IActionResult> GetUserRoles()
        {
            //int roleId = 1; // Static for testing
            var result = await _userRoleService.GetAllUserRoleAsync();
            return Json(result); // return only the list
        }
        public async Task<IActionResult> UpdatePermissionForRole([FromBody] UpdatePermissionRequest request)
        {
            if (request == null)
            {
                return BadRequest(ApiResponseDto<bool>.ErrorResponse("Request payload is null", StatusCodes.Status400BadRequest));
            }

            var result = await _permissionService.UpdatePermissionForRoleAsync(request);

            return StatusCode(result.Status, result);
        }

        [HttpGet]
        public async Task<IActionResult> GetParentMenus()
        {
            //int roleId = 1; // Static for testing
            var result = await _permissionService.GetParentMenusAsync();
            return Json(result); // return only the list
        }

        [HttpGet]
        public async Task<IActionResult> GetChildMenusByParent(int parentId)
        {
            var menus = await _permissionService.GetChildMenusByParentAsync(parentId);
            return Ok(menus);
        }
        public async Task<IActionResult> MapPermissionForRole([FromBody] UpdatePermissionRequest request)
        {
            if (request == null)
            {
                return BadRequest(ApiResponseDto<bool>.ErrorResponse("Request payload is null", StatusCodes.Status400BadRequest));
            }

            var result = await _permissionService.MapPermissionToRoleAsync(request);

            return StatusCode(result.Status, result);
        }

        [HttpPost]
        public async Task<IActionResult> CreatePermission([FromBody] PermissionDto permission)
        {
            if (permission == null)
            {
                return BadRequest(ApiResponseDto<bool>.ErrorResponse("Invalid permission data", StatusCodes.Status400BadRequest));
            }

            var result = await _permissionService.CreatePermissionAsync(permission);
            return StatusCode(result.Status, result);
        }

        [HttpGet]
        public async Task<IActionResult> GetPermissionById(int id)
        {
            var result = await _permissionService.GetPermissionByIdAsync(id);
            return StatusCode(result.Status, result);
        }

        [HttpPost]
        public async Task<IActionResult> TogglePermissionActiveStatus([FromBody] TogglePermissionActiveStatusRequest request)
        {
            if (request == null || request.PermissionId <= 0)
            {
                return BadRequest(ApiResponseDto<bool>.ErrorResponse("Invalid request data", StatusCodes.Status400BadRequest));
            }

            var result = await _permissionService.TogglePermissionActiveStatusAsync(request.PermissionId, request.IsActive);
            return StatusCode(result.Status, result);
        }

        [HttpPost]
        public async Task<IActionResult> UpdatePermissionDetails([FromBody] PermissionDto permission)
        {
            if (permission == null || permission.PermissionId <= 0)
            {
                return BadRequest(ApiResponseDto<bool>.ErrorResponse("Invalid permission data", StatusCodes.Status400BadRequest));
            }

            var result = await _permissionService.UpdatePermissionDetailsAsync(permission);
            return StatusCode(result.Status, result);
        }

        [HttpGet]
        public async Task<IActionResult> GetAllPermissions(string filterType = "all")
        {
            var result = await _permissionService.GetAllPermissionsAsync(filterType);
            return StatusCode(result.Status, result);
        }

        [HttpGet]
        public IActionResult EditPermissions()
        {
            return View();
        }

        [HttpDelete]
        public async Task<IActionResult> DeletePermissionMapping(int permissionId, int roleId)
        {
            if (permissionId <= 0 || roleId <= 0)
            {
                return BadRequest(ApiResponseDto<bool>.ErrorResponse("Invalid permission ID or role ID", StatusCodes.Status400BadRequest));
            }

            var result = await _permissionService.DeletePermissionMappingAsync(permissionId, roleId);
            return StatusCode(result.Status, result);
        }
    }

    // Add a simple DTO for the toggle status request
    public class TogglePermissionActiveStatusRequest
    {
        public int PermissionId { get; set; }
        public bool IsActive { get; set; }
    }
} 