@model HSSE_Models_Dto.ViewModels.AddUserViewModel

@{
    ViewData["Title"] = "View Events";
}

<div class="card mt-4">
    <div class="card-body">
        <div class="d-flex justify-content-between align-items-center">
            <h4 class="card-title">Events</h4>
            <div class="form-inline">
                <button id="btn-toggle-view" class="btn btn-primary btn-sm p-2">
                    <i class="fa fa-table"></i> Table View
                </button>
            </div>
        </div>
    <!-- Filters Section -->
    <div class="row mb-3">
        <div class="col-md-4">
            <input type="text" id="searchInput" class="form-control" placeholder="Search by title..." />
        </div>

        <div class="form-group col-md-4">
            <select id="dateFilter" class="form-control">
                <option value="0">All Dates</option>
                <option value="1">Today</option>
                <option value="2">Tomorrow</option>
                <option value="3">This Week</option>
            </select>
        </div>

   @*      <div class="form-group row">
            <select id="isOnlineFilter" class="form-control">
                <option value="">All Modes</option>
                <option value="true">Online</option>
                <option value="false">Offline</option>
            </select>
        </div>

        <div class="col-md-2">
            <button class="btn btn-primary w-100" onclick="applyFilters()">Apply Filters</button>
        </div> *@
    </div>

    <!-- Events Display Section -->
        <div id="eventsContainer" class="row py-4">
        <!-- Event cards will be injected here -->
    </div>
    </div>
</div>
<!-- RSVP Modal -->
<div class="modal fade" id="rsvpModal" tabindex="-1" aria-labelledby="rsvpModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="rsvpModalLabel">RSVP Confirmation</h5>
                <button type="button" class="close" data-dismiss="modal"><span>&times;</span></button>
            </div>
            <div class="modal-body">
                <p><strong>User:</strong> <span id="modalUserName"></span></p>
                <p><strong>Event:</strong> <span id="modalEventTitle"></span></p>
                <p>Do you want to confirm your RSVP?</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-danger" id="btnCancelRsvp" data-dismiss="modal">Cancel Registration</button>
                <button type="button" class="btn btn-outline-success" id="btnAcceptRsvp">Register</button>
            </div>
        </div>
    </div>
</div>
<!-- Event Detail Modal -->
<div class="modal fade" id="eventDetailModal" tabindex="-1" aria-labelledby="eventDetailModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="eventDetailModalLabel">Event Details</h5>
                <button type="button" class="close" data-dismiss="modal"><span>&times;</span></button>
            </div>
            <div class="modal-body">
                <!-- Filled dynamically -->
                <div id="modalEventContent"></div>
            </div>
        </div>
    </div>
</div>


<script>
          const loggedInUserName = '@(Model.NewUser?.Username ?? "Unknown User")';

    var getEventsByFacilityId = '@Url.Action("GetEventsByFacilityId", "Event")';
    var saveEventResponse = '@Url.Action("SaveEventResponse", "Event")';
   const basePath = '@Url.Content("~/")'; // returns '/UEMS/HSSEWeb/' in production
var defaultIconUrl = '@Url.Content("~/Content/images/Image_not_available.jpg")';

</script>

@section Scripts {
    <script src="~/js/Events/viewEvents.js"></script>
}
