﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HSSE_Models_Dto.ModelsDto
{
    public class MstUserDto
    {
        public int UserId { get; set; }

        public string Username { get; set; } = null!;

        public string FirstName { get; set; } = null!;

        public string LastName { get; set; } = null!;

        public string Email { get; set; } = null!;

        public string Password { get; set; } = null!;

        public string? ProfileImageUrl { get; set; }
        public string? ExistingProfileImageUrl { get; set; }

        public string? Bio { get; set; }

        public int? PrimaryFacilityId { get; set; }

        public DateTime? CreatedAt { get; set; }

        public int? CreatedBy { get; set; }

        public DateTime? LastLogin { get; set; }

        public bool? IsActive { get; set; }

        public int? SsoUserId { get; set; }
        public bool? IsSsoUser { get; set; }
        public int ? RoleId { get; set; }
        public int? Language { get; set; }
        public List<int>? FacilityIds { get; set; }
        public string? EmployeeCode { get; set; }

        public string? ContactNumber { get; set; }
        public List<MstUserRolesConfigDto>? facilityRoleConfigs { get; set; }
        public List<int>? RemovedUserRoleConfigIds { get; set; } 




    }
    public class UserInfo
    {
        public int UserId { get; set; }
        public string UserName { get; set; }
        public string UserEmail { get; set; }
        public int RoleId { get; set; }
        public string FacilityList { get; set; }
        public string Language { get; set; }
        public bool IsAdmin { get; set; }
    }

    public class LoginResponseDto
    {
        public string Token { get; set; }
        public int UserId { get; set; }
        public string Username { get; set; }
        public string Email { get; set; }
        public List<int>? FacilityIds { get; set; }

        public List<int>? RoleIds { get; set; }
        public bool IsAppAdmin { get; set; }
        public bool IsDepartmentAdmin { get; set; }
        public bool IsManager { get; set; }
        public bool IsFacilityAdmin { get; set; }
        public List<int>? FacilityAdminFacilityIds { get; set; }
        public List<int>? ManagerFacilityIds { get; set; }
    }

}
