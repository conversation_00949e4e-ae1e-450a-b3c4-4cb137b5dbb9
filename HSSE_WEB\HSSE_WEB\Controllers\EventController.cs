using HSSE_Models_Dto.ModelsDto;
using HSSE_Models_Dto.ViewModels;
using HSSE_Service.DataManager;
using HSSE_Service.InterfaceService;
using HSSE_Service.ServiceResponce;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace HSSE_WEB.Controllers
{
    public class EventController : Controller
    {
        private readonly IEventService _eventService;
        private readonly IGroupService _groupService;
        private readonly IUserService _userService;


        public EventController(IEventService eventService, IGroupService groupService, IUserService userService)
        {
            _eventService = eventService;
            _groupService=groupService;
            _userService=userService;

        }
        public async Task<IActionResult> CreateEvent()
        {
            var userId = HttpContext?.Session?.GetString("UserId");
            var groupResponse = await _groupService.GetAllGroupsAsync();

            var viewModel = new AddUserViewModel
            {
                Group = groupResponse.Data ?? new List<GroupDto>(),
            };
            return View(viewModel);
        }
        [HttpPost]
        public async Task<IActionResult> InsertOrUpdateEvent([FromBody] MstEventDto dto)
        {
            dto.IsActive = true;
            //dto.FacilityId = int.Parse(HttpContext?.Session?.GetString("FacilityId"));
            //dto.CreatedBy =int.Parse(HttpContext?.Session?.GetString("UserId"));
            var result = await _eventService.InsertOrUpdateEventAsync(dto);
            return StatusCode(result.Status, result);
        }

        [HttpGet]
        public async Task<IActionResult> GetEvents()
        {
            try
            {
                var userIdStr = HttpContext?.Session?.GetString("UserId");
                var facilityIdsStr = HttpContext?.Session?.GetString("FacilityIds"); // Can be comma-separated for multiple
                var isAppAdminStr = HttpContext?.Session?.GetString("IsAppAdmin");
                var isDepartmentAdminStr = HttpContext?.Session?.GetString("IsDepartmentAdmin");

                if (!int.TryParse(userIdStr, out int userId))
                    return Unauthorized("User not logged in");

                bool isAppAdmin = bool.TryParse(isAppAdminStr, out var appAdmin) && appAdmin;
                bool isDepartmentAdmin = bool.TryParse(isDepartmentAdminStr, out var deptAdmin) && deptAdmin;

                List<int>? facilityIds = null;

                // Parse multiple facility IDs from session
                if (!string.IsNullOrEmpty(facilityIdsStr))
                {
                    facilityIds = facilityIdsStr
                        .Split(',', StringSplitOptions.RemoveEmptyEntries)
                        .Select(f => int.TryParse(f, out var id) ? id : (int?)null)
                        .Where(f => f.HasValue)
                        .Select(f => f.Value)
                        .ToList();

                    if (!facilityIds.Any())
                        facilityIds = null; // fallback if invalid data
                }

                ApiResponseDto<List<MstEventDto>> result;

                if (isAppAdmin || isDepartmentAdmin)
                {
                    // Admins see all events
                    result = await _eventService.GetEventsAsync(null, null);
                }
                else if (facilityIds != null)
                {
                    // Facility Admin with multiple facilities
                    result = await _eventService.GetEventsAsync(userId, null);
                }
                else
                {
                    // Normal user sees events mapped to them
                    result = await _eventService.GetEventsAsync(userId, null);
                }

                return StatusCode(result.Status, result);
            }
            catch (Exception ex)
            {
                return StatusCode(500, ApiResponseDto<List<MstEventDto>>.ErrorResponse("Internal server error", 500, new List<HSSE_Service.ServiceResponce.Error> {
            new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
        }));
            }
        }

        public async Task<IActionResult> ViewEvent()
        {
            var userId = int.Parse(HttpContext?.Session?.GetString("UserId"));

            var userResponse = await _userService.GetUserByIdAsync(userId);

            var viewModel = new AddUserViewModel
            {
                NewUser = userResponse,
            };
            return View(viewModel);
        }

        [HttpGet]
        public async Task<IActionResult> GetEventsByFacilityId(
            int? dateFilterId = null,
            string? search = null)
        {
            var userIdStr = HttpContext?.Session?.GetString("UserId");
            var facilityIdStr = HttpContext?.Session?.GetString("FacilityIds");
            var isAppAdminStr = HttpContext?.Session?.GetString("IsAppAdmin");
            var isDepartmentAdminStr = HttpContext?.Session?.GetString("IsDepartmentAdmin");

            if (!int.TryParse(userIdStr, out int userId))
                return Unauthorized("User not logged in");

            bool isAppAdmin = bool.TryParse(isAppAdminStr, out var appAdmin) && appAdmin;
            bool isDeptAdmin = bool.TryParse(isDepartmentAdminStr, out var deptAdmin) && deptAdmin;

            List<int> facilityIds = new();

            if (!string.IsNullOrWhiteSpace(facilityIdStr))
            {
                facilityIds = facilityIdStr
                    .Split(',', StringSplitOptions.RemoveEmptyEntries)
                    .Select(f => int.TryParse(f, out var id) ? id : (int?)null)
                    .Where(f => f.HasValue)
                    .Select(f => f.Value)
                    .ToList();
            }

            ApiResponseDto<List<MstEventDto>> result;

            if (isAppAdmin || isDeptAdmin)
            {
                // App Admin or Dept Admin: get all events
                result = await _eventService.GetEventsByFacilityIdAsync(null, true, userId, dateFilterId, search);
            }
            else if (facilityIds.Any())
            {
                // Facility Admin or Normal User
                result = await _eventService.GetEventsByFacilityIdAsync(null, false, userId, dateFilterId, search);
            }
            else
            {
                return BadRequest("No facility assigned to user.");
            }

            return StatusCode(result.Status, result);
        }


        [HttpPost]
        public async Task<IActionResult> SaveEventLike(int eventId)
        {
            int userId = int.Parse(HttpContext?.Session?.GetString("UserId"));
            var result = await _eventService.SaveEventLikeAsync(eventId, userId);
            return StatusCode(result.Status, result);
        }
        [HttpGet]
        public async Task<IActionResult> GetEventRsvpDetails(int eventId)
        {
            var result = await _eventService.GetEventRsvpDetailsAsync(eventId);
            return StatusCode(result.Status, result);
        }

        [HttpPost]
        public async Task<IActionResult> SaveEventResponse(int eventId, bool isAccepted)
        {
            int userId = int.Parse(HttpContext?.Session?.GetString("UserId"));
            var dto = new EventResponseDto
            {
                ResponseId = 0,
                EventId = eventId,
                UserId = userId,
                IsAccepted = isAccepted,
                RespondedAt = DateTime.UtcNow
            };
            var result = await _eventService.SaveEventResponseAsync(dto);
            return StatusCode(result.Status, result);
        }

        [HttpPost]
        public async Task<IActionResult> ToggleEventActivation(int eventId)
        {
            var result = await _eventService.ToggleEventActivationAsync(eventId);
            return StatusCode(result.Status, result);
        }

        [HttpGet]
        public async Task<IActionResult> GetEventById(int id)
        {
            int userId = int.Parse(HttpContext?.Session?.GetString("UserId"));

            var result = await _eventService.GetEventByIdAsync(id, userId);
            return StatusCode(result.Status, result);
        }
        [HttpGet]
        public async Task<IActionResult> ViewDetailsEvent(int eventId)
        {
            int userId = int.Parse(HttpContext?.Session?.GetString("UserId"));

            var result = await _eventService.GetEventByIdAsync(eventId, userId);
            if (result.Status == 200 && result.Data != null)
            {
                return View("ViewDetailsEvent", result.Data);
            }
            return View("ViewDetailsEvent", null);
        }
    }
} 