﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HSSE_Model_Dto.ModelDto
{
    public class FollowupStatusUpdateRequestDto
    {
        public int FollowupId { get; set; }

        public int PostId { get; set; }

        public int Status { get; set; }
        public int? UserId { get; set; }  // This is the user performing the action
        public List<string>? AfterMediaUrl { get; set; }
        public string? CompletedComments { get; set; }


    }
}
