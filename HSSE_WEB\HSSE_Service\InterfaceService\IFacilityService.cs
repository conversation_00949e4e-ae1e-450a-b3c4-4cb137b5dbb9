﻿using HSSE_Models_Dto.ModelsDto;
using HSSE_Service.DataManager;
using HSSE_Service.ServiceResponce;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HSSE_Service.InterfaceService
{
    public interface IFacilityService
    {
        Task<List<MstFacilityDto>> GetAllFacilityAsync();
        Task<List<MstOrganisationDto>> GetAllOrgAsync();
        Task<List<MstFacilityDto>> GetAllFacilities();
        Task<List<FacilityRoleDto>> GetUserRolesForFacility(int facilityId, int userId);
        Task<MstFacilityDto> GetFacilityByIdAsync(int id);
        Task<bool> UpdateFacilityAsync(MstFacilityDto facility);
        Task<bool> ToggleFacilityStatusAsync(int id);
        Task<bool> SaveFacilityAsync(MstFacilityDto facility);
        Task<ApiResponseDto<List<MstFacilityDto>>> GetFacilityListAsync();
        Task<List<MstFacilityDto>> GetAllMasterFacilityAsync();
    }
}
