﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HSSE_Models_Dto.ModelsDto
{
    public class MstFacilityDto
    {
        public int FacilityId { get; set; }

        public int OrgId { get; set; }

        public string? FacilityName { get; set; }

        public string? FacilityCode { get; set; }

        public DateTime? CreatedAt { get; set; }

        public bool IsActive { get; set; }
    }
    public class FacilityApiResponse
    {
        public int Code { get; set; }
        public string? Msg { get; set; }
        public string? Obj { get; set; } // This is a stringified JSON — must be deserialized separately
    }
    public class FacilityData
    {
        public int TotalRecords { get; set; }
        public List<MstFacilityDto> GridData { get; set; } = new();
    }
}
