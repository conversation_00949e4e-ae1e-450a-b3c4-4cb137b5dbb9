using HSSE_Models_Dto.ModelsDto;
using HSSE_Models_Dto.ViewModels;
using HSSE_Service.DataManager;
using HSSE_Service.InterfaceService;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;

namespace HSSE_WEB.Controllers
{
    public class GroupController : Controller
    {
        private readonly IGroupService _groupService;
        private readonly IUserService _userService;

        public GroupController(IGroupService groupService, IUserService userService)
        {
            _groupService = groupService;
            _userService=userService;
        }

        [HttpGet]
        public IActionResult Create()
        {
            return View();
        }

        [HttpPost]
        public async Task<IActionResult> Create([FromBody] GroupCreateDto dto)
        {
            if (string.IsNullOrWhiteSpace(dto.GroupName))
                return BadRequest("Group Name is required.");

            int createdBy = 0;
            if (HttpContext.Session.GetString("UserId") != null)
                int.TryParse(HttpContext.Session.GetString("UserId"), out createdBy);

            var result = await _groupService.CreateGroupAsync(dto, createdBy);
            return StatusCode(result.Status, result);
        }

        [HttpGet]
        public async Task<IActionResult> GetAllGroups()
        {
            var result = await _groupService.GetAllGroupsAsync();
            return StatusCode(result.Status, result);
        }

        [HttpGet]
        public async Task<IActionResult> GetGroupById(int id)
        {
            var result = await _groupService.GetGroupByIdAsync(id);
            return StatusCode(result.Status, result);
        }

        [HttpDelete]
        public async Task<IActionResult> DeleteGroup(int id)
        {
            var result = await _groupService.DeleteGroupAsync(id);
            return StatusCode(result.Status, result);
        }

        [HttpGet]
        [HttpGet]
        public async Task<IActionResult> AddGroupMembers()
        {
            var groupResponse = await _groupService.GetAllGroupsAsync();
            var users = await _userService.GetAllUserAsync();
            var userId = HttpContext?.Session?.GetString("UserId");

            //var filteredUsers = users.Where(u => u.UserId.ToString() != userId).ToList();
            var viewModel = new AddUserViewModel
            {
                Group = groupResponse.Data ?? new List<GroupDto>(),
                ExistingUsers = users
            };

            return View(viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> InsertOrUpdateGroupMember([FromBody] MstGroupMemberDto dto)
        {
            var result = await _groupService.InsertGroupMemberAsync(dto.GroupId, dto.UserIds, dto.OldGroupId ?? 0);
            return StatusCode(result.Status, result);
        }
        [HttpPost]
        public async Task<IActionResult> UpdateGroupMember([FromBody] MstGroupMemberDto dto)
        {
            var result = await _groupService.UpdateGroupMemberAsync(dto.GroupId, dto.UserIds, dto.OldGroupId ?? 0);
            return StatusCode(result.Status, result);
        }
        [HttpGet]
        public async Task<IActionResult> GetGroupMembers()
        {
            var result = await _groupService.GetGroupMembersAsync();
            return StatusCode(result.Status, result);
        }

        [HttpGet]
        public async Task<IActionResult> GetGroupMembersByGroupId(int groupId)
        {
            var result = await _groupService.GetGroupMembersByGroupIdAsync(groupId);
            return StatusCode(result.Status, result);
        }

        [HttpDelete]
        public async Task<IActionResult> DeleteAllGroupMembersByGroupId(int id)
        {
            var result = await _groupService.DeleteAllGroupMembersByGroupIdAsync(id);
            return StatusCode(result.Status, result);
        }
    }
} 