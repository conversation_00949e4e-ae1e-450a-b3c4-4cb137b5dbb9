@{
    ViewData["Title"] = "Manage Post Categories";
}
<div class="col-12 grid-margin">
    <div class="card">
        <div class="card-body">
            <h4 class="card-title">Create Post Category</h4>
            <form id="postCategoryForm" class="form-sample">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group row">
                            <label for="CategoryName" class="col-sm-4 col-form-label">Category Name <span class="text-danger">*</span></label>
                            <div class="col-sm-8">
                                <input type="hidden" id="CatId" name="CatId" value="0" />
                                <input type="text" class="form-control" id="CategoryName" name="CategoryName" required />
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-12 text-end">
                        <button type="submit" class="btn btn-primary">Save Category</button>
                        <button type="reset" class="btn btn-light">Cancel</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
<div class="card mt-4">
    <div class="card-body">
        <h4 class="card-title">Categories List</h4>
        <div class="table-responsive">
            <table id="post-category-listing" class="table">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>Name</th>
                        <th>Created At</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
        </div>
    </div>
</div>
<!-- Edit Modal -->
<div class="modal fade" id="editPostCategoryModal" tabindex="-1" aria-labelledby="editPostCategoryModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editPostCategoryModalLabel">Edit Category</h5>
                <button type="button" class="close" data-dismiss="modal"><span>&times;</span></button>
            </div>
            <div class="modal-body">
                <form id="editPostCategoryForm">
                    <input type="hidden" id="EditCatId" />
                    <div class="mb-3">
                        <label for="EditCategoryName" class="form-label">Category Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="EditCategoryName" required />
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="submit" form="editPostCategoryForm" class="btn btn-primary">Save Changes</button>
            </div>
        </div>
    </div>
</div>
<script>
        var getCategory = '@Url.Action("GetAll", "PostCategory")';
                var insertOrUpdate = '@Url.Action("InsertOrUpdate", "PostCategory")';
    var getById = '@Url.Action("GetById", "PostCategory")';
        var deleteCategory = '@Url.Action("Delete", "PostCategory")';

</script>
@section Scripts {
    <script src="~/js/Post/postCategory.js"></script>
} 