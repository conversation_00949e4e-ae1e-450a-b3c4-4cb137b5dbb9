/* Layouts */

// Sidebar Mini
.sidebar-mini {
  @media (min-width: 992px) {
    .navbar {
      .navbar-brand-wrapper {
        width: $sidebar-width-mini;
      }
      .navbar-menu-wrapper {
        width: calc(100% - #{$sidebar-width-mini});
      }
    }
    .sidebar {
      width: $sidebar-width-mini;
      .nav {
        .nav-item {
          padding: 0;
          .nav-link {
            @include display-flex;
            @include align-items(center);
            @include justify-content(center);
            @include flex-direction(column);
            text-align: center;
            position: relative;
            border-bottom: none;
            height: 3.8rem;
            .menu-title {
              display: block;
              margin: auto;
            }
            .badge {
              margin-left: 5px;
              display: none;
            }
            i {
              &.menu-icon {
                display:block;
                margin-right: auto;
                margin-left: auto;
                margin-top: .625rem;
              }
              &.menu-arrow {
                display: inline-block;
                margin-left: 5px;
                position: absolute;
                top: 50%;
                right: 20px;
                @include transform(translateY(-50%));
              }
            }
          }
        }
        &.sub-menu {
          .nav-item {
            &::before {
              content: none;
            }
            .nav-link {
              height: 2rem;
            }
          }
        }
        &:not(.sub-menu) {
          .nav-item {
            &::before {
              left: 0;
            }
          }
        }
      }
    }

    .main-panel {
      width: calc(100% - #{$sidebar-width-mini});
    }
    &:not(.sidebar-icon-only) {
      //Apply these styles only when sidebar-mini is not collapsed to icon-only mode
      .sidebar {
        .nav {
          &.sub-menu {
            padding: .5rem 0;
            border-top: none;
            .nav-item {
              .nav-link {
                padding: .75rem 0 .75rem .3rem;
                margin-left: auto;
                margin-right: auto;
                display: block;
                text-align: left;
                width: 66%;
              }
            }
          }
        }
      }
    }
  }
}

// Sidebar Icon Only
.sidebar-icon-only {
  @media (min-width: 992px) {
    .navbar {
      .navbar-brand-wrapper {
        width: $sidebar-width-icon;
        .brand-logo {
          display: none;
        }
        .brand-logo-mini {
          display: inline-block;
        }
      }
      .navbar-menu-wrapper {
        width: calc(100% - #{$sidebar-width-icon});
      }
    }
    .sidebar {
      width: $sidebar-width-icon;
      padding: 1.25rem 0;
      .nav {
        overflow: visible;
        .nav-item {
          position: relative;
          &::before {
            display: none;
          }
          .nav-link {
            display: block;           
            text-align: center;
            position: static;
            padding-top: 9px;
            .menu-title,
            .badge,.menu-sub-title {
              display: none;
            }
            .menu-title {
              @include border-radius(0 5px 5px 0px);
              @at-root #{selector-append(".rtl", &)} {
                @include border-radius(5px 0 0 5px);
              }
            }
            i {
              &.menu-icon {
                margin-right: 0;
                margin-left: 0;
                margin-bottom: 0;
              }
              &.menu-arrow {
                display: none;
              }
            }
            &[aria-expanded] {
              .menu-title {
                @include border-radius(0 5px 0 0px);
                @at-root #{selector-append(".rtl", &)} {
                  @include border-radius(5px 0 0 0);
                }
              }
            }
          }
          .collapse {
            display: none;
          }
          &.hover-open {
            .nav-link {
              .menu-title {
                @include display-flex;
                @include align-items(center);
                background: $icon-only-menu-bg-light;
                @at-root #{selector-append(".sidebar-dark", &)} {
                    background: $icon-only-menu-bg-dark;
                }
                padding: 0.5rem 1.4rem;
                left: $sidebar-width-icon;
                position: absolute;
                text-align: left;
                top: 0;
                bottom: 0;
                width: $icon-only-collapse-width;
                z-index: 1;
                line-height: 1.8;
                box-shadow: 4px 0px 7px 0px rgba(182, 185, 189, 0.25);              
                @at-root #{selector-append(".rtl", &)} {
                  left: auto;
                  right: $sidebar-width-icon;
                  text-align: left;
                  box-shadow: -4px 0px 7px 0px rgba(182, 185, 189, 0.25);                                
                }
                @at-root #{selector-append(".sidebar-dark", &)} {
                  color: $white;
                }
                &:after {
                  display: none;
                }
              }
              &:hover {
                .menu-title {
                  background: $icon-only-menu-bg-light;
                  @at-root #{selector-append(".sidebar-dark", &)} {
                    background: $icon-only-menu-bg-dark;
                  }
                }
              }
            }
            .collapse,
            .collapsing {
              display: block;
              padding: .5rem 0;
              background: $icon-only-menu-bg-light;
              @include border-radius(0 0 5px 0);
              @at-root #{selector-append(".sidebar-dark", &)} {
                  background: $icon-only-menu-bg-dark;
              }
              position: absolute;
              left: $sidebar-width-icon;
              width: $icon-only-collapse-width;
              box-shadow: 4px 4px 7px 0px rgba(182, 185, 189, 0.25);
              @at-root #{selector-append(".rtl", &)} {
                  left: auto;
                  right:$sidebar-width-icon;
                  @include border-radius(0 0 0 5px);
                  box-shadow: -4px 4px 7px 0px rgba(182, 185, 189, 0.25);
              }
            }
          }
        }
        &.sub-menu {
          padding: $sidebar-icon-only-submenu-padding;
          .nav-item {
            .nav-link {
              text-align: left;
              padding-left: 20px;
            }
          }
        }
      }
    }

    .main-panel {
      width: calc(100% - #{$sidebar-width-icon});
    }
  }
}

// Hidden Sidebar
.sidebar-hidden {
  @media (min-width: 992px) {
    .sidebar {
      transition: width $action-transition-duration $action-transition-timing-function, padding $action-transition-duration $action-transition-timing-function;
      -webkit-transition: width $action-transition-duration $action-transition-timing-function, padding $action-transition-duration $action-transition-timing-function;
      -moz-transition: width $action-transition-duration $action-transition-timing-function, padding $action-transition-duration $action-transition-timing-function;
      -ms-transition: width $action-transition-duration $action-transition-timing-function, padding $action-transition-duration $action-transition-timing-function;
      width: 0;
      padding-left: 0;
      padding-right: 0;
    }

    .main-panel {
      width: 100%;
    }
  }
}

// Absolute sidebar with overlay to content
.sidebar-absolute {
  @media (min-width: 992px) {
    .page-body-wrapper {
      position: relative;

      .sidebar {
        transition: none;
      }
    }

    &:not(.sidebar-hidden) {
      .sidebar {
        position: absolute;
        height: 100%;
        overflow-y: auto;
        box-shadow: 0px 0 20px 0 #e1e1e1;
      }
    }

    .main-panel {
      width: 100%;
      transition: none;
    }
  }
}

//Fixed sidebar
.sidebar-fixed {
  @media(min-width: 992px) {
    .sidebar {
      position: fixed;
      max-height: 100%;

      .nav {
        height: auto;
        overflow: auto;
        position: relative;

        &.sub-menu {
          max-height: none;
        }
      }
    }

    .main-panel {
      margin-left: $sidebar-width-lg;
    }

    &.sidebar-icon-only {
      .main-panel {
        margin-left: $sidebar-width-icon;
      }
    }
  }
}

//Boxed layout
.boxed-layout {
  @media (min-width: 992px) {
    .container-scroller {
      background: $boxed-layout-bg;
      padding: 0 calc((100% - #{$boxed-container-width}) / 2);
    }
    .navbar {
      &.fixed-top {
        margin: auto;
        max-width: $boxed-container-width;
      }
    }
  }
}

//RTL layout
.rtl {
  direction: rtl;
  text-align: right;

  .sidebar {
    .nav {
      padding-right: 0;
      &.sub-menu {
        padding: $rtl-sidebar-submenu-padding;
        @at-root #{selector-append(".sidebar-icon-only", &)} {
          padding-right: 0rem;
          .nav-item {
            .nav-link {
              padding-right: 3rem;
              text-align: right;
              &:before {
                right: 1.75rem;
              }
            }
          }
        }
      }
    }
  }

  .product-chart-wrapper,
  .settings-panel .tab-content .tab-pane .scroll-wrapper,
  .sidebar-fixed .nav,
  .table-responsive,
  ul.chats {
    &::-webkit-scrollbar {
      width: 0.5em;
    }

    &::-webkit-scrollbar-track {
      -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3);
    }

    &::-webkit-scrollbar-thumb {
      background-color: darkgrey;
      outline: 1px solid slategrey;
    }
  }
}
