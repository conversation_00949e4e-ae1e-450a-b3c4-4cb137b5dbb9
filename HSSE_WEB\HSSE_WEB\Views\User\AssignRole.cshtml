﻿@model HSSE_Models_Dto.ViewModels.AddUserViewModel


@{
    ViewBag.Title = "Assign Role";
    Layout = "~/Views/Shared/_Layout.cshtml"; // Or your correct layout path
}

<div class="main-panel">
    <div class="content-wrapper">
        <!-- Form Section -->
        <div class="card" data-permission="create">
            <div class="card-body">
                <h4 class="card-title">Assign Role to User</h4>
                <form id="userRoleForm">
                    <div class="form-group row">
                        <label class="col-sm-2 col-form-label">Select User<span class="text-danger">*</span></label>
                        <div class="col-sm-4">
                         @*    <select name="UserId" class="form-control" required>
                                <option value="">-- Select User --</option>
                                @foreach (var role in Model.ExistingUsers)
                                {
                                    <option value="@role.UserId">@role.Username</option>
                                }
                            </select> *@
                            <div id="user-typeahead">
                                <input type="text" class="typeahead form-control" id="user-search" placeholder="Search User">
                                <input type="hidden" id="selectedUserId" name="UserId" />
                            </div>
                        </div>
                  


                        <label class="col-sm-2 col-form-label">Select Role<span class="text-danger">*</span></label>
                        <div class="col-sm-4">
                            <select name="RoleId" id="RoleSelect" class="form-control" required>
                                <option value="">-- Select Role --</option>
                                @foreach (var role in Model.Roles)
                                {
                                    <option value="@role.RoleId">@role.RoleName</option>
                                }
                            </select>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label class="col-sm-2 col-form-label">Facility (Optional)</label>
                        <div class="col-sm-4">
                            <select name="FacilityIds" class="form-control">
                                <option value="">-- Select Facility --</option>

                                    @foreach (var fac in Model.Facilities)
                                    {
                                        <option value="@fac.FacilityId">@fac.FacilityName</option>
                                    }
                            </select>
                        </div>
                    </div>

                    <div class="form-group row">
                        <div class="col-sm-12 text-right">
                            <button type="submit" class="btn btn-outline-primary btn-icon-text"><i class="mdi mdi-file-check btn-icon-prepend"></i>Submit</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Role Mapping Table -->
        <div class="card mt-4" data-permission="view">
            <div class="card-body">
                <h4 class="card-title">User Role Mapping</h4>
                <div class="table-responsive">
                    <table id="order-listing" class="table">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>User</th>
                                <th>Role</th>
                                <th>Facility</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="userRoleMappTableBody">
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="editRoleConfigModal" tabindex="-1" aria-labelledby="editRoleConfigModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-md" role="document">
        <div class="modal-content">

            <div class="modal-header">
                <h5 class="modal-title" id="editRoleConfigModalLabel">Edit Role</h5>
                <button type="button" class="close" data-dismiss="modal"><span>&times;</span></button>
            </div>

            <div class="modal-body">
                <input type="hidden" id="editRoleConfigId" />
                <div id="ffuser-typeahead" class="form-group">
                    <label class="col-sm-3 col-form-label">User Name<span class="text-danger">*</span></label>

                    <input type="text" class="typeahead form-control" id="edit-user-search" placeholder="Search User">
                    <input type="hidden" id="editSelectedUserId" name="editUserId" />
                </div>
                <div class="form-group row">
                    <label class="col-sm-3 col-form-label">User Role<span class="text-danger">*</span></label>

                    <select name="editRoleId" class="form-control" required>
                        <option value="">-- Select Role --</option>
                        @foreach (var role in Model.Roles)
                        {
                            <option value="@role.RoleId">@role.RoleName</option>
                        }
                    </select>
                </div>
                <div class="form-group row">
                    <label class="col-sm-4 col-form-label">Facility (Optional)</label>
               
                        <select name="editFacilityIds" class="form-control">
                            <option value="">-- Select Facility --</option>

                            @foreach (var fac in Model.Facilities)
                            {
                                <option value="@fac.FacilityId">@fac.FacilityName</option>
                            }
                        </select>
             
                </div>

            </div>

            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="updateRole()">Update Role</button>
            </div>

        </div>
    </div>
</div>


<script>
    var existingUsers = [
    @foreach (var user in Model.ExistingUsers)
    {
        @: { id: "@user.UserId", name: "@user.Username" },
    }
    ];
var getUserRoleUrl = '@Url.Action("GetAllUserRoleMappingById", "User")';
var saveUserRoleMapping = '@Url.Action("SaveUserRoleMapping", "User")';
var deleteUserRoleMappingUrl = '@Url.Action("DeleteUserRoleMapping", "User")';
var getAllRolesMappingDetails = '@Url.Action("GetAllRolesMappingDetails", "User")';
</script>
@section Scripts {
    <script src="~/js/User/assignRole.js"></script>
}
