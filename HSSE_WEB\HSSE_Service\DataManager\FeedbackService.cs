using HSSE_Models_Dto.ModelsDto;
using HSSE_Service.Constant;
using HSSE_Service.InterfaceService;
using HSSE_Service.ServiceResponce;
using Microsoft.AspNetCore.Http;
using System.Net.Http.Json;
using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using HSSE_Domain.Models;
using Microsoft.EntityFrameworkCore;

namespace HSSE_Service.DataManager
{
    public class FeedbackService : IFeedbackService
    {
        private readonly HsseDbLatestContext _context;
        private readonly HttpClient _httpClient;
        private readonly string _apiBaseUrl;

        public FeedbackService(HsseDbLatestContext context, IHttpClientFactory httpClientFactory, IConfiguration configuration)
        {
            _httpClient = httpClientFactory.CreateClient("AuthenticatedClient");
            var baseUrl = configuration["ExternalApi:BaseUrl"];
            if (!string.IsNullOrEmpty(baseUrl))
            {
                _httpClient.BaseAddress = new Uri(baseUrl);
            }
            _apiBaseUrl = configuration["ExternalApi:BaseUrl"];
            _context = context;
        }

        public async Task<ApiResponseDto<object>> CreateFeedbackAsync(MstFeedbackDto feedbackDto)
        {
            try
            {
                if (feedbackDto == null)
                {
                    return ApiResponseDto<object>.ErrorResponse("Invalid feedback data", StatusCodes.Status400BadRequest);
                }

                var apiUrl = $"{_apiBaseUrl}{ApiEndpoints.CreateFeedback}";
                var response = await _httpClient.PostAsJsonAsync(apiUrl, feedbackDto);

                if (response.IsSuccessStatusCode)
                {
                    var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<object>>();
                    return ApiResponseDto<object>.SuccessResponse(apiResult?.Message ?? "Feedback created successfully", (int)response.StatusCode, null);
                }
                else
                {
                    return ApiResponseDto<object>.ErrorResponse("Failed to create feedback", (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse("Failed to create feedback", 500, new System.Collections.Generic.List<HSSE_Service.ServiceResponce.Error> {
                    new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
                });
            }
        }

        public async Task<ApiResponseDto<object>> UpdateFeedbackWithResponseAsync(MstFeedbackResponseDto feedbackDto)
        {
            try
            {
                if (feedbackDto == null)
                {
                    return ApiResponseDto<object>.ErrorResponse("Invalid feedback data", StatusCodes.Status400BadRequest);
                }

                var apiUrl = $"{_apiBaseUrl}{ApiEndpoints.UpdateFeedbackResponse}";
                var response = await _httpClient.PostAsJsonAsync(apiUrl, feedbackDto);

                if (response.IsSuccessStatusCode)
                {
                    var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<object>>();
                    return ApiResponseDto<object>.SuccessResponse(apiResult?.Message ?? "Feedback created successfully", (int)response.StatusCode, null);
                }
                else
                {
                    return ApiResponseDto<object>.ErrorResponse("Failed to create feedback", (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse("Failed to create feedback", 500, new System.Collections.Generic.List<HSSE_Service.ServiceResponce.Error> {
                    new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
                });
            }
        }

        public async Task<ApiResponseDto<List<MstFeedbackDto>>> GetFeedbackAsync(int? userId, int? facilityId)
        {
            try
            {
                // Ensure values are safely used in the query
                var queryParams = $"facilityId={(facilityId)}&userId={(userId)}";
                var apiUrl = $"{_apiBaseUrl}/api/MstFeedback/GetFeedbackByFacilityId?{queryParams}";

                var response = await _httpClient.GetAsync(apiUrl);
                if (!response.IsSuccessStatusCode)
                {
                    return ApiResponseDto<List<MstFeedbackDto>>.ErrorResponse(
                        "Failed to fetch newsletters.",
                        (int)response.StatusCode
                    );
                }

                var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<List<MstFeedbackDto>>>();

                return ApiResponseDto<List<MstFeedbackDto>>.SuccessResponse(
                    apiResult?.Message ?? "Newsletters fetched successfully.",
                    (int)response.StatusCode,
                    apiResult?.Data ?? new List<MstFeedbackDto>()
                );
            }
            catch (Exception ex)
            {
                return ApiResponseDto<List<MstFeedbackDto>>.ErrorResponse(
                    "Failed to fetch newsletters.",
                    500,
                    new List<HSSE_Service.ServiceResponce.Error>
                    {
                        new HSSE_Service.ServiceResponce.Error
                        {
                            Code = 500,
                            Message = ex.Message
                        }
                    }
                );
            }
        }

        public async Task<ApiResponseDto<MstFeedbackDto>> GetFeedbackByIdAsync(int id)
        {
            try
            {
                var apiUrl = $"{_apiBaseUrl}/api/MstFeedback/GetFeedbackById?id={id}";
                var response = await _httpClient.GetAsync(apiUrl);

                if (!response.IsSuccessStatusCode)
                {
                    return ApiResponseDto<MstFeedbackDto>.ErrorResponse(
                        "Failed to fetch feedback details.",
                        (int)response.StatusCode
                    );
                }

                var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<MstFeedbackDto>>();

                return ApiResponseDto<MstFeedbackDto>.SuccessResponse(
                    apiResult?.Message ?? "Feedback details fetched successfully.",
                    (int)response.StatusCode,
                    apiResult?.Data
                );
            }
            catch (Exception ex)
            {
                return ApiResponseDto<MstFeedbackDto>.ErrorResponse(
                    "Failed to fetch feedback details.",
                    500,
                    new List<HSSE_Service.ServiceResponce.Error>
                    {
                        new HSSE_Service.ServiceResponce.Error
                        {
                            Code = 500,
                            Message = ex.Message
                        }
                    }
                );
            }
        }

        public async Task<ApiResponseDto<object>> GetFeedbackByFacilityAndUserAsync(int? facilityId, int? userId)
        {
            try
            {
                var query = _context.MstFeedbacks
                    .Include(f => f.Facility) // eager loading
                    .AsQueryable();

                if (facilityId.HasValue)
                {
                    query = query.Where(f => f.FacilityId == facilityId.Value);
                }

                if (userId.HasValue)
                {
                    query = query.Where(f => f.CreatedBy == userId.Value);
                }

                var feedbackList = await query
                    .Select(f => new 
                    {
                        FeedbackId = f.FeedbackId,
                        Name = f.Name,
                        Title = f.Title,
                        Description = f.Description,
                        Date = f.Date,
                        FilePath = f.FilePath,
                        Status = f.Status,
                        FacilityId = f.FacilityId,
                        Response = f.Response,
                        FacilityName = f.Facility != null ? f.Facility.FacilityName : null,
                        CreatedBy = f.CreatedBy
                        // add more if needed
                    })
                    .ToListAsync();

                if (!feedbackList.Any())
                {
                    return ApiResponseDto<object>.SuccessResponse("No feedback found for the provided filters.", StatusCodes.Status200OK, new List<MstFeedbackDto>());
                }

                return ApiResponseDto<object>.SuccessResponse("Feedback retrieved successfully", StatusCodes.Status200OK, feedbackList);
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse("Internal server error", StatusCodes.Status500InternalServerError);
            }
        }

        public enum FeedbackStatus
        {
            Pending = 1,
            InProgress = 2,
            Completed = 3
        }
    }
} 