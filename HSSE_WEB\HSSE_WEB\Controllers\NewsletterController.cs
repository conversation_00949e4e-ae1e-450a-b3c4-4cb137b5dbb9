﻿using HSSE_Models_Dto.ModelsDto;
using HSSE_Models_Dto.ViewModels;
using HSSE_Service.DataManager;
using HSSE_Service.InterfaceService;
using HSSE_Service.ServiceResponce;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace HSSE_WEB.Controllers
{
    public class NewsletterController : Controller
    {
        private readonly INewsletterService _newsletterService;
        private readonly IUserService _userService;

        public NewsletterController(INewsletterService newsletterService, IUserService userService)
        {
            _newsletterService = newsletterService;
            _userService=userService;
        }
        public async Task<IActionResult> CreateNewsletter()
        {
            // Get session flags
            bool isAppAdmin = bool.TryParse(HttpContext.Session.GetString("IsAppAdmin"), out var appAdmin) && appAdmin;
            bool isDepartmentAdmin = bool.TryParse(HttpContext.Session.GetString("IsDepartmentAdmin"), out var deptAdmin) && deptAdmin;
            bool isFacilityAdmin = bool.TryParse(HttpContext.Session.GetString("IsFacilityAdmin"), out var facAdmin) && facAdmin;
            var userFacilities = HttpContext?.Session?.GetString("FacilityIds");

            // Prepare facility list
            List<MstFacilityDto> facilities;

            if (isAppAdmin || isDepartmentAdmin)
            {
                var userFacility = await _userService.GetUserFacilitiesByFacilityId(null);
                facilities = userFacility;
            }
            else
            {
                // Facility Admin and/or Manager – merge their allowed facility IDs
                var facAdminFacilityStr = HttpContext.Session.GetString("FacilityAdminFacilityIds");
                var managerFacilityStr = HttpContext.Session.GetString("ManagerFacilityIds");

                var facAdminFacilityIds = facAdminFacilityStr?.Split(',', StringSplitOptions.RemoveEmptyEntries)
                    .Select(id => int.TryParse(id, out var fid) ? fid : 0).Where(id => id > 0).ToList() ?? new List<int>();

                var managerFacilityIds = managerFacilityStr?.Split(',', StringSplitOptions.RemoveEmptyEntries)
                    .Select(id => int.TryParse(id, out var fid) ? fid : 0).Where(id => id > 0).ToList() ?? new List<int>();

                // Merge and distinct
                var mergedFacilityIds = facAdminFacilityIds.Union(managerFacilityIds).Distinct().ToList();

                facilities = await _userService.GetUserFacilitiesByFacilityId(mergedFacilityIds);
            }
            var viewModel = new AddUserViewModel
            {
                Facilities = facilities
            };

            return View(viewModel);
        }
        public IActionResult ViewNewsletter()
        {
            return View();
        }
        [HttpPost]
        public async Task<IActionResult> InsertOrUpdateNewsletter([FromBody] MstNewsletterDto dto)
        {
            try
            {
                //int facilityId = int.Parse(HttpContext?.Session?.GetString("FacilityId"));
                int userId = int.Parse(HttpContext?.Session?.GetString("UserId") ?? "0");
                dto.CreatedBy = userId;
                //dto.FacilityId = facilityId;
                dto.IsActive = true;
                var result = await _newsletterService.InsertOrUpdateNewsletterAsync(dto);
                return StatusCode(result.Status, result);
            }
            catch (Exception ex)
            {

                throw;
            }
        }
        [HttpGet]
        public async Task<IActionResult> GetNewsletters()
        {
            try
            {
                var userIdStr = HttpContext?.Session?.GetString("UserId");
                var facilityIdsStr = HttpContext?.Session?.GetString("FacilityIds"); // plural, comma-separated string
                var isAppAdminStr = HttpContext?.Session?.GetString("IsAppAdmin");
                var isDepartmentAdminStr = HttpContext?.Session?.GetString("IsDepartmentAdmin");
                var isFacilityAdminStr = HttpContext?.Session?.GetString("IsFacilityAdmin");

                if (!int.TryParse(userIdStr, out int userId))
                    return Unauthorized("User not logged in");

                bool isAppAdmin = bool.TryParse(isAppAdminStr, out var appAdmin) && appAdmin;
                bool isDepartmentAdmin = bool.TryParse(isDepartmentAdminStr, out var deptAdmin) && deptAdmin;
                bool isFacilityAdmin = bool.TryParse(isFacilityAdminStr, out var facAdmin) && facAdmin;

                List<int>? facilityIds = null;
                if (!string.IsNullOrWhiteSpace(facilityIdsStr))
                {
                    facilityIds = facilityIdsStr
                        .Split(',', StringSplitOptions.RemoveEmptyEntries)
                        .Select(idStr => int.TryParse(idStr, out int id) ? id : (int?)null)
                        .Where(id => id.HasValue)
                        .Select(id => id.Value)
                        .ToList();
                }

                ApiResponseDto<List<MstNewsletterDto>> result;

                if (isAppAdmin || isDepartmentAdmin)
                {
                    // App/Department Admin → get all newsletters (no filtering)
                    result = await _newsletterService.GetNewslettersAsync(null, null);
                }
                else if (isFacilityAdmin)
                {
                    var facilityIdString = HttpContext.Session.GetString("FacilityAdminFacilityIds");
                    var adminFacilityIds = facilityIdString?.Split(',').Select(int.Parse).ToList() ?? new List<int>();

                    // Facility Admin → filter by multiple facilities if any
                    result = await _newsletterService.GetNewslettersAsync(null, adminFacilityIds);
                }
                else
                {
                    // Normal user → filter by userId only
                    result = await _newsletterService.GetNewslettersAsync(userId, null);
                }

                return StatusCode(result.Status, result);
            }
            catch (Exception ex)
            {
                return StatusCode(500, ApiResponseDto<object>.ErrorResponse("Internal server error", 500));
            }
        }

        [HttpPost]
        public async Task<IActionResult> ToggleNewsletterActivation(int newsletterId)
        {
            try
            {
                var result = await _newsletterService.ToggleNewsletterActivationAsync(newsletterId);
                return StatusCode(result.Status, result);
            }
            catch (Exception)
            {

                throw;
            }
        }
        [HttpPost]
        public async Task<IActionResult> DeleteNewsletter(int newsletterId)
        {
            var result = await _newsletterService.DeleteNewsletterAsync(newsletterId);
            return StatusCode(result.Status, result);
        }
        [HttpGet]
        public async Task<IActionResult> GetNewsletterById(int newsletterId)
        {
            var result = await _newsletterService.GetNewsletterByIdAsync(newsletterId);
            return StatusCode(result.Status, result);
        }

        [HttpGet]
        public async Task<IActionResult> GetNewslettersByFacilityId()
        {
            try
            {
                var userIdStr = HttpContext?.Session?.GetString("UserId");
                var facilityIdsStr = HttpContext?.Session?.GetString("FacilityIds"); // Assuming multiple IDs stored as CSV or JSON string
                var isAppAdminStr = HttpContext?.Session?.GetString("IsAppAdmin");
                var isDepartmentAdminStr = HttpContext?.Session?.GetString("IsDepartmentAdmin");
                var isFacilityAdminStr = HttpContext?.Session?.GetString("IsFacilityAdmin");

                if (!int.TryParse(userIdStr, out int userId))
                    return Unauthorized("User not logged in");

                bool isAppAdmin = bool.TryParse(isAppAdminStr, out var appAdmin) && appAdmin;
                bool isDepartmentAdmin = bool.TryParse(isDepartmentAdminStr, out var deptAdmin) && deptAdmin;
                bool isFacilityAdmin = bool.TryParse(isFacilityAdminStr, out var facAdmin) && facAdmin;

                ApiResponseDto<List<MstNewsletterDto>> result;
                List<int> facilityIds = new();
                facilityIds = facilityIdsStr.Split(',', StringSplitOptions.RemoveEmptyEntries)
                   .Select(id => int.TryParse(id, out var fid) ? fid : 0)
                   .Where(fid => fid > 0)
                   .ToList();
                if (isAppAdmin || isDepartmentAdmin)
                {
                    // App or Department Admin → get all newsletters (no filters)
                    result = await _newsletterService.GetNewslettersAsync(null, null);
                }
                else if (isFacilityAdmin)
                {
                    // Facility Admin → get newsletters by multiple facility IDs
                    

                    if (!string.IsNullOrEmpty(facilityIdsStr))
                    {
                        // Assuming CSV e.g. "1,2,3"
                        facilityIds = facilityIdsStr.Split(',', StringSplitOptions.RemoveEmptyEntries)
                            .Select(id => int.TryParse(id, out var fid) ? fid : 0)
                            .Where(fid => fid > 0)
                            .ToList();
                    }

                    if (facilityIds.Count == 0)
                        return BadRequest("Invalid or missing Facility IDs.");

                    result = await _newsletterService.GetNewslettersByFacilityIdsAsync(facilityIds, userId);
                }
                else
                {
                    // Normal user → get newsletters by userId only
                    result =await _newsletterService.GetNewslettersByFacilityIdsAsync(facilityIds, userId);
                }

                return StatusCode(result.Status, result);
            }
            catch (Exception ex)
            {
                return StatusCode(500, ApiResponseDto<object>.ErrorResponse("Internal server error", 500));
            }
        }

        [HttpGet]
        public async Task<IActionResult> ViewDetailsNewsletter(int newsletterId)
        {
            var result = await _newsletterService.GetNewsletterByIdAsync(newsletterId);
            if (result.Status == 200 && result.Data != null)
            {
                return View("ViewDetailsNewsletter", result.Data);
            }
            return View("ViewDetailsNewsletter", null);
        }

    }
} 