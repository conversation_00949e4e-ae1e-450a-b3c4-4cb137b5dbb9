﻿using System;
using System.Collections.Generic;

namespace HSSE_Domain.Models;

public partial class MstNotification
{
    public int NotificationId { get; set; }

    public int UserId { get; set; }

    public string Heading { get; set; } = null!;

    public string Message { get; set; } = null!;

    public bool? IsRead { get; set; }

    public DateTime? CreatedAt { get; set; }

    public virtual MstUser User { get; set; } = null!;
}
