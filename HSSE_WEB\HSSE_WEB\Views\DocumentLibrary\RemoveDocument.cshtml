@model HSSE_Models_Dto.ViewModels.AddUserViewModel

@{
    ViewData["Title"] = "Remove Document";
}
<style>
  .document-tree {
    font-family: 'Segoe UI', sans-serif;
    font-size: 15px;
    line-height: 1.6;
    position: relative;
    padding-left: 20px;
    margin-top: 10px;
}

.document-tree ul {
    list-style-type: none;
    margin-left: 12px;
    padding-left: 12px;
    border-left: 1px dashed #ccc;
    position: relative;
}

.document-tree li {
    position: relative;
    padding-left: 12px;
    margin-bottom: 10px;
}

.doc-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px 10px;
    border-radius: 6px;
    transition: background-color 0.2s ease-in-out;
    background-color: #fff;
    position: relative;
}

.doc-item:hover {
    background-color: #f9f9f9;
}

.doc-icon {
    font-size: 16px;
    color: #6c757d;
}

.folder-icon {
    color: #f0ad4e;
}

.download-icon {
    color: #198754;
}

.document-meta {
    font-size: 13px;
    color: #6c757d;
    margin-left: 28px;
    margin-top: 4px;
}

.badge-meta {
    background-color: #e9ecef;
    border-radius: 12px;
    padding: 2px 8px;
    margin-right: 6px;
    font-size: 12px;
}


</style>
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h4 class="card-title">Remove Document</h4>
                <div id="EditDocGrid" class="d-flex flex-wrap gap-3 align-items-start"></div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="editDocumentModal" tabindex="-1" aria-labelledby="editDocumentModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editDocumentModalLabel">Edit Document</h5>
                <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="editDocumentForm">
                    <input type="hidden" id="editDocumentId" />
                    <div class="row col-md-12">
                    <div class="form-group col-md-6">
                        <label for="editTitle">Title</label>
                        <input type="text" class="form-control" id="editTitle" name="editTitle" required />
                    </div>
                    <div class="form-group col-md-6">
                        <label for="editCategory">Category</label>
                        <input type="text" class="form-control" id="editCategory" name="editCategory" />
                    </div>
                    </div>
                    <div class="row col-md-12">
                    <div class="form-group col-md-6">
                        <label for="editVersion">Version</label>
                        <input type="text" class="form-control" id="editVersion" name="editVersion" />
                    </div>
                 @*    <div class="form-group">
                        <label for="editDocumentFile">File</label>
                        <input type="file" class="form-control" id="editDocumentFile" name="editDocumentFile" />
                        <small id="currentFileInfo" class="form-text text-muted"></small>
                    </div> *@
                    <div class="form-group col-md-6">
                        <label for="editParentDocumentId">Parent Document</label>
                        <select class="form-control" id="editParentDocumentId" name="editParentDocumentId">
                            <option value="">-- Select Parent (Optional) --</option>
                            @if (Model != null && Model.DocumentLibrary != null)
                            {
                                foreach (var doc in Model.DocumentLibrary)
                                {
                                    <option value="@doc.DocumentId">@doc.Title</option>
                                }
                            }
                        </select>
                    </div>
                    </div>
                    <div class="form-group col-md-6">
                        <label for="editMediaUrl">Thumbnail</label>
                        <div >
                            <div class="custom-file-upload w-100">
                                <input type="file" class="d-none" id="editMediaFile" name="editThumbnailImage" accept="image/*" />
                                <div class="input-group">
                                    <input type="text" class="form-control file-upload-info" id="editThumbnailFileName" readonly placeholder="Choose Thumbnail" />
                                    <div class="input-group-append">
                                        <button class="btn btn-primary btn-sm" type="button" id="editThumbnailBtn">Upload</button>
                                        <button class="btn btn-danger btn-sm ml-1 d-none" type="button" id="removeEditThumbnailBtn">Remove</button>
                                    </div>
                                </div>
                                <div class="mt-2 d-none" id="editThumbnailPreviewContainer">
                                    <img id="editThumbnailPreview" src="" class="img-thumbnail" style="max-width: 250px;" />
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveEditDocumentBtn">Save Changes</button>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

<script>
    var getDocuments = '@Url.Action("GetDocuments", "DocumentLibrary")';
    var createOrUpdateDocument =  '@Url.Action("CreateOrUpdateDocument", "DocumentLibrary")';

            var getUserDocuments = '@Url.Action("GetUserDocuments", "DocumentLibrary")';
                        var removeDocument = '@Url.Action("RemoveDocument", "DocumentLibrary")?documentId=';
                                var getDocumentById =  '@Url.Action("GetDocumentById", "DocumentLibrary")?documentId=';
                                            var defaultIconUrl = '@Url.Content("~/Content/images/document-icon.png")';

</script>
@section Scripts {
    <script src="~/js/DocumentLibrary/document-create.js"></script>
                   <script src="~/Content/js/tooltips.js"></script>
      <script src="~/Content/js/popover.js"></script>
}
