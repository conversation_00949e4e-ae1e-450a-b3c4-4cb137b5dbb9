using System.Diagnostics;
using System.IdentityModel.Tokens.Jwt;
using System.Text.Json;
using HSSE_Models_Dto.ModelsDto;
using HSSE_Models_Dto.ViewModels;
using HSSE_Service.InterfaceService;
using HSSE_WEB.Models;
using Microsoft.AspNetCore.Diagnostics;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;

namespace HSSE_WEB.Controllers
{
    public class HomeController : Controller
    {
        private readonly ILogger<HomeController> _logger;
        private readonly IUserService _userService;
        private readonly IAnnouncementService _announcementService;

        public HomeController(ILogger<HomeController> logger, IUserService userService, IAnnouncementService announcementService)
        {
            _logger = logger;
            _userService = userService;
            _announcementService=announcementService;
        }

        public async Task<IActionResult> login(string? token)
        {


            if (string.IsNullOrEmpty(token))
            {
                ViewData["UserName"] = HttpContext.Session.GetString("Username");

                return View();
            }

            // Token is present: decode and update user
            var userInfo = DecodeJwtToken(token);
            var result = await _userService.SaveOrUpdateUserAsync(userInfo);

            if (result.Data != null)
            {
                HttpContext.Session.SetString("Token", result.Data.Token.ToString());
                HttpContext.Session.SetString("Username", result.Data.Username);
                HttpContext.Session.SetString("Email", result.Data.Email);
                HttpContext.Session.SetString("UserId", result.Data.UserId.ToString());
                HttpContext.Session.SetString("FacilityIds", string.Join(",", result.Data?.FacilityIds));

                // If needed, store RoleIds as comma-separated string
                HttpContext.Session.SetString("RoleIds", string.Join(",", result.Data.RoleIds));

                HttpContext.Session.SetString("IsAppAdmin", result.Data.IsAppAdmin.ToString());
                HttpContext.Session.SetString("IsDepartmentAdmin", result.Data.IsDepartmentAdmin.ToString());
                HttpContext.Session.SetString("IsFacilityAdmin", result.Data.IsFacilityAdmin.ToString());
                HttpContext.Session.SetString("IsManager", result.Data.IsManager.ToString());

                // Save only if FacilityAdminFacilityIds is available
                if (result.Data.FacilityAdminFacilityIds != null)
                {
                    HttpContext.Session.SetString("FacilityAdminFacilityIds", string.Join(",", result.Data.FacilityAdminFacilityIds));
                }
                if (result.Data.ManagerFacilityIds != null)
                {
                    HttpContext.Session.SetString("ManagerFacilityIds", string.Join(",", result.Data.ManagerFacilityIds));
                }
                ViewData["UserName"] = HttpContext.Session.GetString("Username");

                return View();
            }

            return BadRequest("Failed to save user data.");
        }

        public static UserInfo DecodeJwtToken(string token)
        {
            var handler = new JwtSecurityTokenHandler();

            // Check if the token is valid
            if (handler.CanReadToken(token))
            {
                var jwtToken = handler.ReadJwtToken(token);

                // Extract the details from the token
                var userInfo = new UserInfo
                {
                    UserId = int.Parse(jwtToken.Payload["userId"].ToString()),
                    UserName = jwtToken.Payload["userName"].ToString(),
                    UserEmail = jwtToken.Payload["userEmail"].ToString(),
                    RoleId = int.Parse(jwtToken.Payload["roleId"].ToString()),
                    FacilityList = jwtToken.Payload["facList"].ToString(),
                    Language = jwtToken.Payload["language"].ToString(),
                    IsAdmin = bool.Parse(jwtToken.Payload["isAdmin"].ToString())
                };

                return userInfo;
            }

            // Return null or throw an exception if the token is invalid
            throw new ArgumentException("Invalid JWT Token.");
        }
        public IActionResult Privacy()
        {
            return View();
        }

        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
        public IActionResult Error()
        {
            var exceptionFeature = HttpContext.Features.Get<IExceptionHandlerPathFeature>();
            var errorMessage = exceptionFeature?.Error?.Message ?? "An unexpected error occurred.";
            ViewBag.ErrorMessage = errorMessage;

            return View(new ErrorViewModel
            {
                RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier,
                ErrorMessage = errorMessage
            });
        }

    }
}
