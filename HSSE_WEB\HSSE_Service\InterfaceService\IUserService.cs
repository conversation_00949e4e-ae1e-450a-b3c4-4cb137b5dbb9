﻿using HSSE_Models_Dto.ModelsDto;
using HSSE_Service.DataManager;
using HSSE_Service.ServiceResponce;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HSSE_Service.InterfaceService
{
    public interface IUserService
    {
        Task<ApiResponseDto<LoginResponseDto>> SaveOrUpdateUserAsync(UserInfo userInfo);
        Task<ApiResponseDto<bool>> RegisterUserAsync(MstUserDto model);
        Task<ApiResponseDto<object>> LoginUserAsync(string username, string password);
        Task<ApiResponseDto<bool>> CreateUserAsync(MstUserDto model, IFormFile fileName);
        Task<List<MstUserDto>> GetAllUserAsync();
        Task<MstUserDto> GetUserByIdAsync(int userId);
        Task<MstUserDto> GetUserByIdApiAsync(int userId);
        Task<ApiResponseDto<object>> UpdateUserDetailsAsync(MstUserDto dto, IFormFile profileImage);
        Task<List<MstUserRolesConfigDto>> GetAllUserRolesConfigAsync(int? roleId = null, int? facilityId = null);
        Task<ApiResponseDto<bool>> UpdateUserProfileAsync(string password, IFormFile profileImage, int userId, string? ExistingProfileImageUrl, string EmployeeCode, string ContactNumber);
        Task<List<object>> GetUsersByFacilityId(int facilityId);
        Task<List<FacilityRoleDto>> GetUserFacilitiesWithRoles(int userId);
        Task<List<MstFacilityDto>> GetUserFacilitiesByFacilityId(List<int>? facilityId);
        Task<List<MstLanguageDto>> GetAllLanguagesAsync();
    }
}
