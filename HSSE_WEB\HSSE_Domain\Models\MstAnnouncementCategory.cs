﻿using System;
using System.Collections.Generic;

namespace HSSE_Domain.Models;

public partial class MstAnnouncementCategory
{
    public int AnnoucementCategoryId { get; set; }

    public string AnnoucementCategoryName { get; set; } = null!;

    public bool Status { get; set; }

    public DateTime? CreatedAt { get; set; }

    public int? CreatedBy { get; set; }

    public DateTime? ModifiedAt { get; set; }

    public int? ModifiedBy { get; set; }

    public virtual ICollection<MstAnnouncement> MstAnnouncements { get; set; } = new List<MstAnnouncement>();
}
