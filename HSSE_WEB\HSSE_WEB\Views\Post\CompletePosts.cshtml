﻿@{
    ViewData["Title"] = "Complete Posts";
}
<div class="card mt-2">
    <div class="card-body">
        <div class="d-flex justify-content-between align-items-center">

            <h4 class="card-title">Complete Posts</h4>
            <div class="form-inline">
                <button id="btn-toggle-view" class="btn btn-primary btn-sm p-2">
                    <i class="fa fa-th-large"></i> Card View
                </button>
            </div>
        </div>
        <div id="assigned-posts-container" class="row py-4"></div>
    </div>
</div>
<div class="modal fade" id="commentModal" tabindex="-1" aria-labelledby="commentModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-scrollable modal-md">
        <div class="modal-content">

            <!-- <PERSON><PERSON> Header (Fixed) -->
            <div class="modal-header">
                <h5 class="modal-title">Comments</h5>
                <button type="button" class="close" data-dismiss="modal"><span>&times;</span></button>
            </div>

            <!-- Modal Body (Flexible Layout) -->
            <div class="modal-body d-flex flex-column" style="height: 500px;">

                <!-- Scrollable Comment List -->
                <div id="comment-list" class="mb-3 overflow-auto" style="flex: 1 1 auto;"></div>

                <!-- Input Area (Fixed at bottom) -->
                <div class="input-group">
                    <textarea id="new-comment-text" class="form-control" placeholder="Write a comment..." rows="2" style="resize: none;"></textarea>
                    <button class="btn btn-primary" id="send-comment-btn">Send</button>
                </div>

            </div>

        </div>
    </div>
</div>
<script>
        var getPostsByUserId = '@Url.Action("GetAssignedPosts", "Post")?statusId=';
    const basePath = '@Url.Content("~/")'; // returns '/UEMS/HSSEWeb/' in production
        var userId = '@ViewBag.UserId'; // Set this in your controller or layout
            var toggleLikeUrl = '@Url.Action("ToggleLike", "Post")?postId=';
                    var getAllGetPostComments = '@Url.Action("GetAllGetPostComments", "Post")?postId=';
                        var createComment =  '@Url.Action("CreateComment", "Post")';
                         var status = 3;

</script>

@section Scripts {
    <script src="~/js/Post/viewAssignedPosts.js"></script>
} 