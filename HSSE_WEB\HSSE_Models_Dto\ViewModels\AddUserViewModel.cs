﻿using HSSE_Models_Dto.ModelsDto;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HSSE_Models_Dto.ViewModels
{
    public class AddUserViewModel
    {
        public MstUserDto NewUser { get; set; } = new MstUserDto();
        public List<MstFacilityDto> Facilities { get; set; } = new List<MstFacilityDto>();
        public List<MstUsersRoleDto> Roles { get; set; } = new List<MstUsersRoleDto>();
        public List<MstUserDto> ExistingUsers { get; set; } = new List<MstUserDto>();

        public List<MstUserRolesConfigDto> UserRoleMappings { get; set; } = new List<MstUserRolesConfigDto>();
        public List<MstOrganisationDto> Organisation { get; set; } = new List<MstOrganisationDto>();
        public List<GroupDto> Group { get; set; } = new List<GroupDto>();

        public List<MstAnnouncementDto> Announcements { get; set; } = new List<MstAnnouncementDto>();
        public List<MstAnnoucementCategoryDto> AnnouncementsCategory { get; set; }

        public List<MstPostCategoryDto> MstPostCategory { get; set; } = new List<MstPostCategoryDto>();
        public  MstUserDto userDetails { get; set; }
        public MstNewInspectionDto inspectionDetails { get; set; }

        public List<MstDocumentLibraryDto> DocumentLibrary { get; set; } = new List<MstDocumentLibraryDto>();

        public List<MstInspectionCategoryDto> InspectionCategory { get; set; } = new List<MstInspectionCategoryDto>();
        public List<MstLanguageDto> Languages { get; set; } = new List<MstLanguageDto>();
        public List<MstInspectionDto> InspectionDetails { get; set; } = new List<MstInspectionDto>();
    }
}
