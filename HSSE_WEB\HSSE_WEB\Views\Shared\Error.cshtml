﻿@model ErrorViewModel
@{
    ViewData["Title"] = "Error";
}
<head>
    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title>YoraUI Admin</title>
    <!-- base:css -->
    <link rel="stylesheet" href="~/Content/vendors/mdi/css/materialdesignicons.min.css">
    <link rel="stylesheet" href="~/Content/vendors/css/vendor.bundle.base.css">
    <!-- endinject -->
    <!-- plugin css for this page -->
    <link rel="stylesheet" href="~/Content/vendors/jqvmap/jqvmap.min.css">
    <link rel="stylesheet" href="~/Content/vendors/flag-icon-css/css/flag-icon.min.css">
    <!-- End plugin css for this page -->
    <!-- inject:css -->
    <link rel="stylesheet" href="~/Content/css/vertical-layout-light/style.css">
    <!-- endinject -->
    <link rel="shortcut icon" href="~/Content/images/favicon.png" />
</head>

<body>
    <div class="container-scroller">
        <div class="container-fluid page-body-wrapper full-page-wrapper">
            <div class="content-wrapper d-flex align-items-center text-center error-page bg-info">
                <div class="row flex-grow">
                    <div class="col-lg-7 mx-auto text-white">
                        <div class="row align-items-center d-flex flex-row">
                            <div class="col-lg-6 text-lg-right pr-lg-4">
                                <h1 class="display-1 mb-0">500</h1>
                            </div>
                            <div class="col-lg-6 error-page-divider text-lg-left pl-lg-4">
                                <h2>SORRY!</h2>
                                <h3 class="font-weight-light">Internal server error!</h3>
                            </div>
                        </div>
                        <div class="row mt-5">
                            <div class="col-12 text-center mt-xl-2">
                                @if (ViewBag.ErrorMessage != null)
                                {
                                    <p class="text-white">@ViewBag.ErrorMessage</p>
                                }
                                <a class="text-white font-weight-medium" asp-controller="Home" asp-action="login">Back to dashboard</a>
                            </div>
                        </div>
                        <div class="row mt-5">
                            <div class="col-12 mt-xl-2">
                                <p class="text-white font-weight-medium text-center">Copyright &copy; 2018  All rights reserved.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- content-wrapper ends -->
        </div>
        <!-- page-body-wrapper ends -->
    </div>
    <!-- container-scroller -->
    <!-- base:js -->
    <script src="~/Content/vendors/js/vendor.bundle.base.js"></script>
    <!-- endinject -->
    <!-- Plugin js for this page-->
    <script src="~/Content/vendors/jquery.flot/jquery.flot.js"></script>
    <script src="~/Content/vendors/jquery.flot/jquery.flot.pie.js"></script>
    <script src="~/Content/vendors/jquery.flot/jquery.flot.resize.js"></script>
    <script src="~/Content/vendors/jqvmap/jquery.vmap.min.js"></script>
    <script src="~/Content/vendors/jqvmap/maps/jquery.vmap.world.js"></script>
    <script src="~/Content/vendors/jqvmap/maps/jquery.vmap.usa.js"></script>
    <script src="~/Content/vendors/peity/jquery.peity.min.js"></script>
    <script src="~/Content/js/jquery.flot.dashes.js"></script>
    <!-- End plugin js for this page-->
    <!-- inject:js -->
    <script src="~/Content/js/off-canvas.js"></script>
    <script src="~/Content/js/hoverable-collapse.js"></script>
    <script src="~/Content/js/template.js"></script>
    <script src="~/Content/js/settings.js"></script>
    <script src="~/Content/js/todolist.js"></script>
    <script src="~/Content/js/file-upload.js"></script>

    <!-- endinject -->
    <!-- plugin js for this page -->
    <!-- End plugin js for this page -->
    <!-- Custom js for this page-->
    <script src="~/Content/js/dashboard.js"></script>
    <!-- endinject -->
</body>