using HSSE_Models_Dto.ModelsDto;
using HSSE_Service.ServiceResponce;
using System.Threading.Tasks;

namespace HSSE_Service.InterfaceService
{
    public interface IEventService
    {
        Task<ApiResponseDto<object>> InsertOrUpdateEventAsync(MstEventDto dto);
        Task<ApiResponseDto<List<MstEventDto>>> GetEventsAsync(int? userId, List<int>? facilityIds);
        Task<ApiResponseDto<List<MstEventDto>>> GetEventsByFacilityIdAsync(
                    List<int>? facilityIds,
                    bool isAdmin,
                    int? userId,
                    int? dateFilterId = null,
                    string? search = null);
        Task<ApiResponseDto<object>> SaveEventLikeAsync(int eventId, int userId);
        Task<ApiResponseDto<object>> SaveEventResponseAsync(EventResponseDto dto);
        Task<ApiResponseDto<object>> ToggleEventActivationAsync(int eventId);
        Task<ApiResponseDto<List<object>>> GetEventRsvpDetailsAsync(int eventId);
        Task<ApiResponseDto<MstEventDto>> GetEventByIdAsync(int eventId, int userId);
        // Add more methods as needed
    }
} 