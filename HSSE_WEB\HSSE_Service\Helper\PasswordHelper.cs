﻿using System.Security.Cryptography;
using System.Text;

namespace HSSE_Service.Helper
{
    public class PasswordHelper
    {
        // Encrypts the password
        public static string EncryptPassword(string password)
        {
            using (Aes aesAlg = Aes.Create())
            {
                aesAlg.Key = Encoding.UTF8.GetBytes("1234567890123456"); // 16-byte key
                aesAlg.IV = Encoding.UTF8.GetBytes("1234567890123456");  // 16-byte IV
                aesAlg.Mode = CipherMode.CBC; // Ensure CBC mode
                aesAlg.Padding = PaddingMode.PKCS7; // Ensure PKCS7 padding

                ICryptoTransform encryptor = aesAlg.CreateEncryptor(aesAlg.Key, aesAlg.IV);

                using (MemoryStream msEncrypt = new MemoryStream())
                {
                    using (CryptoStream csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write))
                    {
                        using (StreamWriter swEncrypt = new StreamWriter(csEncrypt))
                        {
                            swEncrypt.Write(password);
                        }
                    }

                    return Convert.ToBase64String(msEncrypt.ToArray());
                }
            }
        }

        // Decrypts the encrypted password
        // Decrypts the encrypted password
        public static string DecryptPassword(string encryptedPassword)
        {
            if (string.IsNullOrWhiteSpace(encryptedPassword))
                return string.Empty;

            var encryptPass = EncryptPassword("qwe123");
            using (Aes aesAlg = Aes.Create())
            {
                aesAlg.Key = Encoding.UTF8.GetBytes("1234567890123456"); // 16-byte key
                aesAlg.IV = Encoding.UTF8.GetBytes("1234567890123456");  // 16-byte IV
                aesAlg.Mode = CipherMode.CBC; // Ensure CBC mode
                aesAlg.Padding = PaddingMode.PKCS7; // Ensure PKCS7 padding

                ICryptoTransform decryptor = aesAlg.CreateDecryptor(aesAlg.Key, aesAlg.IV);

                using (MemoryStream msDecrypt = new MemoryStream(Convert.FromBase64String(encryptedPassword ?? "0")))
                {
                    using (CryptoStream csDecrypt = new CryptoStream(msDecrypt, decryptor, CryptoStreamMode.Read))
                    {
                        using (StreamReader srDecrypt = new StreamReader(csDecrypt))
                        {
                            return srDecrypt.ReadToEnd(); // Return the decrypted password
                        }
                    }
                }
            }
        }
    }
}
