using System;
using System.IO;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Hosting;
using Microsoft.EntityFrameworkCore;
using HSSE_WEB.Models;

namespace HSSE_WEB.Services
{
    public class DocumentLibraryService
    {
        private readonly ApplicationDbContext _context;
        private readonly IWebHostEnvironment _environment;

        public DocumentLibraryService(ApplicationDbContext context, IWebHostEnvironment environment)
        {
            _context = context;
            _environment = environment;
        }

        public async Task<ApiResponse> RemoveDocumentAsync(int documentId)
        {
            try
            {
                var document = await _context.Documents.FindAsync(documentId);
                if (document == null)
                {
                    return new ApiResponse
                    {
                        Status = 404,
                        Message = "Document not found"
                    };
                }

                // Delete the physical file if it exists
                if (!string.IsNullOrEmpty(document.FilePath))
                {
                    var filePath = Path.Combine(_environment.WebRootPath, document.FilePath.TrimStart('/'));
                    if (File.Exists(filePath))
                    {
                        File.Delete(filePath);
                    }
                }

                _context.Documents.Remove(document);
                await _context.SaveChangesAsync();

                return new ApiResponse
                {
                    Status = 200,
                    Message = "Document removed successfully"
                };
            }
            catch (Exception ex)
            {
                return new ApiResponse
                {
                    Status = 500,
                    Message = $"Error removing document: {ex.Message}"
                };
            }
        }
    }
} 