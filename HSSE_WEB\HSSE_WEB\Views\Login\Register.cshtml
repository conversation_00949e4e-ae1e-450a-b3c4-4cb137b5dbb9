﻿@{
    Layout = "~/Views/Shared/_LoginLayout.cshtml";
    ViewData["Title"] = "Register";
}


<div class="container-scroller">
    <div class="container-fluid page-body-wrapper full-page-wrapper">
        <div class="content-wrapper d-flex align-items-center auth px-0">
            <div class="row w-100 mx-0">
                <div class="col-lg-4 mx-auto">
                    <div class="auth-form-light text-left py-2 px-4 px-sm-5 border">
                        <div class="brand-logo">
                            <img src="~/Content/images/dashboard/image-removebg-preview (1).png" alt="logo">
                        </div>
                        <h4>New here?</h4>
                        <h6 class="font-weight-light">Signing up is easy. It only takes a few steps</h6>

                        <!-- Form starts -->
                        <form id="registerForm" class="pt-3">
                            <div class="form-group">
                                <input type="text" class="form-control form-control-lg" id="username" placeholder="Username" required>
                            </div>
                            <div class="form-group">
                                <input type="text" class="form-control form-control-lg" id="firstName" placeholder="First Name" required>
                            </div>
                            <div class="form-group">
                                <input type="text" class="form-control form-control-lg" id="lastName" placeholder="Last Name" required>
                            </div>
                            <div class="form-group">
                                <input type="email" class="form-control form-control-lg" id="email" placeholder="Email" required>
                            </div>
                            <div class="form-group">
                                <div class="input-group">
                                    <input type="text" class="form-control form-control-lg file-upload-info" id="password" placeholder="Password" required>
                                    <span class="input-group-append">
                                        <i class="fa fa-eye-slash position-absolute" id="togglePassword"
                                           style="top: 50%; right: 15px; transform: translateY(-50%); cursor: pointer;"></i>
                                    </span>
                                </div>
                                <small id="passwordFeedback" class="text-danger d-block mt-1"></small>
                            </div>
                            <div class="mb-4">
                                <div class="form-check">
                                    <label class="form-check-label text-muted">
                                        <input type="checkbox" class="form-check-input" id="termsCheck">
                                        I agree to all Terms & Conditions
                                    </label>
                                </div>
                            </div>
                            <div class="mt-3">
                                <button type="submit" class="btn btn-block btn-primary btn-lg font-weight-medium auth-form-btn">SIGN UP</button>
                            </div>
                            <div class="text-center mt-4 font-weight-light">
                                Already have an account?
                                <a asp-controller="Login" asp-action="Login" class="text-primary">Login</a>
                            </div>
                        </form>
                        <!-- Form ends -->

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="~/js/User/register.js"></script>
}
