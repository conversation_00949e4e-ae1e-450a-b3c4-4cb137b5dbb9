﻿@{
    Layout = "~/Views/Shared/_LoginLayout.cshtml";
    ViewData["Title"] = "Login";
}

<div class="container-scroller">
    <div class="container-fluid page-body-wrapper full-page-wrapper">
        <div class="content-wrapper d-flex align-items-center auth px-0">
            <div class="row w-100 mx-0">
                <div class="col-lg-4 mx-auto">
                    <div class="auth-form-light text-left py-2 px-4 px-sm-5 border">
                        <div class="brand-logo">
                            <img src="~/Content/images/dashboard/image-removebg-preview (1).png" alt="logo">
                        </div>
                        <h4>Hello! let's get started</h4>
                        <h6 class="font-weight-light">Sign in to continue.</h6>
                        <form id="loginForm" class="pt-3">
                            <div class="form-group">
                                <input  class="form-control form-control-lg" id="username" placeholder="Username" required>
                            </div>
                            <div class="form-group">
                            <div class="input-group">
                                <input type="password" class="form-control form-control-lg file-upload-info" id="password" placeholder="Password" required>
                                <span class="input-group-append">
                                    <i class="fa fa-eye-slash position-absolute" id="togglePassword"
                                       style="top: 50%; right: 15px; transform: translateY(-50%); cursor: pointer;"></i>
                                </span>
                            </div>
                            </div>
                            <div class="mt-3">
                                <button id="loginBtn" class="btn btn-block btn-primary btn-lg font-weight-medium auth-form-btn" type="submit">
                                    <span class="btn-text">SIGN IN</span>
                                    <span class="spinner-border spinner-border-sm d-none" role="status" aria-hidden="true"></span>
                                </button>
                            </div>
                            <div class="my-2 d-flex justify-content-between align-items-center">
                      
                                <a href="#" class="auth-link text-black">Forgot password?</a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <!-- content-wrapper ends -->
    </div>
    <!-- page-body-wrapper ends -->
</div>
<script>
    var loginUser = '@Url.Action("LoginUser", "Login")';
        var homePage = '@Url.Action("login", "Home")';

</script>
@section Scripts {
    <script src="~/js/User/Login.js"></script>
}