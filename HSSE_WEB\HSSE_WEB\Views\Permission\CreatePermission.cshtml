﻿@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@{
    ViewData["Title"] = "Create Permission";
}

<div class="row">
    <div class="col-12 grid-margin">
        <div class="card">
            <div class="card-body">
                <h4 class="card-title">Create New Permission</h4>
                <form id="createPermissionForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="menuName">Menu Name</label>
                                <input type="text" class="form-control" id="menuName" name="menuName" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="controllerName">Controller Name</label>
                                <input type="text" class="form-control" id="controllerName" name="controllerName" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="actionName">Action Name</label>
                                <input type="text" class="form-control" id="actionName" name="actionName" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="areaName">Area Name</label>
                                <input type="text" class="form-control" id="areaName" name="areaName">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="routeParams">Route Parameters</label>
                                <input type="text" class="form-control" id="routeParams" name="routeParams">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="icon">Icon</label>
                                <input type="text" class="form-control" id="icon" name="icon">
                            </div>
                        </div>
                    </div>

                     <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="parentMenuId">Parent Menu</label>
                                 <select class="form-control" id="parentMenuId" name="parentMenuId">
                                     <option value="">-- Select Parent Menu --</option>
                                 </select>
                             </div>
                         </div>
                         <div class="col-md-6">
                             <div class="form-group">
                                 <div class="form-check">
                                     <label class="form-check-label">
                                         <input type="checkbox" class="form-check-input" id="isActive" name="isActive" checked>
                                         Is Active
                                     </label>
                                 </div>
                             </div>
                         </div>
                    </div>

                    <div class="row">
                         <div class="col-md-6">
                            <div class="form-group">
                                <label for="fileUpload">File upload</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="fileUpload" placeholder="Upload Document" readonly>
                                    <div class="input-group-append">
                                        <button class="btn btn-primary" type="button">Upload</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12 text-right">
                             <button type="submit" class="btn btn-primary mt-3"><i class="mdi mdi-upload"></i> Upload Document</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="~/js/permission/createPermission.js"></script>
}
