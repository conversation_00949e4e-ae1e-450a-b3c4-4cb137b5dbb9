using AutoMapper;
using HSSE_Models_Dto.ModelsDto;
using HSSE_Service.Constant;
using HSSE_Service.InterfaceService;
using HSSE_Service.ServiceResponce;
using Microsoft.Extensions.Configuration;
using System;
using System.Net.Http;
using System.Net.Http.Json;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Web;
using Microsoft.AspNetCore.Http;
using HSSE_Domain.Models;
using Microsoft.EntityFrameworkCore;

namespace HSSE_Service.DataManager
{
    public class InspectionService : IInspectionService
    {
        private readonly HttpClient _httpClient;
        private readonly string _apiBaseUrl;
        private readonly HsseDbLatestContext _context;


        public InspectionService(IHttpClientFactory httpClientFactory, IConfiguration configuration, HsseDbLatestContext context)
        {
            _context = context;
            _httpClient = httpClientFactory.CreateClient("AuthenticatedClient");
            var baseUrl = configuration["ExternalApi:BaseUrl"];
            if (!string.IsNullOrEmpty(baseUrl))
            {
                _httpClient.BaseAddress = new Uri(baseUrl);
            }
            _apiBaseUrl = configuration["ExternalApi:BaseUrl"];
        }

        public async Task<ApiResponseDto<object>> InsertOrUpdateInspectionAsync(MstNewInspectionDto dto)
        {
            try
            {
                var apiUrl = $"{_apiBaseUrl}/api/Inspection/CreateOrUpdateInspection";
                var response = await _httpClient.PostAsJsonAsync(apiUrl, dto);
                if (response.IsSuccessStatusCode)
                {
                    var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<object>>();
                    return ApiResponseDto<object>.SuccessResponse(apiResult?.Message ?? "Inspection saved successfully.", (int)response.StatusCode, apiResult?.Data);
                }
                else
                {
                    return ApiResponseDto<object>.ErrorResponse("Failed to save inspection.", (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse("Failed to save inspection.", 500, new List<HSSE_Service.ServiceResponce.Error> {
                    new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
                });
            }
        }

        public async Task<ApiResponseDto<List<MstInspectionDto>>> GetInspectionsAsync(int userId)
        {
            try
            {
                var apiUrl = $"{_apiBaseUrl}/api/Inspection/GetInspections?userId={userId}";
                var response = await _httpClient.GetAsync(apiUrl);
                if (response.IsSuccessStatusCode)
                {
                    var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<List<MstInspectionDto>>>();
                    return ApiResponseDto<List<MstInspectionDto>>.SuccessResponse(apiResult?.Message ?? "Inspections fetched successfully.", (int)response.StatusCode, apiResult?.Data ?? new List<MstInspectionDto>());
                }
                else
                {
                    return ApiResponseDto<List<MstInspectionDto>>.ErrorResponse("Failed to fetch inspections.", (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<List<MstInspectionDto>>.ErrorResponse("Failed to fetch inspections.", 500, new List<HSSE_Service.ServiceResponce.Error> {
                    new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
                });
            }
        }

        public async Task<ApiResponseDto<MstNewInspectionDto>> GetInspectionByIdAsync(int inspectionId)
        {
            try
            {
                var apiUrl = $"{_apiBaseUrl}/api/Inspection/GetInspectionById?inspectionId={inspectionId}";
                var response = await _httpClient.GetAsync(apiUrl);
                if (response.IsSuccessStatusCode)
                {
                    var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<MstNewInspectionDto>>();
                    return ApiResponseDto<MstNewInspectionDto>.SuccessResponse(apiResult?.Message ?? "Inspection fetched successfully.", (int)response.StatusCode, apiResult?.Data);
                }
                else
                {
                    return ApiResponseDto<MstNewInspectionDto>.ErrorResponse("Failed to fetch inspection.", (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<MstNewInspectionDto>.ErrorResponse("Failed to fetch inspection.", 500, new List<HSSE_Service.ServiceResponce.Error> {
                    new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
                });
            }
        }

        public async Task<ApiResponseDto<object>> CreateOrUpdateActionPartyAsync(MstActionPartyDto dto)
        {
            try
            {
                var apiUrl = $"{_apiBaseUrl}/api/Inspection/CreateOrUpdateActionParty";
                var response = await _httpClient.PostAsJsonAsync(apiUrl, dto);
                if (response.IsSuccessStatusCode)
                {
                    var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<object>>();
                    return ApiResponseDto<object>.SuccessResponse(apiResult?.Message ?? "Action Party saved successfully.", (int)response.StatusCode, apiResult?.Data);
                }
                else
                {
                    return ApiResponseDto<object>.ErrorResponse("Failed to save action party.", (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse("Failed to save action party.", 500, new List<HSSE_Service.ServiceResponce.Error> {
                    new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
                });
            }
        }

        public async Task<ApiResponseDto<object>> UpdateInspectionItemStatusAsync(UpdateInspectionStatusDto dto)
        {
            try
            {
                var apiUrl = $"{_apiBaseUrl}/api/Inspection/UpdateInspectionItemStatus";
                var response = await _httpClient.PostAsJsonAsync(apiUrl, dto);
                if (response.IsSuccessStatusCode)
                {
                    var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<object>>();
                    return ApiResponseDto<object>.SuccessResponse(apiResult?.Message ?? "Action Party saved successfully.", (int)response.StatusCode, apiResult?.Data);
                }
                else
                {
                    return ApiResponseDto<object>.ErrorResponse("Failed to save action party.", (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse("Failed to save action party.", 500, new List<HSSE_Service.ServiceResponce.Error> {
                    new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
                });
            }
        }
        public async Task<ApiResponseDto<object>> GetActionPartyByIdAsync(int id)
        {
            try
            {
                var apiUrl = $"{_apiBaseUrl}/api/Inspection/GetActionPartyById?id={id}";
                var response = await _httpClient.GetAsync(apiUrl);
                if (response.IsSuccessStatusCode)
                {
                    var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<object>>();
                    return ApiResponseDto<object>.SuccessResponse(apiResult?.Message ?? "Action Party fetched successfully.", (int)response.StatusCode, apiResult?.Data);
                }
                else
                {
                    return ApiResponseDto<object>.ErrorResponse("Failed to fetch action party.", (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse("Failed to fetch action party.", 500, new List<HSSE_Service.ServiceResponce.Error> {
                    new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
                });
            }
        }

        public async Task<ApiResponseDto<List<MstActionPartyDto>>> GetAllActionPartyByFacilityIdAsync(int facilityId)
        {
            try
            {
                var apiUrl = $"{_apiBaseUrl}/api/Inspection/GetAllActionPartyByFacilityId?facilityId={facilityId}";
                var response = await _httpClient.GetAsync(apiUrl);
                if (response.IsSuccessStatusCode)
                {
                    var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<List<MstActionPartyDto>>>();
                    return ApiResponseDto<List<MstActionPartyDto>>.SuccessResponse(apiResult?.Message ?? "Action Parties fetched successfully.", (int)response.StatusCode, apiResult?.Data ?? new List<MstActionPartyDto>());
                }
                else
                {
                    return ApiResponseDto<List<MstActionPartyDto>>.ErrorResponse("Failed to fetch action parties.", (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<List<MstActionPartyDto>>.ErrorResponse("Failed to fetch action parties.", 500, new List<HSSE_Service.ServiceResponce.Error> {
                    new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
                });
            }
        }

        public async Task<ApiResponseDto<List<ActionPartyViewDto>>> GetActionPartyByUserIdAsync(int userId)
        {
            try
            {
                var apiUrl = $"{_apiBaseUrl}/api/Inspection/GetActionPartyByUserId?userId={userId}";
                var response = await _httpClient.GetAsync(apiUrl);
                if (response.IsSuccessStatusCode)
                {
                    var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<List<ActionPartyViewDto>>>();
                    return ApiResponseDto<List<ActionPartyViewDto>>.SuccessResponse(apiResult?.Message ?? "Action Parties fetched successfully.", (int)response.StatusCode, apiResult?.Data ?? new List<ActionPartyViewDto>());
                }
                else
                {
                    return ApiResponseDto<List<ActionPartyViewDto>>.ErrorResponse("Failed to fetch action parties.", (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<List<ActionPartyViewDto>>.ErrorResponse("Failed to fetch action parties.", 500, new List<HSSE_Service.ServiceResponce.Error> {
                    new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
                });
            }
        }
        public async Task<ApiResponseDto<object>> ToggleActionPartiesActivationAsync(int actionPartyId)
        {
            try
            {
                var apiUrl = $"{_apiBaseUrl}/api/Inspection/ToggleActionPartiesActivation?actionPartyId={actionPartyId}";
                var response = await _httpClient.PostAsync(apiUrl, null);
                if (response.IsSuccessStatusCode)
                {
                    var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<object>>();
                    return ApiResponseDto<object>.SuccessResponse(apiResult?.Message ?? "Event activation toggled successfully.", (int)response.StatusCode, apiResult?.Data);
                }
                else
                {
                    return ApiResponseDto<object>.ErrorResponse("Failed to toggle event activation.", (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse("Failed to toggle event activation.", 500, new System.Collections.Generic.List<HSSE_Service.ServiceResponce.Error> {
                    new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
                });
            }
        }

        public async Task<ApiResponseDto<List<MstInspectionDto>>> GetInspectionsByActionPartyAsync(int actionPartyId, int? status)
        {
            try
            {
                var apiUrl = $"{_apiBaseUrl}/api/Inspection/GetInspectionsByActionParty?actionPartyName={actionPartyId}&status={status}";
                var response = await _httpClient.GetAsync(apiUrl);
                if (response.IsSuccessStatusCode)
                {
                    var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<List<MstInspectionDto>>>();
                    return ApiResponseDto<List<MstInspectionDto>>.SuccessResponse(apiResult?.Message ?? "Inspections fetched successfully.", (int)response.StatusCode, apiResult?.Data ?? new List<MstInspectionDto>());
                }
                else
                {
                    return ApiResponseDto<List<MstInspectionDto>>.ErrorResponse("Failed to fetch inspections.", (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<List<MstInspectionDto>>.ErrorResponse("Failed to fetch inspections.", 500, new List<HSSE_Service.ServiceResponce.Error> {
                    new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
                });
            }
        }
        public async Task<ApiResponseDto<List<MstInspectionCategoryDto>>> GetInspectionCategoryAsync()
        {
            try
            {
                var apiUrl = $"{_apiBaseUrl}/api/Inspection/GetInspectionCategory";
                var response = await _httpClient.GetAsync(apiUrl);
                if (response.IsSuccessStatusCode)
                {
                    var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<List<MstInspectionCategoryDto>>>();
                    return ApiResponseDto<List<MstInspectionCategoryDto>>.SuccessResponse(apiResult?.Message ?? "Inspections fetched successfully.", (int)response.StatusCode, apiResult?.Data ?? new List<MstInspectionCategoryDto>());
                }
                else
                {
                    return ApiResponseDto<List<MstInspectionCategoryDto>>.ErrorResponse("Failed to fetch inspections.", (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<List<MstInspectionCategoryDto>>.ErrorResponse("Failed to fetch inspections.", 500, new List<HSSE_Service.ServiceResponce.Error> {
                    new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
                });
            }
        }

        public async Task<ApiResponseDto<List<MstInspectionDto>>> GetUserInspectionsAsync(int? userId = null, List<int>? facilityId = null)
        {
            try
            {
                var uriBuilder = new UriBuilder($"{_apiBaseUrl}/api/Inspection/GetUserInspections");
                var query = HttpUtility.ParseQueryString(uriBuilder.Query);

                if (userId.HasValue)
                    query["userId"] = userId.ToString();

                if (facilityId != null && facilityId.Any())
                {
                    foreach (var id in facilityId)
                    {
                        query.Add("facilityIds", id.ToString());
                    }
                }
                uriBuilder.Query = query.ToString();
                var apiUrl = uriBuilder.ToString();

                var response = await _httpClient.GetAsync(apiUrl);
                if (response.IsSuccessStatusCode)
                {
                    var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<List<MstInspectionDto>>>();
                    return ApiResponseDto<List<MstInspectionDto>>.SuccessResponse(
                        apiResult?.Message ?? "User inspections fetched successfully.",
                        (int)response.StatusCode,
                        apiResult?.Data ?? new List<MstInspectionDto>());
                }
                else
                {
                    return ApiResponseDto<List<MstInspectionDto>>.ErrorResponse("Failed to fetch user inspections.", (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<List<MstInspectionDto>>.ErrorResponse("Failed to fetch user inspections.", 500, new List<HSSE_Service.ServiceResponce.Error> {
            new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
        });
            }
        }

        public async Task<ApiResponseDto<object>> InsertInspectionObservationItemsAsync(MstNewInspectionItemDto dto)
        {
            try
            {
                var apiUrl = $"{_apiBaseUrl}/api/Inspection/InsertInspectionObservationItems";
                var response = await _httpClient.PostAsJsonAsync(apiUrl, dto);
                if (response.IsSuccessStatusCode)
                {
                    var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<object>>();
                    return ApiResponseDto<object>.SuccessResponse(apiResult?.Message ?? "Action Party saved successfully.", (int)response.StatusCode, apiResult?.Data);
                }
                else
                {
                    return ApiResponseDto<object>.ErrorResponse("Failed to save action party.", (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse("Failed to save action party.", 500, new List<HSSE_Service.ServiceResponce.Error> {
                    new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
                });
            }
        }
        public async Task<ApiResponseDto<MstNewInspectionItemDto>> GetInspectionItemByIdAsync(int itemId)
        {
            try
            {
                var response = await _httpClient.GetAsync($"{_apiBaseUrl}/api/Inspection/GetInspectionItemById?itemId={itemId}");
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<ApiResponseDto<MstNewInspectionItemDto>>();
                }

                return ApiResponseDto<MstNewInspectionItemDto>.ErrorResponse("Failed to fetch inspection item", (int)response.StatusCode);
            }
            catch (Exception ex)
            {
                return ApiResponseDto<MstNewInspectionItemDto>.ErrorResponse("Failed to get details.", 500, new List<HSSE_Service.ServiceResponce.Error> {
                    new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
                });
            }
        }

        public async Task<ApiResponseDto<object>> DeleteObservationItemAsync(int itemId)
        {
            try
            {
                var item = await _context.MstInspectionItems
                    .FirstOrDefaultAsync(i => i.ItemId == itemId);

                if (item == null)
                {
                    return ApiResponseDto<object>.ErrorResponse(
                        "Observation item not found.",
                        StatusCodes.Status404NotFound
                    );
                }

                _context.MstInspectionItems.Remove(item);
                await _context.SaveChangesAsync();

                return ApiResponseDto<object>.SuccessResponse(
                    "Observation item deleted successfully.",
                    StatusCodes.Status200OK,
                    new { item.ItemId }
                );
            }
            catch (Exception ex)
            {
                // Optional: Log the exception
                return ApiResponseDto<object>.ErrorResponse(
                    "Internal server error while deleting the observation item.",
                    StatusCodes.Status500InternalServerError
                );
            }
        }

    }
} 