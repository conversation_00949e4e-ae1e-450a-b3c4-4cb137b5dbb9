using System.Threading.Tasks;
using HSSE_Models_Dto.ModelsDto;
using HSSE_Service.ServiceResponce;
using System.Collections.Generic;

namespace HSSE_Service.InterfaceService
{
    public interface IGroupService
    {
        Task<ApiResponseDto<object>> CreateGroupAsync(GroupCreateDto dto, int createdBy);
        Task<ApiResponseDto<List<GroupDto>>> GetAllGroupsAsync();
        Task<ApiResponseDto<GroupDto>> GetGroupByIdAsync(int id);
        Task<ApiResponseDto<object>> DeleteGroupAsync(int groupId);
        Task<ApiResponseDto<object>> InsertGroupMemberAsync(int groupId, List<int> userIds, int OldGroupId);
        Task<ApiResponseDto<object>> UpdateGroupMemberAsync(int groupId, List<int> userIds, int OldGroupId);
        Task<ApiResponseDto<List<GroupMemberListDto>>> GetGroupMembersAsync();
        Task<ApiResponseDto<GroupMemberListDto>> GetGroupMembersByGroupIdAsync(int groupId);
        Task<ApiResponseDto<object>> DeleteAllGroupMembersByGroupIdAsync(int groupId);
    }
} 