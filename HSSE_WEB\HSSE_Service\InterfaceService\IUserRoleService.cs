﻿using HSSE_Models_Dto.ModelsDto;
using HSSE_Service.ServiceResponce;
using Org.BouncyCastle.Asn1.Ocsp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HSSE_Service.InterfaceService
{
    public interface IUserRoleService
    {
        Task<List<MstUsersRoleDto>> GetAllUserRoleAsync();
        Task<MstUsersRoleDto> GetAllUserRoleByIdAsync(int roleId);
        Task<ApiResponseDto<object>> SaveRoleAsync(MstUsersRoleDto dto);
        Task<ApiResponseDto<object>> ToggleRoleActivationAsync(int roleId);
        Task<ApiResponseDto<object>> SaveUserRoleMappingAsync(MstUserRolesConfigDto dto);
        Task<List<MstUserRolesConfigDto>> GetAllUserRoleMappingAsync();
        Task<ApiResponseDto<object>> DeleteUserRoleMappingAsync(int userRoleConfigId);
        Task<MstUserRolesConfigDto> GetAllUserRoleMappingByIdAsync(int id);
    }
}
