using HSSE_Models_Dto.ModelsDto;
using HSSE_Service.InterfaceService;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace HSSE_WEB.Controllers
{
    public class PostCategoryController : Controller
    {
        private readonly IPostService _postService;
        public PostCategoryController(IPostService postService)
        {
            _postService = postService;
        }
        public IActionResult CreatePostCategory()
        {
            return View();
        }
        [HttpPost]
        public async Task<IActionResult> InsertOrUpdate([FromBody] MstPostCategoryDto dto)
        {
            var result = await _postService.InsertOrUpdatePostCategoryAsync(dto);
            return StatusCode(result.Status, result);
        }
        [HttpGet]
        public async Task<IActionResult> GetAll()
        {
            var result = await _postService.GetPostCategoriesAsync();
            return StatusCode(result.Status, result);
        }
        [HttpGet]
        public async Task<IActionResult> GetById(int catId)
        {
            var result = await _postService.GetPostCategoryByIdAsync(catId);
            return StatusCode(result.Status, result);
        }
        [HttpPost]
        public async Task<IActionResult> Delete(int catId)
        {
            var result = await _postService.DeletePostCategoryAsync(catId);
            return StatusCode(result.Status, result);
        }
    }
} 