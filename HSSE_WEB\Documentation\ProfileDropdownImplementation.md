# Profile and Logout Section Implementation

## Overview
This document describes the implementation of a profile and logout section in the top right corner of the navbar for the HSSE Web application.

## Features Implemented

### 1. Profile Dropdown in Navbar
- **Location**: Top right corner of the main navigation bar
- **Components**:
  - User profile image with online status indicator
  - User display name (First Name + Last Name or Username)
  - User email address
  - Dropdown menu with profile actions

### 2. Dropdown Menu Options
- **My Profile**: Links to the user's profile edit page (`/User/EditProfile`)
- **Settings**: Placeholder for future settings functionality
- **Sign Out**: Logout functionality that clears session and redirects to login

### 3. Responsive Design
- **Desktop**: Shows full profile information (image, name, email)
- **Tablet**: Hides email on smaller screens
- **Mobile**: Shows only profile image and dropdown

## Files Modified/Created

### 1. Layout File
**File**: `Views/Shared/_Layout.cshtml`
**Changes**:
- Added profile dropdown HTML structure in navbar
- Added CSS reference for custom styling
- Added JavaScript for logout functionality and profile data loading

### 2. Custom CSS
**File**: `wwwroot/Content/css/profile-navbar.css`
**Features**:
- Responsive profile dropdown styling
- Hover effects and animations
- Dark theme support
- Mobile-friendly design

## Technical Implementation

### Profile Image Handling
- **Default Avatar**: SVG-based default user icon when no profile image is available
- **Error Handling**: Fallback to default avatar if profile image fails to load
- **Source Priority**: 
  1. User's uploaded profile image from localStorage
  2. Default SVG avatar

### User Data Sources
1. **Primary**: localStorage userInfo (set during login)
2. **Fallback**: Session data from server-side

### Logout Functionality
- Clears localStorage and sessionStorage
- Clears browser cookies
- Redirects to login page to clear server session

## Usage

### For Users
1. **View Profile**: Click on profile image/name to open dropdown
2. **Edit Profile**: Click "My Profile" to navigate to profile edit page
3. **Logout**: Click "Sign Out" to securely logout

### For Developers
1. **Customization**: Modify `profile-navbar.css` for styling changes
2. **Settings Page**: Uncomment settings redirect in JavaScript when settings page is ready
3. **Profile Data**: User data is automatically loaded from localStorage/session

## Browser Compatibility
- Modern browsers (Chrome, Firefox, Safari, Edge)
- Responsive design for mobile devices
- Graceful degradation for older browsers

## Security Features
- Secure logout process
- Session and local storage cleanup
- CSRF protection through ASP.NET Core

## Future Enhancements
1. **Settings Page**: Implement user preferences and settings
2. **Notifications**: Add notification dropdown next to profile
3. **Theme Toggle**: Add dark/light theme switcher
4. **Status Indicators**: More status options (away, busy, etc.)

## Troubleshooting

### Profile Image Not Showing
- Check if user has uploaded a profile image
- Verify Firebase storage configuration
- Check browser console for image loading errors

### Dropdown Not Working
- Ensure Bootstrap JavaScript is loaded
- Check for JavaScript errors in console
- Verify dropdown HTML structure

### Logout Issues
- Check session configuration in Program.cs
- Verify logout redirect URL is correct
- Check for JavaScript errors during logout process

## Code Examples

### Adding New Dropdown Item
```html
<a class="dropdown-item" asp-controller="User" asp-action="NewAction">
    <i class="mdi mdi-icon-name text-primary"></i>
    New Action
</a>
```

### Customizing Profile Data Display
```javascript
// In the JavaScript section of _Layout.cshtml
if (userData.customField) {
    $('#customElement').text(userData.customField);
}
```

### Styling Modifications
```css
/* In profile-navbar.css */
.nav-profile-custom {
    /* Your custom styles here */
}
```

## Testing
1. **Login**: Verify profile appears after successful login
2. **Profile Image**: Test with and without profile images
3. **Responsive**: Test on different screen sizes
4. **Logout**: Verify complete session cleanup
5. **Navigation**: Test all dropdown menu links

## Support
For issues or questions regarding this implementation, please refer to the development team or create an issue in the project repository.
