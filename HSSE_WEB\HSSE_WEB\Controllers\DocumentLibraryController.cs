using HSSE_Models_Dto.ModelsDto;
using HSSE_Service.InterfaceService;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using System.Collections.Generic;
using System;
using HSSE_Models_Dto.ViewModels;

namespace HSSE_WEB.Controllers
{
    public class DocumentLibraryController : Controller
    {
        private readonly IDocumentLibraryService _documentLibraryService;
        public DocumentLibraryController(IDocumentLibraryService documentLibraryService)
        {
            _documentLibraryService = documentLibraryService;
        }

        public async Task <IActionResult> CreateDocument()
        {
            var result = await _documentLibraryService.GetDocumentsLibraryAsync();
            var viewModal = new AddUserViewModel
            {
                DocumentLibrary = result.Data ?? new List<MstDocumentLibraryDto>()
            };
            return View(viewModal);
        }
        public IActionResult ViewDocument()
        {
            return View();
        }
        [HttpPost]
        public async Task<IActionResult> CreateOrUpdateDocument([FromBody] MstDocumentLibraryDto dto)
        {
            dto.CreatedBy = int.Parse(HttpContext?.Session?.GetString("UserId"));
            var result = await _documentLibraryService.CreateOrUpdateDocumentAsync(dto);
            return StatusCode(result.Status, result);
        }

        [HttpGet]
        public async Task<IActionResult> GetDocuments()
        {
            var result = await _documentLibraryService.GetDocumentsAsync();
            return StatusCode(result.Status, result);
        }
        [HttpGet]
        public async Task<IActionResult> GetDocumentById(int documentId)
        {
            var result = await _documentLibraryService.GetDocumentByIdAsync(documentId);
            return StatusCode(result.Status, result);
        }
        
        [HttpGet]
        public async Task<IActionResult> RemoveDocument()
        {
            var result = await _documentLibraryService.GetDocumentsLibraryAsync();
            var viewModal = new AddUserViewModel
            {
                DocumentLibrary = result.Data ?? new List<MstDocumentLibraryDto>()
            };
            return View(viewModal);
        }
        [HttpGet]
        public async Task<IActionResult> GetUserDocuments()
        {
           //var userId = int.Parse(HttpContext?.Session?.GetString("UserId"));

            var result = await _documentLibraryService.GetDocumentsAsync();
            return StatusCode(result.Status, result);
        }
        [HttpDelete]
        public async Task<IActionResult> RemoveDocument(int documentId)
        {
            try
            {
                var deletedBy = int.Parse(HttpContext?.Session?.GetString("UserId"));

                var result = await _documentLibraryService.DeleteDocumentAsync(documentId, deletedBy);
                return StatusCode(result.Status, result);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { status = 500, message = ex.Message });
            }
        }
    }
} 