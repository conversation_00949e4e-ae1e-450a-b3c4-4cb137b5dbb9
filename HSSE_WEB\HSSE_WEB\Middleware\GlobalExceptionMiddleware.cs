﻿using System.Net;
using System.Text.Json;

namespace HSSE_WEB.Middleware
{
    public class GlobalExceptionMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<GlobalExceptionMiddleware> _logger;
        private readonly IWebHostEnvironment _env;

        public GlobalExceptionMiddleware(IWebHostEnvironment env, RequestDelegate next, ILogger<GlobalExceptionMiddleware> logger)
        {
            _next = next;
            _logger = logger;
            _env = env;
        }
        public async Task Invoke(HttpContext context)
        {
            try
            {
                var path = context.Request.Path.Value?.ToLower();

                // Allow access to these paths and static files
                var isAllowedPath =
                    path.StartsWith("/login/index") ||
                    path.StartsWith("/login/login") ||
                    path.StartsWith("/css") ||
                    path.StartsWith("/js") ||
                    path.StartsWith("/images") ||
                    path.StartsWith("/lib") ||
                    path.StartsWith("/externalfiles");

                // Allow /home/<USER>"token" is present in the query
                var isTokenLoginPath = path.StartsWith("/home/<USER>") && context.Request.Query.ContainsKey("token");

                if (!isAllowedPath && !isTokenLoginPath)
                {
                    var hasToken = context.Session.TryGetValue("Token", out var tokenBytes);
                    var token = hasToken ? System.Text.Encoding.UTF8.GetString(tokenBytes) : null;

                    if (string.IsNullOrEmpty(token))
                    {
                        context.Response.Redirect($"{context.Request.PathBase}/Login/Index");
                        return;
                    }
                }

                await _next(context); // continue pipeline
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An unhandled exception occurred.");
                await HandleExceptionAsync(context, ex);
            }
        }


        private Task HandleExceptionAsync(HttpContext context, Exception exception)
        {
            // Log the exception
            _logger.LogError(exception, "An unexpected error occurred.");

            // Set the response status code
            context.Response.StatusCode = (int)HttpStatusCode.InternalServerError;

            // Determine whether the environment is development or production
            if (_env.IsDevelopment())
            {
                // In development, we can show more detailed error messages
                context.Response.Redirect($"{context.Request.PathBase}/home/<USER>");
            }
            else
            {
                // In production, redirect to a generic error page
                context.Response.Redirect($"{context.Request.PathBase}/home/<USER>");
            }

            return Task.CompletedTask;
        }
    }
}
