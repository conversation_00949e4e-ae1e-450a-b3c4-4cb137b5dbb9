﻿using System;
using System.Collections.Generic;

namespace HSSE_Domain.Models;

public partial class MstActionParty
{
    public int ActionPartyId { get; set; }

    public string Name { get; set; } = null!;

    public DateTime? CreatedAt { get; set; }

    public int? CreatedBy { get; set; }

    public DateTime? ModifiedAt { get; set; }

    public int? ModifiedBy { get; set; }

    public bool IsActive { get; set; }

    public int? FacilityId { get; set; }

    public string? Observation { get; set; }

    public string? ObservationMediaUrl { get; set; }

    public string? RecommendationMediaUrl { get; set; }

    public virtual MstFacility? Facility { get; set; }

    public virtual ICollection<MstActionPartyUserMapping> MstActionPartyUserMappings { get; set; } = new List<MstActionPartyUserMapping>();
}
