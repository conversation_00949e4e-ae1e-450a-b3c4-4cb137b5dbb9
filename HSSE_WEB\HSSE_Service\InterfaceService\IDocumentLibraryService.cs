using HSSE_Models_Dto.ModelsDto;
using HSSE_Service.ServiceResponce;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace HSSE_Service.InterfaceService
{
    public interface IDocumentLibraryService
    {
        Task<ApiResponseDto<object>> CreateOrUpdateDocumentAsync(MstDocumentLibraryDto dto);
        Task<ApiResponseDto<List<MstDocumentLibraryDto>>> GetDocumentsAsync();
        Task<ApiResponseDto<List<MstDocumentLibraryDto>>> GetDocumentsByUserIdAsync(int userId);
        Task<ApiResponseDto<MstDocumentLibraryDto>> GetDocumentByIdAsync(int id);
        Task<ApiResponseDto<object>> DeleteDocumentAsync(int documentId, int deletedBy);
        Task<ApiResponseDto<List<MstDocumentLibraryDto>>> GetDocumentsLibraryAsync();
    }
} 