@model HSSE_Models_Dto.ModelsDto.MstDocumentLibraryDto
@{
    ViewData["Title"] = "Document Details";
}

<h2>Document Details</h2>
<div class="card">
    <div class="card-body">
        <dl class="row">
            <dt class="col-sm-3">Title</dt>
            <dd class="col-sm-9">@Model.Title</dd>

            <dt class="col-sm-3">Category</dt>
            <dd class="col-sm-9">@Model.Category</dd>

            <dt class="col-sm-3">Version</dt>
            <dd class="col-sm-9">@Model.Version</dd>

            <dt class="col-sm-3">Date</dt>
            <dd class="col-sm-9">@(Model.Date?.ToString("yyyy-MM-dd") ?? "")</dd>
        </dl>
        @if (!string.IsNullOrEmpty(Model.DocumentUrl))
        {
            <a href="@Model.DocumentUrl" class="btn btn-success" target="_blank" download>Download Document</a>
        }
        else
        {
            <span class="text-danger">No document available for download.</span>
        }
    </div>
</div> 