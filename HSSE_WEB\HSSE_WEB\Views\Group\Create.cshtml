@{
    ViewData["Title"] = "Create Group";
}

<div class="col-12 grid-margin" data-permission="create">
    <div class="card">
        <div class="card-body">
            <h4 class="card-title">Add New Group</h4>
            <form id="groupCreateForm" class="form-sample" method="post">
                <p class="card-description">Group Details</p>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group row">
                            <label class="col-sm-3 col-form-label">Group Name<span class="text-danger">*</span></label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control" id="GroupName" name="GroupName" required />
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group row">
                            <label class="col-sm-3 col-form-label">Description</label>
                            <div class="col-sm-9">
                                <textarea class="form-control" id="Description" name="Description"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12 text-end">
                        <button type="submit" class="btn btn-primary">Create Group</button>
                        <button type="reset" class="btn btn-light">Cancel</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<div class="card mt-4" data-permission="view">
    <div class="card-body">
        <h4 class="card-title">Existing Groups</h4>
        <div class="table-responsive">
            <table id="group-listing" class="table">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>Group Name</th>
                        <th>Description</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="groupTableBody">
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Edit Group Modal -->
<div class="modal fade" id="editGroupModal" tabindex="-1" aria-labelledby="editGroupModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-md" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editGroupModalLabel">Edit Group</h5>
                <button type="button" class="close" data-dismiss="modal"><span>&times;</span></button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="editGroupId" />
                <div class="form-group">
                    <label>Group Name<span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="editGroupName" required />
                </div>
                <div class="form-group">
                    <label>Description</label>
                    <input type="text" class="form-control" id="editGroupDescription" />
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="updateGroupBtn">Update Group</button>
            </div>
        </div>
    </div>
</div>
<script>
    var createGroup = '@Url.Action("Create", "Group")';
    var getAllGroups  = '@Url.Action("GetAllGroups", "Group")';
    var getGroupById = '@Url.Action("GetGroupById", "Group")?id=';
     var deleteGroup = '@Url.Action("DeleteGroup", "Group")?id=';
</script>

@section Scripts {
    <script src="~/js/Group/group-create.js"></script>
} 