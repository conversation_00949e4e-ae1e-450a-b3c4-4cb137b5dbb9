@model HSSE_Models_Dto.ViewModels.AddUserViewModel

@{
    ViewData["Title"] = "Create Inspection";
}
@{
    var distinctLocations = Model.InspectionDetails
    .Where(x => !string.IsNullOrWhiteSpace(x.InspectionLocation))
    .Select(x => x.InspectionLocation)
    .Distinct()
    .ToList();
    var distinctTypeOfInspection = Model.InspectionCategory
        .Where(x => !string.IsNullOrWhiteSpace(x.CategoryName))
        .GroupBy(x => x.CategoryName)
        .Select(g => g.First()) // Keep the first ID for each unique name
        .ToList();
    var isAppAdmin = Convert.ToBoolean(Context.Session.GetString("IsAppAdmin"));
    var isDeptAdmin = Convert.ToBoolean(Context.Session.GetString("IsDepartmentAdmin"));
}
<div class="col-12 grid-margin">
    <div class="card">
        <div class="card-body">
            <h4 class="card-title">Create Inspection</h4>

            <form id="inspectionForm" data-action="@Url.Action("InsertOrUpdateInspection", "Inspection")" enctype="multipart/form-data">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>Reference No</label>

                            <input type="text" class="form-control font-weight-bold" id="ReferenceNo" name="ReferenceNo" readonly />
                          
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>Date and Time</label>
                        
                            <input type="datetime" class="form-control" id="DateTime" name="DateTime" @(isAppAdmin ? "" : "readonly") />

                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>Facility <span class="text-danger">*</span></label>
                            <select class="form-control" id="FacilityId" name="FacilityId" required></select>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            <label>Location <span class="text-danger">*</span></label>
                            <div id="location-typeahead">
                                <input type="text" class="form-control typeahead" id="LocationId" name="LocationId" placeholder="Search Location" />
                                <input type="hidden" id="selectedLocationId" name="selectedLocationId" />
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>Type of Inspection <span class="text-danger">*</span></label>
                            <div id="user-typeahead">
                                <input type="text" class="form-control typeahead" id="user-search" placeholder="Search Category" />
                                <input type="hidden" id="selectedCategoryId" name="selectedCategoryId" />
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            <label>Person Conducting Inspection</label>
                            <input type="text" class="form-control" id="InspectorName" name="InspectorName" readonly />
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>Contact Person <span class="text-danger">*</span></label>
                            <select class="form-control" id="ContactPersonId" name="ContactPersonId">
                                <option value="">--Select Contact Person--</option>

                            </select>
                        </div>
                    </div>
                </div>
                <!-- Add Observation Button -->
                <div class="row mt-4">
                    <div class="col-md-12">
                        <button type="button" class="btn btn-outline-primary" id="addObservationBtn">
                            + Add Observation
                        </button>
                    </div>
                </div>

                <!-- Observation Table -->
                <div class="row mt-3">
                    <div class="col-md-12">
                        <div class="table-responsive">
                            <table class="table table-bordered" id="observationTable">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>Observation</th>
                                        <th>Recommendation</th>
                                        <th>Action Party</th>
                                        <th>Status</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Final Submit Button -->
                <div class="row mt-4">
                    <div class="col-md-12 text-end">
                        <button type="submit" class="btn btn-primary">Create Inspection</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Observation Modal -->
<div class="modal fade" id="observationModal" tabindex="-1" aria-labelledby="observationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <form id="observationForm">
                <div class="modal-header">
                    <h5 class="modal-title">New Observation</h5>
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                </div>

                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Location <span class="text-danger">*</span></label>
                                <input type="hidden" id="ObservationAttachmentBase64" />
                                <input type="hidden" id="RecommendationAttachmentBase64" />
                                <input type="text" class="form-control" id="ObservationLocationId" name="LocationId" placeholder="Location" />
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Action Party <span class="text-danger">*</span></label>
                                <select id="ActionPartyId" name="ActionPartyId" class="form-control js-example-basic-multiple w-100" multiple="multiple">
                                    <option value="">Select Action Party</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group d-flex align-items-center gap-2">
                                <span id="switchLabel" class="fw-semibold mr-2">Positive</span>
                                <label class="toggle-switch mb-0">
                                    <input type="checkbox" id="TypeStatus" checked>
                                    <span class="toggle-slider round"></span>
                                </label>
                            </div>
                        </div>

                    </div>
                    <!-- Observation Upload -->
                    <div class="form-group">
                        <label>Observation</label>
                        <textarea class="form-control" id="tinyMceExample" name="Observation"></textarea>

                        <div class="custom-file-upload mt-2">
                            <input type="file" class="d-none" id="ObservationAttachment" accept="image/*">
                            <div class="input-group">
                                <input type="text" class="form-control file-upload-info col-md-3" id="ObservationFileName" readonly placeholder="Upload Image">
                                <div class="input-group-append">
                                    <button class="btn btn-primary btn-sm" type="button" id="observationUploadBtn">Upload</button>
                                    <button class="btn btn-danger btn-sm ml-1 d-none" type="button" id="removeObservationFile">Remove</button>
                                </div>
                            </div>
                            <div class="mt-2 d-none" id="ObservationPreviewContainer">
                                <img id="ObservationPreview" src="" class="img-thumbnail" style="max-width: 200px;" />
                            </div>
                        </div>
                    </div>

                    <!-- Recommendation Upload -->
                    <div class="form-group mt-3" id="recommendationSection">
                        <label>Recommendation (If any)</label>
                        <textarea class="form-control" id="editTinyMceExample" name="Recommendation"></textarea>

                        <div class="custom-file-upload mt-2">
                            <input type="file" class="d-none" id="RecommendationAttachment" accept="image/*">
                            <div class="input-group">
                                <input type="text" class="form-control file-upload-info col-md-3" id="RecommendationFileName" readonly placeholder="Upload Image">
                                <div class="input-group-append">
                                    <button class="btn btn-primary btn-sm" type="button" id="recommendationUploadBtn">Upload</button>
                                    <button class="btn btn-danger btn-sm ml-1 d-none" type="button" id="removeRecommendationFile">Remove</button>
                                </div>
                            </div>
                            <div class="mt-2 d-none" id="RecommendationPreviewContainer">
                                <img id="RecommendationPreview" src="" class="img-thumbnail" style="max-width: 200px;" />
                            </div>
                        </div>
                    </div>
                @*     <div class="form-group mt-3">
                        <label>Status</label>
                        <select id="ObservationStatus" class="form-control">
                            <option value="1">Pending</option>
                            <option value="2">Completed</option>
                        </select>
                    </div> *@
                </div>

                <div class="modal-footer">
                    <button type="submit" class="btn btn-primary">Add to List</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    const loggedInUserName = '@(Model.NewUser?.Username ?? "Unknown User")';
    var getUsersByFacilityId = '@Url.Action("GetUsersByFacilityId", "User")?facilityId=';
     var getAllFacility = '@Url.Action("GetAllFacility", "Facility")';
 var existingUsers = [
    @foreach (var user in distinctTypeOfInspection)
    {
        @: { id: "@user.InspectionCategoryId", name: "@user.CategoryName" },
    }
    ];
     
          var existingLocation = [
    @foreach (var user in distinctLocations)
    {
        @: { id: "@user", name: "@user" },
    }
       ]
</script>
@section Scripts {
    <script src="~/js/Inspection/inspection-create.js"></script>
} 