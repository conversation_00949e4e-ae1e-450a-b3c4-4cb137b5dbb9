﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HSSE_Models_Dto.ViewModels
{
    public class CreateAnnouncementViewModel
    {
        public int? AnnouncementsId { get; set; }
        public int? SendTo { get; set; }  

        public List<int>? UserIds { get; set; } 

        public List<int>? GroupId { get; set; } 

        public string Title { get; set; } 

        public string Description { get; set; } 

        public int? Status { get; set; } 

        public int? CreatedBy { get; set; } 
        public DateTime? CreatedAt { get; set; }  

        public DateTime? ScheduleAt { get; set; } 

        public DateTime? ExpiryAt { get; set; }
        public string? Base64File { get; set; }
        public string? FileName { get; set; }
        public int? CategoryId { get; set; }
        public int? FacilityId { get; set; }
        public string? CategoryName { get; set; }
        public string? Attachment { get; set; }

    }
    public class AnnouncementViewModel
    {
        public int AnnouncementsId { get; set; }
        public string Title { get; set; }
        public string Description { get; set; }
        public DateTime? ScheduleAt { get; set; }
        public DateTime? ExpiryAt { get; set; }
        public string DocumentPath { get; set; }
        public string PublishedBy { get; set; }
        public string AnnouncementDocument { get; set; }
        public DateTime? CreatedAt { get; set; }
        public string? Attachment { get; set; }

    }

}
