@model HSSE_Models_Dto.ViewModels.AddUserViewModel

@{
    ViewData["Title"] = "Add Group Members";
}

<div class="col-12 grid-margin data-permission=" create"">
    <div class="card">
        <div class="card-body">
            <h4 class="card-title">Add Group Member</h4>
            <form id="groupMemberCreateForm" class="form-sample" method="post">
                <p class="card-description">Group Member Details</p>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group row">
                            <label class="col-sm-3 col-form-label">Group ID<span class="text-danger">*</span></label>
                            <div class="col-sm-9">
                                <select name="GroupId" id="GroupId" class="form-control">
                                    <option value="">-- Select Group --</option>
                                    @foreach (var role in Model.Group)
                                    {
                                        <option value="@role.GroupId">@role.GroupName</option>
                                    }
                                </select>
                            </div>
                        </div>
                    </div>
                        <div class="col-md-6">
                        <div class="form-group row">
                            <label class="col-sm-3 col-form-label">Facility <span class="text-danger">*</span></label>
                            <div class="col-sm-9">
                                <select class="form-control" id="FacilityId" name="FacilityId" required></select>
                                </div>
                            </div>
                        </div>
                
                    <div class="col-md-6">
                        <div class="form-group row">
                            <label class="col-sm-3 col-form-label">User ID<span class="text-danger">*</span></label>
                            <div class="col-sm-9">

                                <select name="FacilityIds" id="selectFacility" class="form-control js-example-basic-multiple w-100" multiple="multiple">
                             @*    @foreach (var fac in Model.ExistingUsers)
                                {
                                    <option value="@fac.UserId">@fac.Username</option>
                                } *@
                            </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12 text-end">
                        <button type="submit" class="btn btn-primary">Add Member</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<div class="card mt-4" data-permission="view">
    <div class="card-body">
        <h4 class="card-title">Existing Group Members</h4>
        <div class="table-responsive">
            <table id="group-member-listing" class="table">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>Group</th>
                        <th>No. of Users</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="groupMemberTableBody">
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Edit Group Member Modal -->
<div class="modal fade" id="editGroupMemberModal" tabindex="-1" aria-labelledby="editGroupMemberModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-md" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editGroupMemberModalLabel">Edit Group Member</h5>
                <button type="button" class="close" data-dismiss="modal"><span>&times;</span></button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="editGroupMemberId" />
                <div class="form-group row">
                    <label>Group<span class="text-danger">*</span></label>
                        <input type="hidden" id="oldGroupId" />

                    <select name="editGroupId" id="editGroupId" class="form-control">
                        <option value="">-- Select Group --</option>
                        @foreach (var role in Model.Group)
                        {
                            <option value="@role.GroupId">@role.GroupName</option>
                        }
                    </select>
                </div>
                <div class="form-group row">

                    <label>Users<span class="text-danger">*</span></label>
                    <select name="editUserIds" id="editUserIds" class="form-control js-example-basic-multiple w-100" multiple="multiple">
                        @foreach (var fac in Model.ExistingUsers)
                        {
                            <option value="@fac.UserId">@fac.Username</option>
                        }
                    </select>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="updateGroupMemberBtn">Update Member</button>
            </div>
        </div>
    </div>
</div>
<script>
     var insertOrUpdateGroupMember ='@Url.Action("InsertOrUpdateGroupMember", "Group")';
       var updateGroupMember= '@Url.Action("UpdateGroupMember", "Group")';
      var  getGroupMembers = '@Url.Action("GetGroupMembers", "Group")';
      var  getGroupMembersByGroupId = '@Url.Action("GetGroupMembersByGroupId", "Group")?groupId=';
      var  deleteAllGroupMembersByGroupId = '@Url.Action("DeleteAllGroupMembersByGroupId", "Group")?id=';
     var getAllFacility = '@Url.Action("GetAllFacility", "Facility")';
        var getUsersByFacilityId = '@Url.Action("GetUsersByFacilityId", "User")?facilityId=';

</script>

@section Scripts {
    <script src="~/js/Group/group-member-create.js"></script>
} 