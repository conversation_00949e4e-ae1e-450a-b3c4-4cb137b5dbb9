@model HSSE_Models_Dto.ModelsDto.MstEventDto

@{
    ViewData["Title"] = "Event Details";
    var isEventExpired = Model.EventDateTime.HasValue && Model.EventDateTime.Value < DateTime.Now;

}

<div class="container">
    @if (Model == null)
    {
        <div class="alert alert-danger text-center" role="alert">
            <i class="bi bi-exclamation-triangle-fill me-2"></i>
            Unable to load event details. Please try again later.
        </div>
        <div class="text-center mt-3">
            <a href="/Event/ViewEvent" class="btn btn-outline-primary">
                <i class="bi bi-arrow-left me-1"></i> Back to Events
            </a>
        </div>
    }
    else
    {
        <div class="card shadow-lg border-0">
            <div class="card-header  py-3 d-flex justify-content-between align-items-center flex-wrap">
                <h3 class="card-title mb-0">
                    <i class="mdi mdi-calendar"></i> @Model.Title
                </h3>
                @if (Model.IsRsvp)
                {
                    if (Model.IsAcceptedByUser == true)
                    {
                        <button class="btn btn-outline-warning rounded-pill px-4 py-2 fw-semibold shadow-sm"
                                onclick="withdrawEvent(@Model.EventId)">
                            <i class="fa fa-undo me-1"></i> Withdraw
                        </button>
                    }
                    else
                    {
                        var tooltipText = isEventExpired ? "Registration closed for this event" : "Click to register for this event";

                        <button type="button"
                                class="btn btn-outline-success rounded-pill px-4 py-2 fw-semibold shadow-sm btn-rsvp"
                                data-title="@Html.Encode(Model.Title)"
                                data-event-id="@Model.EventId"
                                title="@tooltipText"
                                data-toggle="tooltip"
                                data-custom-class="tooltip-warning"
                        @(isEventExpired ? "disabled" : "")>
                            <i class="mdi mdi-calendar-multiple-check"></i> Register
                        </button>
                    }
                }

                <!-- Download button on the right -->
                @if (!string.IsNullOrEmpty(@Model.Attachment))
                {
                    <a href="@Model.Attachment"
                       class="btn btn-outline-info btn-sm"
                       target="_blank"
                       download>
                        <i class="mdi mdi-folder-download"></i>Attachment
                    </a>
                }

            </div>
            <div class="card-body">
                @if (!string.IsNullOrEmpty(Model.MediaUrl))
                {
                    <div class="mb-4 text-left">
                        <img src="@Model.MediaUrl"
                             class="img-fluid rounded shadow-sm"
                             style="max-height: 200px; object-fit: cover;"
                             alt="Event Thumbnail" />
                    </div>
                }

                <div>

                    @*   <p class="text-muted mb-4">
                    <strong>Event by:</strong> @Model.cre
                </p> *@

                    <div class="mb-4">
                        <p>
                            <i class="mdi mdi-calendar me-2 text-primary"></i>
                            <strong>@Model.EventDateTime?.ToString("MMM dd, yyyy, hh:mm tt")</strong>
                            - @(Model?.ExpiryAt?.ToString("hh:mm tt") ?? "")
                        </p>
                        <p>
                            <i class="mdi mdi-pin me-2 text-primary"></i>
                            <span>@Model.Location</span>
                        </p>
                        @if (!string.IsNullOrWhiteSpace(Model.ExternalLink))
                        {
                            <p>
                                <i class="mdi mdi-link-variant me-2 text-primary"></i>
                                <a href="@Model.ExternalLink" target="_blank" class="text-decoration-none">@Model.ExternalLink</a>
                            </p>
                        }
                    </div>

                    @if (!string.IsNullOrWhiteSpace(Model.Description))
                    {
                        <div class="mb-4">
                            <h6 class="text-dark fw-bold mb-2">About the event</h6>
                            <div class="text-muted fs-6">
                                @Html.Raw(Model.Description)
                            </div>
                        </div>
                    }

                    <a  asp-controller="Event"
                       asp-action="ViewEvent"
                       class="btn btn-outline-secondary rounded-pill px-4 py-2 fw-semibold shadow-sm">
                        <i class="bi bi-arrow-left-circle me-1"></i> Back
                    </a>
                </div>
            </div>
        </div>
    }
</div>

<script>
     var getEventsByFacilityId = '@Url.Action("GetEventsByFacilityId", "Event")';
     var saveEventResponse = '@Url.Action("SaveEventResponse", "Event")';
    const basePath = '@Url.Content("~/")'; // returns '/UEMS/HSSEWeb/' in production
    var defaultIconUrl = '@Url.Content("~/Content/images/Image_not_available.jpg")';

</script>

@section Scripts {
    <script src="~/js/Events/viewEvents.js"></script>
}