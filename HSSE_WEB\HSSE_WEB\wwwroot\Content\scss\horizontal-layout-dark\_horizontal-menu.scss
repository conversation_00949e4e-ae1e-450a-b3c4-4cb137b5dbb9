.horizontal-menu {
  .top-navbar {
    font-weight: 400;
    background: $horizontal-top-bg;
    border-bottom: 1px solid $border-color;

    .navbar-brand-wrapper {
      width: auto;
      height: $horizontal-top-navbar-height;
      .navbar-brand {
        color: lighten(color(gray-dark), 20%);
        font-size: 1.5rem;
        line-height: 48px;
        margin-right: 0;
        padding: .25rem 0;
        text-align: left;
        
        &.brand-logo-mini {
          display: none;
          padding-left: 0;
          text-align: center;
          img {
            width: 50px;
            max-width: 100%;
            margin: auto;
          }
        }

        &:active,
        &:focus,
        &:hover {
          color: lighten(color(gray-dark), 10%);
        }

        img {
          max-width: 100%;
          height: 20px;
          margin: auto;
          vertical-align: middle;
        }

        @media (max-width: 991px) {
          width: 55px;
          &.brand-logo {
            display: none;
          }
          &.brand-logo-mini {
            display: block;
          }
        }
      }
    }

    .navbar-menu-wrapper {
      color: $horizontal-top-menu-item-color;
      width: auto;
      height: $horizontal-top-navbar-height;
      @include flex-grow(1);
      @media (max-width: 991px) {
        width: calc(100% - 55px);
        padding-left: 15px;
        padding-right: 15px;
      }

      .navbar-toggler {
        border: 0;
        color: inherit;
        font-size: 1.5rem;
        &:not(.navbar-toggler-right) {
          @media (max-width: 991px) {
            display: none;
          }
        }
        &.navbar-toggler-right{
          @media (max-width:991px){
            padding-left: 15px;
            padding-right: 11px;
          }
        }
      }
      .navbar-nav {
        @extend .d-flex;
        @extend .align-items-center;
        @extend .flex-row;
        .nav-item {
          margin-left: .25rem;
          margin-right: .25rem;
          width: 2rem;
          .nav-link {
            color: inherit;
            font-size: $horizontal-top-font-size;
          }
          &.nav-profile {
            @extend .d-flex;
            @extend .align-items-center;
            margin-left: .6rem;
            img {
              width: 32px;
              height: 32px;
              border-radius: 100%;
            }
            .nav-profile-name {
              margin-left: .5rem;
              @media (max-width: 767px) {
                display: none;
              }
            }
          }
          &.nav-search {
            margin-right: 1.5rem;
            margin-left: 1.5rem;
            flex: 1;
            .input-group {
              background: $content-bg;
              border: 2px solid $border-color;
              border-radius: 0;
              padding: 0 .75rem;
              .form-control,
              .input-group-text {
                background: transparent;
                border: 0;
                color: $horizontal-bottom-menu-item-color;
                padding: 0;
              }
              .form-control {
                margin-left: .5rem;
                @include placeholder {
                  color: $horizontal-bottom-menu-item-color;
                }
              }
            }
          }
          &.dropdown {
            .navbar-dropdown {
              position: absolute;            
              font-size: 0.9rem;
              margin-top: 0;
              right: -14px;
              left: auto;
              top: $horizontal-top-navbar-height;
              .rtl & {
                right: auto;
                left: 0;
              }

              .dropdown-item {
                @extend .d-flex;
                @extend .align-items-center;
                margin-bottom: 0;
                padding: .4rem 1.5rem;
                &:hover {
                  background: darken($content-bg, 3%);
                }

                i {
                  font-size: 17px;
                }

                .badge {
                  margin-left: 2.5rem;
                }
                .ellipsis {
                  max-width: 200px;
                  overflow: hidden;
                  text-overflow: ellipsis;
                }
                .rtl & {
                  i {
                    @extend .mr-0;
                    margin-left: 10px;
                  }
                  .badge {
                    margin-left: 0;
                    margin-right: 2.5rem;
                  }
                }
              }

              .dropdown-divider {
                margin: 0;
              }
            }
            i {
              margin-right: .5rem;
              vertical-align: middle;
            }
            @media (max-width: 991px) {
              position: static;
              .navbar-dropdown {
                left: 20px;
                right: 20px;
                top: $horizontal-top-navbar-height;
                width: calc(100% - 40px);
              }
            }
          }
        }
        &.navbar-nav-right {
          @extend .align-self-stretch;
          @extend .align-items-stretch;
          .nav-item {
            @extend .d-flex;
            @extend .align-items-center;
            @extend .justify-content-center;
            &:last-child {
              margin-right: 0;
            }
          }
          @media (min-width: 992px) {
            margin-left: auto;
            .rtl & {
              margin-left: 0;
              margin-right: auto;
            }
          }
        }
        &:not(.navbar-nav-right) {
          flex: 1;
        }
      }
      .dropdown-menu {
        border: 2px solid $border-color;
        &::before {
          content: '';
          width: 15px;
          height: 15px;
          background: $card-bg;
          position: absolute;
          right: 20px;
          top: -8px;
          border-left: 2px solid $border-color;
          border-top: 2px solid $border-color;
          transform: rotate(45deg);
        }
      }
      .count-indicator {
        position: relative;
        padding: 0;
        text-align: center;
        i {
          font-size: 1.25rem;
          margin-right: 0;
          vertical-align: middle;
        }
        .count {
          position: absolute;
          left: 36%;
          top: 1px;
          width: 6px;
          height: 6px;
          border-radius: 100%;
          background: theme-color(danger);
        }
        &:after {
          display: none;
        }
      }
      .dropdown {
        .dropdown-menu {
          @extend .dropdownAnimation;        
        }
      }
    }
  }
  .bottom-navbar {
      background: $horizontal-bottom-bg;			
      position: relative;
      top: 0;
      right: 0;
      left: 0;
      width: 100%;
      border-bottom: 1px solid $border-color;
      @include display-flex;
      @include align-items(center);
      @include transition-duration(3s);
      @include transition-property(position,left,right,top,z-index);
      box-shadow: $horizontal-menu-box-shadow;
      -webkit-box-shadow: $horizontal-menu-box-shadow;
      -moz-box-shadow: $horizontal-menu-box-shadow;
      @include media-breakpoint-down(md) {
          display: none;
          &.header-toggled {
              display: block;
              max-height: calc(100vh - #{$horizontal-top-navbar-height});
              overflow: auto;
          }
      }

      .page-navigation {
          position: relative;
          width: 100%;
          z-index: 99;
          @include justify-content(space-between);
          @include transition-duration(0.2s);
          @include transition-property(background, box-shadow);
          @include media-breakpoint-down(md) {
              border: none;
          }

          > .nav-item {
              line-height: 1;
              text-align: left;
              @include media-breakpoint-down(md) {
                  display: block;
                  width: 100%;
                  border-right: none;
              }

              &:first-child {
                  @include media-breakpoint-up(lg) {
                      >.nav-link {
                          padding-left: 0;
                      }
                  }
              }

              &:last-child {
                  border-right: none;
                  @include media-breakpoint-up(lg) {
                      >.nav-link {
                          padding-right: 0;
                      }
                  }
              }

              > .nav-link {
                  color: $horizontal-bottom-menu-item-color;
                  padding: 22px 10px;
                  line-height: 1;
                  font-weight: 400;
                  @extend .d-flex;
                  @extend .align-items-center;
                  .menu-title {
                      font-size: $horizontal-bottom-menu-item-font-size;
                      font-weight: 500;
                  }
                  .menu-icon {
                      margin-right: 10px;
                      font-size: $horizontal-menu-icon-font-size;
                      color: $horizontal-bottom-menu-item-icon-color;
                      font-weight: 400;

                      .rtl & {
                          margin-right: 0;
                          margin-left: 10px;
                      }
                  }

                  .menu-arrow {
                      margin-left: 5px;
                      display: inline-block;
                      @include transform(rotate(0deg));
                      @include transform-origin(center);
                      @include transition-duration($action-transition-duration);

                      &:before {
                        content: "\F140";
                        font-family: "Material Design Icons";
                        font-style: normal;
                        display: block;
                        font-size: 20px;
                        line-height: 10px;
                      }
                  }
              }

              @include media-breakpoint-up(md) {
                  &:hover {
                      @include show-submenu();
                  }
              }

              @include media-breakpoint-down(md) {
                  &.show-submenu {
                      @include show-submenu();
                  }
              }

              &.active {
                  > .nav-link {
                      position: relative;
                      &:before {
                          position: absolute;
                          content: "";
                          left: 0;
                          bottom: -2px;
                          width: 100%;
                          height: 5px;
                          @media (max-width: 991px) {
                            left: -15px;
                            top: 0;
                            bottom: 0;
                            height: 100%;
                            width: 5px;
                          }
                      }
                      .menu-title,
                      i,
                      .menu-arrow {
                          color: $horizontal-bottom-menu-item-active-color;							
                      }
                  }
              }

              .submenu {
                  display: none;
                  @extend .dropdownAnimation;

                  ul {
                      list-style-type: none;
                      padding-left: 0;
                  
                      li {
                          display: block;
                          line-height: 20px;

                          a {
                              display: block;
                              padding: 5px 10px;
                              font-weight: 300;
                              color: $horizontal-bottom-menu-item-color;
                              text-decoration: none;
                              text-align: left;
                              margin: 4px 0;
                              @include ellipsor;
                              @include transition-duration(0.2s);
                              @include transition-property(background);
                              @include border-radius(6px);

                              &:hover {
                                  color: theme-color(primary);
                                  @include transition (color .1s linear);
                                  &:before {
                                      background: theme-color(primary);
                                  }
                              }
                              &.active {
                                color: theme-color(primary);
                              }
                          }

                          &.active {
                              a {
                                      color: theme-color(primary);
                                      &:before {
                                          background: theme-color(primary);
                                      }
                              }
                          }
                      }
                  }
              }

              &:not(.mega-menu) {
                  position: relative;
                  .submenu {
                      @include menu-style;	
                      left: 0;													
                      @include media-breakpoint-down(md) {
                          top: 0;
                      }
                      ul {
                          width: auto;
                          padding: 25px;
                          @include media-breakpoint-down(md) {
                              padding: 0 23px;
                          }
                      }
                  }
              }

              &.mega-menu {
                  .submenu {
                      @include menu-style;
                      width: 100%;						
                      left: 0;
                      right: 0;
                      padding: 25px;

                      .category-heading {
                          font-size: .875rem;
                          font-weight: 500;
                          text-align: left;
                          color: $black;
                          padding: 1rem 0;
                          margin-bottom: 0;
                          @extend .text-primary;								
                      }
                      .col-group-wrapper {
                          padding: 0 1rem;								
                      }
                      @include media-breakpoint-down(md) {
                          padding: 0 32px;

                          .col-group-wrapper {
                              margin-left: 0;
                              margin-right: 0;
                              padding: 0;

                              .col-group {
                                  padding-left: 0;
                                  padding-right: 0;
                                  margin-bottom: 20px;

                                  .category-heading {

                                      &:after {
                                          display: none;
                                      }
                                  }
                              }
                          }
                      }
                  }
              }
          }
      }
  }
  &.fixed-on-scroll {
    .bottom-navbar {
        @extend .fixed-top;
        border-bottom: 1px solid $border-color;
    }
  }
  @media (max-width: 991px) {
    position: fixed;
    z-index: 1030;
    top: 0;
    left: 0;
    right: 0;
  }
}
