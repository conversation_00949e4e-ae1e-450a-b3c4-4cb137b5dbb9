using AutoMapper;
using HSSE_Models_Dto.ModelsDto;
using HSSE_Service.Constant;
using HSSE_Service.InterfaceService;
using HSSE_Service.ServiceResponce;
using Microsoft.Extensions.Configuration;
using System;
using System.Net.Http;
using System.Net.Http.Json;
using System.Threading.Tasks;

namespace HSSE_Service.DataManager
{
    public class EventService : IEventService
    {
        private readonly HttpClient _httpClient;
        private readonly string _apiBaseUrl;

        public EventService(IHttpClientFactory httpClientFactory, IConfiguration configuration)
        {
            _httpClient = httpClientFactory.CreateClient("AuthenticatedClient");
            var baseUrl = configuration["ExternalApi:BaseUrl"];
            if (!string.IsNullOrEmpty(baseUrl))
            {
                _httpClient.BaseAddress = new Uri(baseUrl);
            }
            _apiBaseUrl = configuration["ExternalApi:BaseUrl"];
        }

        public async Task<ApiResponseDto<object>> InsertOrUpdateEventAsync(MstEventDto dto)
        {
            try
            {
                var apiUrl = $"{_apiBaseUrl}{ApiEndpoints.InsertOrUpdateEvent}";
                var response = await _httpClient.PostAsJsonAsync(apiUrl, dto);
                if (response.IsSuccessStatusCode)
                {
                    var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<object>>();
                    return ApiResponseDto<object>.SuccessResponse(apiResult?.Message ?? "Event saved successfully.", (int)response.StatusCode, apiResult?.Data);
                }
                else
                {
                    return ApiResponseDto<object>.ErrorResponse("Failed to save event.", (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse("Failed to save event.", 500, new System.Collections.Generic.List<HSSE_Service.ServiceResponce.Error> {
                    new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
                });
            }
        }
        public async Task<ApiResponseDto<List<MstEventDto>>> GetEventsAsync(int? userId, List<int>? facilityIds)
        {
            try
            {
                var queryParams = new List<string>();

                if (userId.HasValue)
                    queryParams.Add($"userId={userId.Value}");

                if (facilityIds != null && facilityIds.Any())
                {
                    foreach (var id in facilityIds)
                    {
                        queryParams.Add($"facilityIds={id}");
                    }
                }

                string queryString = queryParams.Any() ? "?" + string.Join("&", queryParams) : string.Empty;
                string apiUrl = $"{_apiBaseUrl}{ApiEndpoints.GetEvents}{queryString}";

                var response = await _httpClient.GetAsync(apiUrl);

                if (response.IsSuccessStatusCode)
                {
                    var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<List<MstEventDto>>>();
                    return ApiResponseDto<List<MstEventDto>>.SuccessResponse(
                        apiResult?.Message ?? "Events fetched successfully.",
                        (int)response.StatusCode,
                        apiResult?.Data ?? new List<MstEventDto>());
                }
                else
                {
                    return ApiResponseDto<List<MstEventDto>>.ErrorResponse("Failed to fetch events.", (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<List<MstEventDto>>.ErrorResponse("Failed to fetch events.", 500, new List<HSSE_Service.ServiceResponce.Error> {
            new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
        });
            }
        }


        public async Task<ApiResponseDto<List<MstEventDto>>> GetEventsByFacilityIdAsync(
            List<int>? facilityIds,
            bool isAdmin,
            int? userId,
            int? dateFilterId = null,
            string? search = null)
        {
            try
            {
                var queryParams = new List<string>
        {
            $"userId={userId}"
        };

                // Append each facilityId as separate query param (e.g., ?facilityIds=1&facilityIds=2...)
                if (facilityIds != null && facilityIds.Any())
                {
                    foreach (var id in facilityIds)
                        queryParams.Add($"facilityIds={id}");
                }

                if (!string.IsNullOrWhiteSpace(search))
                    queryParams.Add($"search={Uri.EscapeDataString(search)}");

                if (dateFilterId.HasValue && dateFilterId > 0)
                    queryParams.Add($"dateFilterId={dateFilterId.Value}");

                 queryParams.Add($"isAdmin={isAdmin}");
                var apiUrl = $"{_apiBaseUrl}/api/Event/GetEventsByFacilityId?" + string.Join("&", queryParams);

                var response = await _httpClient.GetAsync(apiUrl);

                if (response.IsSuccessStatusCode)
                {
                    var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<List<MstEventDto>>>();
                    return ApiResponseDto<List<MstEventDto>>.SuccessResponse(
                        apiResult?.Message ?? "Events fetched successfully.",
                        (int)response.StatusCode,
                        apiResult?.Data ?? new List<MstEventDto>());
                }
                else
                {
                    return ApiResponseDto<List<MstEventDto>>.ErrorResponse("Failed to fetch events.", (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<List<MstEventDto>>.ErrorResponse("Failed to fetch events.", 500, new List<HSSE_Service.ServiceResponce.Error>
        {
            new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
        });
            }
        }



        public async Task<ApiResponseDto<object>> SaveEventLikeAsync(int eventId, int userId)
        {
            try
            {
                var apiUrl = $"{_apiBaseUrl}/api/Event/SaveEventLike?eventId={eventId}&userId={userId}";
                var response = await _httpClient.PostAsync(apiUrl, null);
                if (response.IsSuccessStatusCode)
                {
                    var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<object>>();
                    return ApiResponseDto<object>.SuccessResponse(apiResult?.Message ?? "Event liked successfully.", (int)response.StatusCode, apiResult?.Data);
                }
                else
                {
                    return ApiResponseDto<object>.ErrorResponse("Failed to like event.", (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse("Failed to like event.", 500, new System.Collections.Generic.List<HSSE_Service.ServiceResponce.Error> {
                    new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
                });
            }
        }

        public async Task<ApiResponseDto<object>> SaveEventResponseAsync(EventResponseDto dto)
        {
            try
            {
                var apiUrl = $"{_apiBaseUrl}/api/Event/SaveEventResponse";
                var response = await _httpClient.PostAsJsonAsync(apiUrl, dto);
                if (response.IsSuccessStatusCode)
                {
                    var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<object>>();
                    return ApiResponseDto<object>.SuccessResponse(apiResult?.Message ?? "Event response saved successfully.", (int)response.StatusCode, apiResult?.Data);
                }
                else
                {
                    return ApiResponseDto<object>.ErrorResponse("Failed to save event response.", (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse("Failed to save event response.", 500, new System.Collections.Generic.List<HSSE_Service.ServiceResponce.Error> {
                    new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
                });
            }
        }

        public async Task<ApiResponseDto<object>> ToggleEventActivationAsync(int eventId)
        {
            try
            {
                var apiUrl = $"{_apiBaseUrl}/api/Event/ToggleEventActivation?eventId={eventId}";
                var response = await _httpClient.PostAsync(apiUrl, null);
                if (response.IsSuccessStatusCode)
                {
                    var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<object>>();
                    return ApiResponseDto<object>.SuccessResponse(apiResult?.Message ?? "Event activation toggled successfully.", (int)response.StatusCode, apiResult?.Data);
                }
                else
                {
                    return ApiResponseDto<object>.ErrorResponse("Failed to toggle event activation.", (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse("Failed to toggle event activation.", 500, new System.Collections.Generic.List<HSSE_Service.ServiceResponce.Error> {
                    new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
                });
            }
        }

        public async Task<ApiResponseDto<List<object>>> GetEventRsvpDetailsAsync(int eventId)
        {
            try
            {
                var apiUrl = $"{_apiBaseUrl}{ApiEndpoints.GetEventRsvpDetails}{eventId}";
                var response = await _httpClient.GetAsync(apiUrl);
                if (response.IsSuccessStatusCode)
                {
                    var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<List<object>>>();
                    return ApiResponseDto<List<object>>.SuccessResponse(apiResult?.Message ?? "Events fetched successfully.", (int)response.StatusCode, apiResult?.Data ?? new List<object>());
                }
                else
                {
                    return ApiResponseDto<List<object>>.ErrorResponse("Failed to fetch events.", (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<List<object>>.ErrorResponse("Failed to fetch events.", 500, new System.Collections.Generic.List<HSSE_Service.ServiceResponce.Error> {
                    new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
                });
            }
        }

        public async Task<ApiResponseDto<MstEventDto>> GetEventByIdAsync(int eventId, int userId)
        {
            try
            {
                var apiUrl = $"{_apiBaseUrl}{ApiEndpoints.GetEventById}{eventId}&userId={userId}";
                var response = await _httpClient.GetAsync(apiUrl);
                if (response.IsSuccessStatusCode)
                {
                    var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<MstEventDto>>();
                    return ApiResponseDto<MstEventDto>.SuccessResponse(apiResult?.Message ?? "Event fetched successfully.", (int)response.StatusCode, apiResult?.Data);
                }
                else
                {
                    return ApiResponseDto<MstEventDto>.ErrorResponse("Failed to fetch event.", (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<MstEventDto>.ErrorResponse("Failed to fetch event.", 500, new List<HSSE_Service.ServiceResponce.Error> {
                    new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
                });
            }
        }
    }
} 