﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HSSE_Service.Constant
{
    public static class ApiMessages
    {
        // Success Messages
        public const string UserRegisteredSuccessfully = "User registered successfully";
        public const string UserUpdatedSuccessfully = "User updated successfully";
        public const string LoginSuccess = "Login successful.";
        public const string UserUpdateSuccess = "User details and roles updated successfully.";
        public const string RoleSaveSuccess = "Role saved successfully.";
        public const string RoleDeactivationSuccess = "Role deactivated successfully.";
        public const string RoleActivationSuccess = "Role activated successfully.";
        public const string UserRoleMappingSaveSuccess = "User role mapping saved successfully.";
        public const string UserRoleMappingDuplicate = "This role is already assigned to the user for the selected facility.";
        public const string UserRoleMappingDeleted = "User role mapping deleted successfully.";
        public const string GroupSaveSuccess = "Group saved successfully.";

        // Error Messages
        public const string UserAlreadyExists = "User already exists with the given username or email.";
        public const string UserNotFound = "User not found.";
        public const string InvalidCredentials = "Invalid username or password.";
        public const string ValidationError = "Validation error occurred.";
        public const string GeneralError = "An unexpected error occurred during login.";
        public const string InternalServerError = "An unexpected error occurred. Please try again later.";
        public const string UserUpdateFailure = "Failed to update user details and roles.";
        public const string RoleSaveFailed = "Failed to save role.";
        public const string RoleDeactivationFailed = "Failed to deactivate role.";
        public const string RoleActivationFailed = "Failed to activate role.";
        public const string RoleNotFound = "Role not found.";
        public const string UserRoleMappingInvalidInput = "UserId and RoleId are required.";
        public const string UserRoleMappingSaveFailed = "Failed to save user role mapping.";
        public const string UserRoleMappingNotFound = "User role mapping not found.";
        public const string GroupSaveFailed = "Failed to save group.";

    }
    public class PaginatedResponseDto<T>
    {
        public List<T> Items { get; set; }
        public int PageNumber { get; set; }
        public int PageSize { get; set; }
        public int TotalRecords { get; set; }
    }
}
