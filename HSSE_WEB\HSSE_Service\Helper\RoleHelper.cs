﻿using HSSE_Service.Constant;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HSSE_Service.Helper
{
    public static class RoleHelper
    {
        private static readonly List<UserRole> RolePriorityOrder = new List<UserRole>
    {
        UserRole.AppAdmin,
        UserRole.DeptAdmin,
        UserRole.FacilityAdmin,
        UserRole.Manager,
        UserRole.User
    };

        public static UserRole? GetHighestPriorityRole(List<int> roleIds)
        {
            // Convert role IDs to enum (ignore unknown ones)
            var userRoles = roleIds
                .Where(id => Enum.IsDefined(typeof(UserRole), id))
                .Select(id => (UserRole)id)
                .ToList();

            // Return the highest priority role from userRoles
            return RolePriorityOrder.FirstOrDefault(userRoles.Contains);
        }
    }
}
