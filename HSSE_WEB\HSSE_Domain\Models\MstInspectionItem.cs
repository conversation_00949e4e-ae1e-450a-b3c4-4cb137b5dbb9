﻿using System;
using System.Collections.Generic;

namespace HSSE_Domain.Models;

public partial class MstInspectionItem
{
    public int ItemId { get; set; }

    public int InspectionId { get; set; }

    public string? Description { get; set; }

    public string? SpecificLocation { get; set; }

    public string? Recommendation { get; set; }

    public int? Status { get; set; }

    public string? Rectification { get; set; }

    public string? AfterImagePath { get; set; }

    public DateTime? CompletionDateTime { get; set; }

    public DateTime? CreatedAt { get; set; }

    public DateTime? ModifiedAt { get; set; }

    public string? RecommendationMediaUrl { get; set; }

    public string? Observation { get; set; }

    public string? ObservationMediaUrl { get; set; }

    public int? Verification { get; set; }

    public int? ContactPersonId { get; set; }

    public string? Location { get; set; }

    public int? ObservationType { get; set; }

    public string? ActionPartyId { get; set; }

    public int? CreatedBy { get; set; }

    public int? ModifiedBy { get; set; }

    public virtual MstInspection Inspection { get; set; } = null!;

    public virtual ICollection<MstInspectionItemAudit> MstInspectionItemAudits { get; set; } = new List<MstInspectionItemAudit>();

    public virtual ICollection<MstInspectionItemComment> MstInspectionItemComments { get; set; } = new List<MstInspectionItemComment>();
}
