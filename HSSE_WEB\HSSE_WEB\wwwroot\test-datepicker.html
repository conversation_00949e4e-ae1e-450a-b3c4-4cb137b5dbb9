<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Date Picker Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-md-8 mx-auto">
                <div class="card">
                    <div class="card-header">
                        <h3>Enhanced Date Picker Test</h3>
                        <p class="mb-0 text-muted">Test clicking anywhere in the date input controls to open the date picker</p>
                    </div>
                    <div class="card-body">
                        <form>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="testDate1" class="form-label">Date Input (type="date")</label>
                                    <input type="date" class="form-control" id="testDate1" name="testDate1" />
                                    <small class="text-muted">Click anywhere in this control to open date picker</small>
                                </div>
                                <div class="col-md-6">
                                    <label for="testDateTime1" class="form-label">DateTime Input (type="datetime-local")</label>
                                    <input type="datetime-local" class="form-control" id="testDateTime1" name="testDateTime1" />
                                    <small class="text-muted">Click anywhere in this control to open date/time picker</small>
                                </div>
                            </div>
                            
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="testDate2" class="form-label">Another Date Input</label>
                                    <input type="date" class="form-control" id="testDate2" name="testDate2" />
                                </div>
                                <div class="col-md-6">
                                    <label for="testDateTime2" class="form-label">Another DateTime Input</label>
                                    <input type="datetime-local" class="form-control" id="testDateTime2" name="testDateTime2" />
                                </div>
                            </div>
                            
                            <div class="row mb-3">
                                <div class="col-md-12">
                                    <label for="testDate3" class="form-label">Full Width Date Input</label>
                                    <input type="date" class="form-control" id="testDate3" name="testDate3" />
                                </div>
                            </div>
                            
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle"></i> Test Instructions:</h6>
                                <ul class="mb-0">
                                    <li>Click anywhere within each date/datetime input control</li>
                                    <li>The date picker should open immediately</li>
                                    <li>You should see a calendar icon on the right side of each input</li>
                                    <li>The entire input area should be clickable</li>
                                    <li>Keyboard navigation should work (Space/Enter to open picker)</li>
                                </ul>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-12">
                                    <button type="button" class="btn btn-primary" onclick="testValues()">Test Values</button>
                                    <button type="button" class="btn btn-secondary" onclick="clearValues()">Clear All</button>
                                </div>
                            </div>
                        </form>
                        
                        <div id="testResults" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Include our enhanced date picker script -->
    <script src="js/enhanced-datepicker.js"></script>
    
    <script>
        function testValues() {
            const values = {
                testDate1: document.getElementById('testDate1').value,
                testDateTime1: document.getElementById('testDateTime1').value,
                testDate2: document.getElementById('testDate2').value,
                testDateTime2: document.getElementById('testDateTime2').value,
                testDate3: document.getElementById('testDate3').value
            };
            
            let html = '<div class="alert alert-success"><h6>Current Values:</h6><ul>';
            for (const [key, value] of Object.entries(values)) {
                html += `<li><strong>${key}:</strong> ${value || 'Not set'}</li>`;
            }
            html += '</ul></div>';
            
            document.getElementById('testResults').innerHTML = html;
        }
        
        function clearValues() {
            document.getElementById('testDate1').value = '';
            document.getElementById('testDateTime1').value = '';
            document.getElementById('testDate2').value = '';
            document.getElementById('testDateTime2').value = '';
            document.getElementById('testDate3').value = '';
            document.getElementById('testResults').innerHTML = '';
        }
        
        // Test keyboard events
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Enhanced Date Picker Test Page Loaded');
            console.log('Enhanced date picker should be initialized automatically');
            
            // Add some test logging
            setTimeout(function() {
                const enhancedInputs = document.querySelectorAll('.enhanced-datepicker');
                console.log(`Found ${enhancedInputs.length} enhanced date picker inputs`);
                
                const wrappers = document.querySelectorAll('.enhanced-datepicker-wrapper');
                console.log(`Found ${wrappers.length} enhanced date picker wrappers`);
            }, 1000);
        });
    </script>
</body>
</html>
