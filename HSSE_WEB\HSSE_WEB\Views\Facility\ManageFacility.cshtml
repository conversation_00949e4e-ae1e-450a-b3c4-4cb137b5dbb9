﻿@model HSSE_Models_Dto.ViewModels.AddUserViewModel

@{
    ViewData["Title"] = "Manage Facility";
}

    <div class="content-wrapper">

@*         <!-- Form to Add/Edit Facility -->
        <div class="card" data-permission="create">
            <div class="card-body">
                <h4 class="card-title">Create Facility</h4>
                <form id="facilityForm" class="form-sample" method="post">
                    <input type="hidden" name="FacilityId" id="FacilityId" value="0" />

                    <div class="form-group row">
                        <label class="col-sm-2 col-form-label">Facility Name<span class="text-danger">*</span></label>
                        <div class="col-sm-4">
                            <input type="text" class="form-control" name="FacilityName" id="FacilityName" placeholder="Enter facility name"/>
                        </div>

                        <label class="col-sm-2 col-form-label">Facility Code<span class="text-danger">*</span></label>
                        <div class="col-sm-2">
                            <input type="text" class="form-control" name="FacilityCode" id="FacilityCode" placeholder="Code" required />
                        </div>
                    </div>

                    <div class="form-group row">
                        <label class="col-sm-2 col-form-label">Organization<span class="text-danger">*</span></label>
                        <div class="col-sm-4">
                            <select name="Org" id="OrgId" class="form-control">
                                <option value="">-- Select Org --</option>
                                @foreach (var org in Model.Organisation)
                                {
                                    <option value="@org.OrgId">@org.OrganisationName</option>
                                }
                            </select>
                        </div>
                  
                    </div>
                    <div class="text-left">
                        <button type="submit" class="btn btn-outline-primary btn-icon-text" data-permission="create">
                            <i class="mdi mdi-file-check btn-icon-prepend"></i>Submit
                        </button>
                    </div>
                </form>

            </div>
        </div> *@

        <!-- Table to Show Existing Facilities -->
        <div class="card mt-4" data-permission="view">
            <div class="card-body">
                <h4 class="card-title">Existing Facilities</h4>
                <div class="table-responsive">
                    <table id="order-listing" class="table">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>Facility Name</th>
                                <th>Facility Code</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody  id="facilityTableBody">
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

    </div>
<!-- Edit Facility Modal -->
<div class="modal fade" id="editFacilityModal" tabindex="-1" role="dialog" aria-labelledby="editFacilityModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editFacilityModalLabel">Edit Facility</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="editFacilityForm">
                    <input type="hidden" id="editFacilityId" name="FacilityId" />
                    <div class="form-group row">
                        <label class="col-sm-3 col-form-label">Facility Name<span class="text-danger">*</span></label>
                        <div class="col-sm-9">
                            <input type="text" class="form-control" name="FacilityName" id="editFacilityName" required />
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-3 col-form-label">Facility Code<span class="text-danger">*</span></label>
                        <div class="col-sm-9">
                            <input type="text" class="form-control" name="FacilityCode" id="editFacilityCode" required />
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-3 col-form-label">Organization<span class="text-danger">*</span></label>
                        <div class="col-sm-9">
                            <select name="OrgId" id="editOrgId" class="form-control">
                                <option value="">-- Select Org --</option>
                                @foreach (var org in Model.Organisation)
                                {
                                    <option value="@org.OrgId">@org.OrganisationName</option>
                                }
                            </select>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" onclick="updateFacility()">Save Changes</button>
            </div>
        </div>
    </div>
</div>
<script>
    var getFacility = '@Url.Action("GetAllMasterFacility", "Facility")';
    var getFacilityById = '@Url.Action("GetFacilityById", "Facility")';
     var editFacilityUrl = '@Url.Action("EditFacility", "Facility")';
    var updateFacilityUrl = '@Url.Action("UpdateFacility", "Facility")';
    var deleteFacilityUrl = '@Url.Action("DeleteFacility", "Facility")';
    var saveFacilityUrl = '@Url.Action("SaveFacility", "Facility")';
    var toggleFacilityStatusUrl = '@Url.Action("ToggleFacilityStatus", "Facility")';
</script>
@section Scripts {
    <script src="~/js/facility/manageFacility.js"></script>
}