﻿using AutoMapper;
using HSSE_Domain.Models;
using HSSE_Models_Dto.ModelsDto;

namespace HSSE_WEB.AutoMapper
{
    public class AutoMapperProfile : Profile
    {
        public AutoMapperProfile()
        {
            CreateMap<MstUser, MstUserDto>();
            CreateMap<MstUserDto, MstUser>();

            CreateMap<MstFacility, MstFacilityDto>();
            CreateMap<MstFacilityDto, MstFacility>();
            CreateMap<MstUsersRoleDto, MstUsersRole>();
            CreateMap<MstUsersRole, MstUsersRoleDto>();
            CreateMap<MstUserRolesConfigDto, MstUserRolesConfig>();
            CreateMap<MstOrganisationDto, MstOrganisation>();
            CreateMap<MstOrganisation , MstOrganisationDto>();
            CreateMap<MstLanguage, MstLanguageDto>().ReverseMap();

        }
    }
}
