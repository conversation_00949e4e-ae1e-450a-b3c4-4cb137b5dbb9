﻿using HSSE_Domain.Models;
using HSSE_Models_Dto.ModelsDto;
using HSSE_Service.Constant;
using HSSE_Service.InterfaceService;
using HSSE_Service.ServiceResponce;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using BCrypt.Net; // Namespace for BCrypt
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using Microsoft.Extensions.Configuration;
using AutoMapper;
using Newtonsoft.Json;
using System.Runtime.InteropServices;
using HSSE_Service.Helper;
using Azure;
using Google.Api.Gax.ResourceNames;



namespace HSSE_Service.DataManager
{
    public class UserService : IUserService
    {
        private readonly HsseDbLatestContext _context;
        private IConfiguration _configuration;
        private readonly IMapper _mapper;
        private readonly FirebaseStorageHelper _firebaseStorageHelper;

        public UserService(IMapper mapper , HsseDbLatestContext context, IConfiguration configuration, FirebaseStorageHelper firebaseStorageHelper)
        {
            _context = context;
            _configuration = configuration;
            _mapper = mapper;
            _firebaseStorageHelper=firebaseStorageHelper;
        }

        public async Task<ApiResponseDto<LoginResponseDto>> SaveOrUpdateUserAsync(UserInfo userInfo)
        {
            try
            {
                // Check if the user already exists using SsoUserId
                var existingUser = await _context.MstUsers
                    .Include(x => x.MstUserRolesConfigs)
                    .ThenInclude(x => x.Facility)
                    .FirstOrDefaultAsync(u => u.SsoUserId == userInfo.UserId || u.Username == userInfo.UserEmail);

                MstUser user;
                if (existingUser != null)
                {
                    user = existingUser;
                }
                else
                {
                    // Create and add new user
                    user = new MstUser
                    {
                        Username = userInfo.UserEmail,
                        Email = userInfo.UserEmail,
                        SsoUserId = userInfo.UserId,
                        FirstName = userInfo.UserName,
                        LastName = "Unknown",
                        CreatedAt = DateTime.UtcNow,
                        LastLogin = DateTime.UtcNow,
                        IsActive = true,
                        IsSsoUser = true
                    };

                    _context.MstUsers.Add(user);
                    await _context.SaveChangesAsync();

                    // Convert comma-separated FacilityList string to list
                    var facilityCodes = string.IsNullOrWhiteSpace(userInfo.FacilityList)
                        ? new List<string>()
                        : userInfo.FacilityList.Split(',', StringSplitOptions.RemoveEmptyEntries)
                                               .Select(f => f.Trim())
                                               .ToList();

                    // Fetch matching FacilityIds
                    var matchedFacilityIds = await _context.MstFacilities
                        .Where(f => facilityCodes.Contains(f.FacilityCode))
                        .Select(f => f.FacilityId)
                        .ToListAsync();

                    // Default to at least one role mapping (no facility)
                    if (!matchedFacilityIds.Any())
                    {
                        matchedFacilityIds.Add(0); // Or handle gracefully
                    }

                    // Assign role(s) for each facility
                    foreach (var facilityId in matchedFacilityIds)
                    {
                        var userRole = new MstUserRolesConfig
                        {
                            UserId = user.UserId,
                            RoleId = 2,
                            FacilityId = facilityId,
                            CreatedAt = DateTime.UtcNow
                        };

                        _context.MstUserRolesConfigs.Add(userRole);
                    }

                    await _context.SaveChangesAsync();


                    // Reload user with roles and facilities
                    user = await _context.MstUsers
                        .Include(x => x.MstUserRolesConfigs)
                        .ThenInclude(x => x.Facility)
                        .FirstOrDefaultAsync(u => u.UserId == user.UserId);
                }

                // Generate JWT
                var token = GenerateJwtToken(user);

                // Collect role and facility info
                var roleIds = user.MstUserRolesConfigs.Select(r => r.RoleId ?? 0).ToList();
                var facilityIds = user.MstUserRolesConfigs.Select(r => r.FacilityId ?? 0).ToList();

                bool isAppAdmin = roleIds.Contains(1);
                bool isDepartmentAdmin = roleIds.Contains(3);
                bool isFacilityAdmin = roleIds.Contains(6);
                bool isManager = roleIds.Contains(7);
                List<int> facilityAdminFacilityIds = new();
                if (isFacilityAdmin)
                {
                    facilityAdminFacilityIds = user.MstUserRolesConfigs
                        .Where(r => r.RoleId == 6 && r.Facility != null)
                        .Select(r => r.Facility.FacilityId)
                        .Distinct()
                        .ToList();
                }

                List<int> managerFacilityIds = new();
                if (isManager)
                {
                    managerFacilityIds = user.MstUserRolesConfigs
                        .Where(r => r.RoleId == 7 && r.Facility != null)
                        .Select(r => r.Facility.FacilityId)
                        .Distinct()
                        .ToList();
                }

                // Prepare response DTO
                var responseDto = new LoginResponseDto
                {
                    Token = token,
                    Username = user.Username,
                    Email = user.Email,
                    UserId = user.UserId,
                    FacilityIds = facilityIds,
                    RoleIds = roleIds,
                    IsAppAdmin = isAppAdmin,
                    IsDepartmentAdmin = isDepartmentAdmin,
                    IsFacilityAdmin = isFacilityAdmin,
                    IsManager = isManager,
                    FacilityAdminFacilityIds = facilityAdminFacilityIds, // <-- Only IDs
                    ManagerFacilityIds = managerFacilityIds
                };

                return ApiResponseDto<LoginResponseDto>.SuccessResponse(ApiMessages.LoginSuccess, StatusCodes.Status200OK, responseDto);
            }
            catch (Exception ex)
            {
                // Optionally log the error here
                // _logger.LogError(ex, "Error in SaveOrUpdateUserAsync");
                return ApiResponseDto<LoginResponseDto>.ErrorResponse("An error occurred while saving or updating the user.", StatusCodes.Status500InternalServerError);
            }
        }

        public async Task<ApiResponseDto<bool>> RegisterUserAsync(MstUserDto model)
        {
            try
            {
                // Check if the user already exists
                var existingUser = await _context.MstUsers
                    .FirstOrDefaultAsync(u => u.Username == model.Username || u.Email == model.Email);

                if (existingUser != null)
                {
                    return ApiResponseDto<bool>.ErrorResponse(ApiMessages.UserAlreadyExists, StatusCodes.Status400BadRequest);
                }
                string hashedPassword = BCrypt.Net.BCrypt.HashPassword(model.Password);

                var user = new MstUser
                {
                    Username = model.Username,
                    FirstName = model.FirstName,
                    LastName = model.LastName,
                    Email = model.Email,
                    Password = hashedPassword,
                    CreatedAt = DateTime.UtcNow
                };

                _context.MstUsers.Add(user);
                await _context.SaveChangesAsync();
                var userRole = new MstUserRolesConfig
                {
                    UserId = user.UserId,
                    RoleId = 2,  
                    FacilityId = null,
                    CreatedAt = DateTime.UtcNow
                };

                _context.MstUserRolesConfigs.Add(userRole);
                await _context.SaveChangesAsync();
                return ApiResponseDto<bool>.SuccessResponse(ApiMessages.UserRegisteredSuccessfully, StatusCodes.Status200OK, true);
            }
            catch (Exception ex)
            {
                // Log the exception here
                return ApiResponseDto<bool>.ErrorResponse(ApiMessages.InternalServerError, StatusCodes.Status500InternalServerError);
            }
        }

        public async Task<ApiResponseDto<object>> LoginUserAsync(string username, string password)
        {
            try
            {
                var user = await _context.MstUsers
                       .Include(r => r.MstUserRolesConfigs).ThenInclude(f => f.Facility)
                       .FirstOrDefaultAsync(u =>
                           (u.Username == username || u.Email == username) && u.IsActive == true);

                if (user == null)
                {
                    return ApiResponseDto<object>.ErrorResponse(ApiMessages.InvalidCredentials, StatusCodes.Status401Unauthorized);
                }

                string decryptedPassword = PasswordHelper.DecryptPassword(user.Password);

                if (decryptedPassword != password)
                {
                    return ApiResponseDto<object>.ErrorResponse(ApiMessages.InvalidCredentials, StatusCodes.Status401Unauthorized);
                }

                var token = GenerateJwtToken(user);

                var roleIds = user.MstUserRolesConfigs.Select(r => r.RoleId).ToList();

                bool isAppAdmin = roleIds.Contains((int)UserRole.AppAdmin);
                bool isDepartmentAdmin = roleIds.Contains((int)UserRole.DeptAdmin);
                bool isFacilityAdmin = roleIds.Contains((int)UserRole.FacilityAdmin);
                bool isManager = roleIds.Contains((int)UserRole.Manager);

                var facilityId = user?.MstUserRolesConfigs?
                    .Where(r => r.FacilityId.HasValue)                 // filter non-null
                    .Select(r => r.FacilityId.Value)                   // unwrap nullable
                    .ToList();
                // If Facility Admin, include list of facility names
                List<int> facilityAdminFacilityIds = new();

                if (isFacilityAdmin)
                {
                    facilityAdminFacilityIds = user.MstUserRolesConfigs
                        .Where(r => r.RoleId == 6 && r.Facility != null)
                        .Select(r => r.Facility.FacilityId)
                        .Distinct()
                        .ToList();
                }

                List<int> managerFacilityIds = new();
                if (isManager)
                {
                    managerFacilityIds = user.MstUserRolesConfigs
                        .Where(r => r.RoleId == 7 && r.Facility != null)
                        .Select(r => r.Facility.FacilityId)
                        .Distinct()
                        .ToList();
                }

                var response = new
                {
                    Token = token,
                    Username = user.Username,
                    Email = user.Email,
                    UserId = user.UserId,
                    RoleIds = roleIds,
                    FacilityIds = facilityId,
                    IsAppAdmin = isAppAdmin,
                    IsDepartmentAdmin = isDepartmentAdmin,
                    IsFacilityAdmin = isFacilityAdmin,
                    IsManager = isManager,
                    FacilityAdminFacilityIds = facilityAdminFacilityIds, // <-- Only IDs
                    ManagerFacilityIds = managerFacilityIds
                };


                return ApiResponseDto<object>.SuccessResponse(ApiMessages.LoginSuccess, StatusCodes.Status200OK, response);
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse(ex.Message, StatusCodes.Status500InternalServerError);
            }
        }



        public async Task<ApiResponseDto<bool>> CreateUserAsync(MstUserDto model, IFormFile fileName)
        {
            try
            {
                var userExist = await _context.MstUsers
                    .Include(r => r.MstUserRolesConfigs)
                    .FirstOrDefaultAsync(u => u.Username == model.Email || u.Email == model.Email);

                if (userExist != null)
                {
                    return ApiResponseDto<bool>.ErrorResponse(ApiMessages.UserAlreadyExists, StatusCodes.Status400BadRequest);
                }

                string encryptedPassword = PasswordHelper.EncryptPassword(model.Password);

                string profileImagePath = null;

                if (fileName != null && fileName.Length > 0)
                {
                    using (var ms = new MemoryStream())
                    {
                        await fileName.CopyToAsync(ms);
                        var imageBytes = ms.ToArray();
                        var base64Image = Convert.ToBase64String(imageBytes);

                        var folderName = "user-profile";
                        string firebaseUrl = await _firebaseStorageHelper.UploadBase64FileAsync(base64Image, fileName.FileName, folderName);

                        model.ProfileImageUrl = firebaseUrl;
                    }
                }

                var user = new MstUser
                {
                    Username = model.Email,
                    Email = model.Email,
                    FirstName = model.FirstName,
                    LastName = model.LastName,
                    Password = encryptedPassword,
                    Bio = model.Bio,
                    IsActive = model.IsActive,
                    IsSsoUser = model.IsSsoUser ?? false,
                    ProfileImageUrl = model.ProfileImageUrl,
                    CreatedAt = DateTime.UtcNow,
                    LanguageId = model.Language,
                    CreatedBy = model.CreatedBy ?? 0, // Assuming CreatedBy is optional
                };

                _context.MstUsers.Add(user);
                _context.SaveChanges();

                var rolesToAdd = (model.FacilityIds != null && model.FacilityIds.Any())
        ? model.FacilityIds.Select(fid => new MstUserRolesConfig
        {
            UserId = user.UserId,
            RoleId = model.RoleId,
            FacilityId = fid,
            CreatedAt = DateTime.UtcNow
        }).ToList()
        : new List<MstUserRolesConfig>
            {
                    new MstUserRolesConfig
                    {
                        UserId = user.UserId,
                        RoleId = model.RoleId,
                        FacilityId = null,
                        CreatedAt = DateTime.UtcNow
                    }
            };

                _context.MstUserRolesConfigs.AddRange(rolesToAdd);
                await _context.SaveChangesAsync();

                await _context.SaveChangesAsync();
                return ApiResponseDto<bool>.SuccessResponse(ApiMessages.UserRegisteredSuccessfully, StatusCodes.Status200OK, true);
            }
            catch (Exception ex)
            {
                // Log the exception here
                return ApiResponseDto<bool>.ErrorResponse(ApiMessages.InternalServerError, StatusCodes.Status500InternalServerError);
            }
        }

        public async Task<ApiResponseDto<bool>> UpdateUserProfileAsync(string password, IFormFile profileImage, int userId, string? ExistingProfileImageUrl, string EmployeeCode, string ContactNumber)
        {
            try
            {
                var user = await _context.MstUsers.FindAsync(userId);
                if (user == null)
                {
                    return ApiResponseDto<bool>.ErrorResponse("User not found.", StatusCodes.Status404NotFound);
                }

                // Update password if provided
                if (!string.IsNullOrEmpty(password))
                {
                    user.Password = PasswordHelper.EncryptPassword(password);
                }

               user.EmployeeCode = EmployeeCode;
               user.ContactNumber = ContactNumber;
                // Upload profile image if provided
                if (profileImage != null && profileImage.Length > 0)
                {
                    using (var ms = new MemoryStream())
                    {
                        await profileImage.CopyToAsync(ms);
                        var imageBytes = ms.ToArray();
                        var base64Image = Convert.ToBase64String(imageBytes);

                        var folderName = "user-profile";
                        string firebaseUrl = await _firebaseStorageHelper.UploadBase64FileAsync(base64Image, profileImage.FileName, folderName);

                        user.ProfileImageUrl = firebaseUrl;
                    }
                }
                else
                {
                    user.ProfileImageUrl = ExistingProfileImageUrl;
                }

                _context.MstUsers.Update(user);
                await _context.SaveChangesAsync();

                return ApiResponseDto<bool>.SuccessResponse("Profile updated successfully.", StatusCodes.Status200OK, true);
            }
            catch (Exception ex)
            {
                // Log exception if needed
                return ApiResponseDto<bool>.ErrorResponse("Internal server error.", StatusCodes.Status500InternalServerError);
            }
        }


        public async Task<List<MstUserDto>> GetAllUserAsync()
        {
            var userDetails = await _context.MstUsers.OrderByDescending(i => i.CreatedAt).ToListAsync();
            return _mapper.Map<List<MstUserDto>>(userDetails);
        }
        public async Task<List<MstLanguageDto>> GetAllLanguagesAsync()
        {
            var userDetails = await _context.MstLanguages.ToListAsync();
            return _mapper.Map<List<MstLanguageDto>>(userDetails);
        }
        public async Task<MstUserDto> GetUserByIdAsync(int userId)
        {
            var userDetails = await _context.MstUsers.FirstOrDefaultAsync(u=> u.UserId == userId);
            return _mapper.Map<MstUserDto>(userDetails);
        }

        public async Task<MstUserDto> GetUserByIdApiAsync(int userId)
        {
            var user = await _context.MstUsers
           .Where(u => u.UserId == userId)
           .Select(u => new MstUserDto
           {
               UserId = u.UserId,
               Username = u.Username,
               FirstName = u.FirstName,
               LastName = u.LastName,
               Email = u.Email,
               Password = PasswordHelper.DecryptPassword(u.Password), // Decrypt the password
               ProfileImageUrl = u.ProfileImageUrl,
               Bio = u.Bio,
               IsActive = u.IsActive,
               IsSsoUser = u.IsSsoUser,
               Language = u.LanguageId,
               EmployeeCode = u.EmployeeCode,
               ContactNumber = u.ContactNumber,
               facilityRoleConfigs = u.MstUserRolesConfigs.Select(r => new MstUserRolesConfigDto
               {
                   UserRoleConfigId = r.UserRoleConfigId,
                   FacilityId = r.FacilityId,
                   FacilityName =  _context.MstFacilities
                       .Where(f => f.FacilityId == r.FacilityId)
                       .Select(f => f.FacilityName)
                       .FirstOrDefault(),
                   RoleId = r.RoleId,
                   RoleName = r.Role.RoleName
               }).ToList()
           })
           .FirstOrDefaultAsync();

            return user;
        }


        public async Task<ApiResponseDto<object>> UpdateUserDetailsAsync(MstUserDto dto, IFormFile profileImage)
        {
            try
            {
                string encryptedPassword = PasswordHelper.EncryptPassword(dto.Password);

                var user = await _context.MstUsers.FirstOrDefaultAsync(x => x.UserId == dto.UserId);
                if (user == null)
                {
                    return ApiResponseDto<object>.ErrorResponse(ApiMessages.UserUpdateFailure, StatusCodes.Status404NotFound, new List<Error>
            {
                new Error { Code = 404, Message = "User not found.", Field = "UserId" }
            });
                }
                user.Username = dto.Email;
                user.Email = dto.Email;
                user.Password = encryptedPassword;
                user.FirstName = dto.FirstName;
                user.LastName = dto.LastName;
                user.Bio = dto.Bio;
                user.IsSsoUser = dto.IsSsoUser ?? false;
                user.IsActive = dto.IsActive;
                user.LanguageId = dto.Language;
                user.EmployeeCode = dto.EmployeeCode;
                user.ContactNumber = dto.ContactNumber;
                user.ModifiedAt = DateTime.Now;
                user.ModifiedBy = dto.CreatedBy ?? 0; // Assuming CreatedBy is optional

                // Save profile image
                if (profileImage != null && profileImage.Length > 0)
                {
                    using (var ms = new MemoryStream())
                    {
                        await profileImage.CopyToAsync(ms);
                        var imageBytes = ms.ToArray();
                        var base64Image = Convert.ToBase64String(imageBytes);

                        var folderName = "user-profile";
                        string firebaseUrl = await _firebaseStorageHelper.UploadBase64FileAsync(base64Image, profileImage.FileName, folderName);

                        user.ProfileImageUrl = firebaseUrl;
                    }
                }
                else {
                    user.ProfileImageUrl = dto.ExistingProfileImageUrl;
                }
                await _context.SaveChangesAsync();

                // Remove deleted role mappings
                if (dto.RemovedUserRoleConfigIds != null && dto.RemovedUserRoleConfigIds.Any())
                {
                    var toDelete = await _context.MstUserRolesConfigs
                        .Where(x => dto.RemovedUserRoleConfigIds.Contains(x.UserRoleConfigId))
                        .ToListAsync();

                    _context.MstUserRolesConfigs.RemoveRange(toDelete);
                }

                // Add or update role mappings
                foreach (var config in dto.facilityRoleConfigs)
                {
                    if (config.UserRoleConfigId > 0)
                    {
                        var existingConfig = await _context.MstUserRolesConfigs
                            .FirstOrDefaultAsync(x => x.UserRoleConfigId == config.UserRoleConfigId);

                        if (existingConfig != null)
                        {
                            existingConfig.FacilityId = config.FacilityId;
                            existingConfig.RoleId = config.RoleId;
                        }
                    }
                    else
                    {
                        var newConfig = new MstUserRolesConfig
                        {
                            UserId = user.UserId,
                            FacilityId = config.FacilityId,
                            RoleId = config.RoleId
                        };
                        _context.MstUserRolesConfigs.Add(newConfig);
                    }
                }

                await _context.SaveChangesAsync();

                return ApiResponseDto<object>.SuccessResponse(ApiMessages.UserUpdateSuccess, StatusCodes.Status200OK);
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse(ApiMessages.UserUpdateFailure, StatusCodes.Status500InternalServerError, new List<Error>
        {
            new Error { Code = 500, Message = ex.Message, Field = null }
        });
            }
        }
        public async Task<List<MstUserRolesConfigDto>> GetAllUserRolesConfigAsync(int? roleId = null, int? facilityId = null)
        {
            var query = _context.MstUserRolesConfigs
                .Include(u => u.User)
                .Include(f => f.Facility)
                .Include(r => r.Role)
                .Where(x => x.User.IsActive == true);

            if (roleId.HasValue)
                query = query.Where(x => x.RoleId == roleId.Value);

            if (facilityId.HasValue)
                query = query.Where(x => x.FacilityId == facilityId.Value);

            var userDetails = await query
                .Select(x => new MstUserRolesConfigDto
                {
                    UserRoleConfigId = x.UserRoleConfigId,
                    Username = x.User.Username,
                    RoleName = x.Role.RoleName,
                    FacilityName = x.Facility.FacilityName ?? "N/A"
                })
                .ToListAsync();

            return userDetails;
        }


        private string GenerateJwtToken(MstUser user)
        {
            var claims = new List<Claim>
    {
        new Claim(JwtRegisteredClaimNames.Sub, user.Username),
        new Claim("UserId", user.UserId.ToString()),
        new Claim("Username", user.Username),
        new Claim("Email", user.Email ?? ""),
        new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString())
    };

            // Add one claim per RoleId
            if (user.MstUserRolesConfigs != null)
            {
                foreach (var role in user.MstUserRolesConfigs)
                {
                    claims.Add(new Claim("RoleId", role.RoleId.ToString()));
                }
            }

            var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_configuration["Jwt:Key"]));
            var creds = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);

            var token = new JwtSecurityToken(
                issuer: _configuration["Jwt:Issuer"],
                audience: _configuration["Jwt:Audience"],
                claims: claims,
                expires: DateTime.UtcNow.AddHours(Convert.ToDouble(_configuration["Jwt:ExpireMinutes"])),
                signingCredentials: creds
            );

            return new JwtSecurityTokenHandler().WriteToken(token);
        }

        public async Task<List<object>> GetUsersByFacilityId(int facilityId)
        {
            var query = _context.MstUserRolesConfigs
                 .Include(u => u.User)
                 .Include(f => f.Facility)
                 .Include(r => r.Role)
                 .Where(x => x.User.IsActive == true && x.FacilityId == facilityId);


            var userDetails = await query
                .Select(x => new 
                {
                    UserId = x.UserId,
                    Username = x.User.Username,
                    RoleName = x.Role.RoleName,
                    FacilityName = x.Facility.FacilityName ?? "N/A"
                })
                .ToListAsync();
            return _mapper.Map<List<object>>(userDetails);
        }

        public async Task<List<FacilityRoleDto>> GetUserFacilitiesWithRoles(int userId)
        {
            var facilities = await _context.MstUserRolesConfigs
        .Include(x => x.Facility)
        .Where(x => x.UserId == userId && x.User.IsActive == true)
        .GroupBy(x => new { x.FacilityId, x.Facility.FacilityName }) // Group by Facility
        .Select(g => new FacilityRoleDto
        {
            FacilityId = g.Key.FacilityId ?? 0,
            FacilityName = g.Key.FacilityName ?? "N/A",
            //RoleId = 0, // Optional: you can leave this out or set to 0/null if you don't want a specific Role
            //RoleName = "" // Optional: same here
        })
        .ToListAsync();

            return facilities;
        }

        public async Task<List<MstFacilityDto>> GetUserFacilitiesByFacilityId(List<int>? facilityId)
        {
            var facilities = await _context.MstFacilities
                .Where(x =>
                  (facilityId == null || !facilityId.Any() || facilityId.Contains(x.FacilityId)) && x.IsActive
                )
                .GroupBy(x => new { x.FacilityId, x.FacilityName })
                .Select(g => new MstFacilityDto
                {
                    FacilityId = g.Key.FacilityId,
                    FacilityName = g.Key.FacilityName ?? "N/A"
                })
                .ToListAsync();

            return facilities;
        }

    }

    public class FacilityRoleDto
    {
        public int FacilityId { get; set; }
        public string FacilityName { get; set; }
        public int? RoleId { get; set; }
        public string RoleName { get; set; }
    }
}
