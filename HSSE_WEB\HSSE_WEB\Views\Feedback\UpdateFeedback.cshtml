﻿@{
    ViewData["Title"] = "Update Feedback";
}
<div class="card mt-4">
    <div class="card-body">
        <h4 class="card-title">Feedback List</h4>
        <div class="table-responsive">
            <table id="feedBack-listing" class="table">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>Name</th>
                        <th>Title</th>
                        <th>Description</th>
                        <th>Facility</th>
                        <th>Date</th>
                        <th>Status</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
        </div>
    </div>
</div>

<div class="modal fade" id="editFeedbackPreviewModal" tabindex="-1" role="dialog" aria-labelledby="previewModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Update Feedback</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>

            <div class="modal-body">
                <table class="table table-bordered">
                    <tbody>
                        <tr><th>Name</th><td id="previewName"></td></tr>
                        <tr><th>Title</th><td id="previewTitle"></td></tr>
                        <tr><th>Facility</th><td id="previewFacility"></td></tr>
                        <tr><th>Description</th><td id="previewDescription"></td></tr>
                        <tr>
                            <th>File</th>
                            <td id="previewFile">
                                <!-- Will be populated via JavaScript -->
                            </td>
                        </tr>
                        <tr>
                            <th>Response / Reason</th>
                            <td>
                                <textarea id="previewReason" class="form-control" rows="3" placeholder="Enter response..."></textarea>
                            </td>
                        </tr>
                        <tr>
                            <th>Status</th>
                            <td>
                                <select id="previewStatus" class="form-control">
                                    <option value="0">Open</option>
                                    <option value="1">Closed</option>
                                </select>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="modal-footer">
                <button id="confirmSubmitBtn" type="button" class="btn btn-primary">Submit</button>
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="feedbackPreviewModal" tabindex="-1" role="dialog" aria-labelledby="previewModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-centered" role="document">
        <div class="modal-content rounded-lg shadow-sm">
            <div class="modal-header">
                <h5 class="modal-title font-weight-semibold">Feedback Details</h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>

            <div class="modal-body px-4 py-3">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <div class="border p-3 rounded">
                            <label class="text-muted mb-1">Name</label>
                            <div id="previewNameDetails" class="text-dark font-weight-bold"></div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="border p-3 rounded">
                            <label class="text-muted mb-1">Facility</label>
                            <div id="previewFacilityDetails" class="text-dark font-weight-bold"></div>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <div class="border p-3 rounded">
                        <label class="text-muted mb-1">Title</label>
                        <div id="previewTitleDetails" class="text-dark font-weight-bold"></div>
                    </div>
                </div>

                <div class="mb-3">
                    <div class="border p-3 rounded">
                        <label class="text-muted mb-1">Description</label>
                        <div id="previewDescriptionDetails" class="text-dark" style="white-space: pre-wrap;"></div>
                    </div>
                </div>

                <div class="mb-3">
                    <div class="border p-3 rounded">
                        <label class="text-muted mb-1">Attached File</label>
                        <div id="previewFileDetails" class="text-dark mt-1">
                            <!-- File link injected here -->
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <div class="border p-3 rounded">
                        <label class="text-muted mb-1">Status</label>
                        <div>
                            <span id="previewStatusDetails" class="badge badge-pill badge-outline-primary"></span>
                        </div>
                    </div>
                </div>

                <div class="mb-0">
                    <div class="border p-3 rounded">
                        <label class="text-muted mb-1">Response</label>
                        <div id="previewResponseDetails" class="text-dark" style="white-space: pre-wrap;"></div>
                    </div>
                </div>
            </div>

            <div class="modal-footer border-top-0">
                <button type="button" class="btn btn-secondary px-4" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>


<script>
                    const createFeedbackUrl = '@Url.Action("CreateFeedback", "Feedback")';
            const getAllFacilitiesUrl = '@Url.Action("GetAllFacilities", "Feedback")'; // Assuming Facility controller is accessible, adjust if needed
    const getFeedback = '@Url.Action("GetFeedbackByFacilityAndUser", "Feedback")'; // Assuming Facility controller is accessible, adjust if needed

    const getFeedbackById =  '@Url.Action("GetFeedbackById", "Feedback")?id=';
    const updateFeedbackWithResponse = '@Url.Action("UpdateFeedbackWithResponse", "Feedback")';

</script>
@section Scripts {
    <script src="~/js/feedback/updateFeedback.js"></script>

} 