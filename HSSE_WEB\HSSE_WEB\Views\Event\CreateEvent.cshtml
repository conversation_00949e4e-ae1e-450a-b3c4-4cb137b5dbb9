@model HSSE_Models_Dto.ViewModels.AddUserViewModel

@{
    ViewData["Title"] = "Create Event";
}

<div class="col-12 grid-margin">
    <div class="card">
        <div class="card-body">
            <h4 class="card-title">Create Event</h4>
            <form id="eventForm" class="form-sample" enctype="multipart/form-data">
                <p class="card-description">Event Details</p>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group row">
                            <label for="Title" class="col-sm-4 col-form-label">Title <span class="text-danger">*</span></label>
                            <div class="col-sm-8">
                                <input type="text" class="form-control" id="Title" name="Title" required />
                            </div>
                        </div>
                        <div class="form-group row">
                            <label for="ExternalLink" class="col-sm-4 col-form-label">External Link</label>
                            <div class="col-sm-8">
                                <input type="text" class="form-control" id="ExternalLink" name="ExternalLink" />
                            </div>
                        </div>
                        <div class="form-group row">
                            <label for="Location" class="col-sm-4 col-form-label">Location <span class="text-danger">*</span></label>
                            <div class="col-sm-8">
                                <input type="text" class="form-control" id="Location" name="Location" />
                            </div>
                        </div>
                       <div class="form-group row">
                            <label class="col-sm-4 col-form-label">Group  <span class="text-danger">*</span></label>
                            <div class="col-sm-8">
                                <select name="GroupId" id="GroupId" class="form-control js-example-basic-multiple w-100" multiple="multiple">
                                    <option value="">-- Select Group --</option>
                                    @foreach (var group in Model.Group)
                                    {
                                            <option value="@group.GroupId">@group.GroupName</option>
                                    }
                                </select>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-sm-4 col-form-label">RSVP</label>
                            <div class="form-check">
                                <label class="form-check-label">
                                    <input type="checkbox" class="form-check-input" name="IsRsvp" id="IsRsvp" />
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                     
                        <div class="form-group row">
                            <label for="EventDateTime" class="col-sm-4 col-form-label">Event Date & Time<span class="text-danger">*</span></label>
                            <div class="col-sm-8">
                                <input type="datetime-local" class="form-control" id="EventDateTime" name="EventDateTime" />
                            </div>
                        </div>
                    
                             <div class="form-group row">
                            <label class="col-sm-4 col-form-label">Schedule At</label>
                            <div class="col-sm-8">
                                <input type="datetime-local" class="form-control" name="ScheduledAt" id="ScheduledAt" />
                            </div>
                        </div>
                             <div class="form-group row">
                            <label class="col-sm-4 col-form-label">Expiry At</label>
                            <div class="col-sm-8">
                                <input type="datetime-local" class="form-control" name="ExpiryAt" id="ExpiryAt" />
                            </div>
                        </div>
                        <div class="form-group row">
                            <label for="Attachment" class="col-sm-4 col-form-label">Attachment</label>
                            <div class="col-sm-8">
                                <div class="custom-file-upload w-100">
                                    <input type="file" class="d-none" id="EditAttachment" name="EditAttachment" accept="image/*" />
                                    <div class="input-group">
                                        <input type="hidden" id="ExistingAttachment" />
                                        <input type="text" class="form-control file-upload-info" id="AttachmentFileName" readonly placeholder="Choose Thumbnail" />
                                        <div class="input-group-append">
                                            <button class="btn btn-primary btn-sm" type="button" id="AttachmentBtn">Upload</button>
                                            <button class="btn btn-danger btn-sm ml-1 d-none" type="button" id="removeAttachmentBtn">Remove</button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>
                        <div class="form-group row">
                            <label for="MediaUrl" class="col-sm-4 col-form-label">Thumbnail</label>
                            <div class="col-sm-8">
                                <div class="custom-file-upload w-100">
                                     <input type="file" class="d-none" id="MediaFile" name="ThumbnailImage" accept="image/*" />
                                    <div class="input-group">
                                        <input type="text" class="form-control file-upload-info" id="ThumbnailFileName" readonly placeholder="Choose Thumbnail" />
                                        <div class="input-group-append">
                                            <button class="btn btn-primary btn-sm" type="button" id="ThumbnailBtn">Upload</button>
                                            <button class="btn btn-danger btn-sm ml-1 d-none" type="button" id="removeThumbnailBtn">Remove</button>
                                        </div>
                                    </div>
                                    <div class="mt-2 d-none" id="ThumbnailPreviewContainer">
                                        <img id="ThumbnailPreview" src="" class="img-thumbnail" style="max-width: 250px;" />
                                    </div>
                                </div>
                            </div>
                        
                        </div>
                     
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-group row">
                            <label class="col-sm-2 col-form-label">Description</label>
                            <div class="col-sm-12">
                                <textarea name="Description" id="tinyMceExample" class="form-control"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-12 text-end">
                        <button type="submit" class="btn btn-primary">Create Event</button>
                        <button type="reset" class="btn btn-light">Cancel</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>


<hr />
<div class="card mt-4">
    <div class="card-body">
        <h4 class="card-title">Events List</h4>
        <div class="table-responsive">
            <table id="order-listing" class="table">
    <thead>
        <tr>
            <th>#</th>
            <th>Title</th>
            <th>Description</th>
            <th>Date & Time</th>
            <th>Location</th>
            <th>Is RSVP</th>
            <th>Action</th>
        </tr>
    </thead>
    <tbody>
    </tbody>
            </table>
        </div>
    </div>
</div>
<div class="modal fade" id="rsvpModal" tabindex="-1" aria-labelledby="rsvpModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-md">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="rsvpModalLabel">Registered Users</h5>
                <button type="button" class="close" data-dismiss="modal"><span>&times;</span></button>
            </div>
            <div class="modal-body">
                <div id="rsvpUserList"></div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="editEventModal" tabindex="-1" aria-labelledby="editEventModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editEventModalLabel">Edit Event</h5>
                <button type="button" class="close" data-dismiss="modal"><span>&times;</span></button>
            </div>
            <div class="modal-body">
                <form id="editEventForm">
                    <input type="hidden" id="editEventId" />
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group row">
                                <label for="editTitle" class="col-sm-4 col-form-label">Title <span class="text-danger">*</span></label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" id="editTitle" name="editTitle" required />
                                </div>
                            </div>
                            <div class="form-group row">
                                <label for="editExternalLink" class="col-sm-4 col-form-label">External Link</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" id="editExternalLink" name="editExternalLink" />
                                </div>
                            </div>
                            <div class="form-group row">
                                <label for="editLocation" class="col-sm-4 col-form-label">Location <span class="text-danger">*</span></label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" id="editLocation" name="editLocation" />
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-4 col-form-label">Group <span class="text-danger">*</span></label>
                                <div class="col-sm-8">
                                    <select name="editGroupId" id="editGroupId" class="form-control js-example-basic-multiple w-100" multiple="multiple">
                                        <option value="">-- Select Group --</option>
                                        @foreach (var group in Model.Group)
                                        {
                                            <option value="@group.GroupId">@group.GroupName</option>
                                        }
                                    </select>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-4 col-form-label">RSVP</label>
                                <div class="form-check">
                                    <label class="form-check-label">
                                        <input type="checkbox" class="form-check-input" name="editIsRsvp" id="editIsRsvp" />
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group row">
                                <label for="editEventDateTime" class="col-sm-4 col-form-label">Event Date & Time<span class="text-danger">*</span></label>
                                <div class="col-sm-8">
                                    <input type="datetime-local" class="form-control" id="editEventDateTime" name="editEventDateTime" />
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-4 col-form-label">Schedule At</label>
                                <div class="col-sm-8">
                                    <input type="datetime-local" class="form-control" name="editScheduledAt" id="editScheduledAt" />
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-4 col-form-label">Expiry At</label>
                                <div class="col-sm-8">
                                    <input type="datetime-local" class="form-control" name="editExpiryAt" id="editExpiryAt" />
                                </div>
                            </div>
                            <div class="form-group row">
                                <label for="Attachment" class="col-sm-4 col-form-label">Attachment</label>
                                <div class="col-sm-8">
                                    <div class="custom-file-upload w-100">
                                        <input type="file" class="d-none" id="EditAttachment" name="EditAttachment" accept="image/*" />
                                        <div class="input-group">
                                            <input type="text" class="form-control file-upload-info" id="EditAttachmentFileName" readonly placeholder="Choose Attachment" />
                                            <div class="input-group-append">
                                                <button class="btn btn-primary btn-sm" type="button" id="EditAttachmentBtn">Upload</button>
                                                <button class="btn btn-danger btn-sm ml-1 d-none" type="button" id="removeAttachmentThumbnailBtn">Remove</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                            </div>
                            <div class="form-group row">
                                <label for="editMediaUrl" class="col-sm-4 col-form-label">Thumbnail</label>
                                <div class="col-sm-8">
                                    <div class="custom-file-upload w-100">
                                        <input type="file" class="d-none" id="editMediaFile" name="editThumbnailImage" accept="image/*" />
                                        <div class="input-group">
                                            <input type="text" class="form-control file-upload-info" id="editThumbnailFileName" readonly placeholder="Choose Thumbnail" />
                                            <div class="input-group-append">
                                                <button class="btn btn-primary btn-sm" type="button" id="editThumbnailBtn">Upload</button>
                                                <button class="btn btn-danger btn-sm ml-1 d-none" type="button" id="removeEditThumbnailBtn">Remove</button>
                                            </div>
                                        </div>
                                        <div class="mt-2 d-none" id="editThumbnailPreviewContainer">
                                            <img id="editThumbnailPreview" src="" class="img-thumbnail" style="max-width: 250px;" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label">Description</label>
                                <div class="col-sm-12">
                                    <textarea name="editDescription" id="editTinyMceExample" class="form-control"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveEditEventBtn">Save Changes</button>
            </div>
        </div>
    </div>
</div>

<script>
    var getEventRsvpDetails = '@Url.Action("GetEventRsvpDetails", "Event")?eventId='; 
    var InsertOrUpdateEvent = '@Url.Action("InsertOrUpdateEvent", "Event")';
    var getEvents ='@Url.Action("GetEvents", "Event")';
    var toggleEventActivation  = '@Url.Action("ToggleEventActivation", "Event")';
            var getEventById  = '@Url.Action("GetEventById", "Event")?id=';
</script>
@section Scripts {
    <script src="~/js/Events/event.js"></script>
} 