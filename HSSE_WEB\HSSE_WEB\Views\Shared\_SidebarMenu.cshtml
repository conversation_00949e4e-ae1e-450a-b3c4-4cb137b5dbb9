﻿@model List<HSSE_Models_Dto.ModelsDto.PermissionDto>

<nav class="sidebar sidebar-offcanvas" id="sidebar">
    <ul class="nav">
        @foreach (var item in Model.OrderBy(m => m.OrderNo))
        {
            if (item.Children != null && item.Children.Any())
            {
                <li class="nav-item">
                    <a class="nav-link" data-toggle="collapse" href="#<EMAIL>" aria-expanded="false" aria-controls="<EMAIL>">
                        <i class="mdi @item.Icon menu-icon"></i>
                        <span class="menu-title">@item.MenuName</span>
                        <i class="menu-arrow"></i>
                    </a>
                    <div class="collapse" id="<EMAIL>">
                        <ul class="nav flex-column sub-menu">
                            @foreach (var child in item.Children.OrderBy(c => c.OrderNo))
                            {
                                <li class="nav-item">
                                    <a class="nav-link" asp-controller="@child.ControllerName" asp-action="@child.ActionName">
                                        @child.MenuName
                                    </a>
                                </li>
                            }
                        </ul>
                    </div>
                </li>
            }
            else
            {
                <li class="nav-item">
                    <a class="nav-link" asp-controller="@item.ControllerName" asp-action="@item.ActionName">
                        <i class="mdi @item.Icon menu-icon"></i>
                        <span class="menu-title">@item.MenuName</span>
                    </a>
                </li>
            }
        }
    </ul>
</nav>
