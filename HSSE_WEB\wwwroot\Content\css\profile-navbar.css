/* Profile Navbar Styles */
.navbar-nav-right {
    margin-left: auto;
}

.nav-profile {
    position: relative;
}

.nav-profile .nav-link {
    display: flex;
    align-items: center;
    padding: 0.5rem 1rem;
    color: #6c757d;
    text-decoration: none;
    border-radius: 0.375rem;
    transition: all 0.3s ease;
}

.nav-profile .nav-link:hover {
    background-color: rgba(0, 0, 0, 0.05);
    color: #495057;
}

.nav-profile-img {
    position: relative;
    margin-right: 0.75rem;
}

.nav-profile-img img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid #e9ecef;
}

.availability-status {
    position: absolute;
    bottom: 2px;
    right: 2px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
}

.availability-status.online {
    background-color: #28a745;
}

.availability-status.offline {
    background-color: #6c757d;
}

.availability-status.away {
    background-color: #ffc107;
}

.nav-profile-text {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    min-width: 0;
}

.nav-profile-text span {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 150px;
}

.nav-profile-text .font-weight-bold {
    font-size: 0.875rem;
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.125rem;
}

.nav-profile-text .text-secondary {
    font-size: 0.75rem;
    color: #6c757d;
}

/* Dropdown Menu Styles */
.nav-profile .dropdown-menu {
    min-width: 200px;
    border: 1px solid #e9ecef;
    border-radius: 0.5rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    padding: 0.5rem 0;
    margin-top: 0.5rem;
}

.nav-profile .dropdown-item {
    display: flex;
    align-items: center;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    color: #495057;
    text-decoration: none;
    transition: all 0.2s ease;
}

.nav-profile .dropdown-item:hover {
    background-color: #f8f9fa;
    color: #495057;
}

.nav-profile .dropdown-item i {
    margin-right: 0.75rem;
    font-size: 1rem;
    width: 16px;
    text-align: center;
}

.nav-profile .dropdown-divider {
    margin: 0.5rem 0;
    border-top: 1px solid #e9ecef;
}

/* Responsive Design */
@media (max-width: 767px) {
    .nav-profile-text {
        display: none;
    }
    
    .nav-profile-img {
        margin-right: 0;
    }
    
    .nav-profile .dropdown-menu {
        right: 0;
        left: auto;
        min-width: 180px;
    }
}

@media (max-width: 575px) {
    .nav-profile .nav-link {
        padding: 0.25rem 0.5rem;
    }
    
    .nav-profile-img img {
        width: 32px;
        height: 32px;
    }
    
    .availability-status {
        width: 10px;
        height: 10px;
        bottom: 1px;
        right: 1px;
    }
}

/* Dark theme support */
.navbar-dark .nav-profile .nav-link {
    color: rgba(255, 255, 255, 0.75);
}

.navbar-dark .nav-profile .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.9);
}

.navbar-dark .nav-profile-text .font-weight-bold {
    color: rgba(255, 255, 255, 0.9);
}

.navbar-dark .nav-profile-text .text-secondary {
    color: rgba(255, 255, 255, 0.6);
}

.navbar-dark .nav-profile .dropdown-menu {
    background-color: #343a40;
    border-color: #495057;
}

.navbar-dark .nav-profile .dropdown-item {
    color: rgba(255, 255, 255, 0.75);
}

.navbar-dark .nav-profile .dropdown-item:hover {
    background-color: #495057;
    color: rgba(255, 255, 255, 0.9);
}

.navbar-dark .nav-profile .dropdown-divider {
    border-top-color: #495057;
}

/* Animation for dropdown */
.nav-profile .dropdown-menu {
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    pointer-events: none;
    display: block !important;
}

.nav-profile.show .dropdown-menu {
    opacity: 1;
    transform: translateY(0);
    pointer-events: auto;
}

/* Ensure dropdown shows on hover and click */
.nav-profile:hover .dropdown-menu,
.nav-profile.show .dropdown-menu {
    opacity: 1;
    transform: translateY(0);
    pointer-events: auto;
}

/* Profile image hover effect */
.nav-profile-img img:hover {
    transform: scale(1.05);
    transition: transform 0.2s ease;
}

/* Better spacing for profile text */
.nav-profile-text {
    line-height: 1.2;
}

/* Dropdown arrow indicator */
.nav-profile .nav-link::after {
    margin-left: 0.5rem;
    vertical-align: middle;
    border-top: 0.3em solid;
    border-right: 0.3em solid transparent;
    border-bottom: 0;
    border-left: 0.3em solid transparent;
    transition: transform 0.2s ease;
}

.nav-profile.show .nav-link::after {
    transform: rotate(180deg);
}

/* Custom scrollbar for long dropdown menus */
.nav-profile .dropdown-menu {
    max-height: 300px;
    overflow-y: auto;
}

.nav-profile .dropdown-menu::-webkit-scrollbar {
    width: 4px;
}

.nav-profile .dropdown-menu::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 2px;
}

.nav-profile .dropdown-menu::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 2px;
}

.nav-profile .dropdown-menu::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
