(function ($) {
    // General showToast function to display toast dynamically
    showToast = function (heading, text, icon, loaderBg, position) {
        'use strict';
        resetToastPosition();
        $.toast({
            heading: heading,
            text: text,
            showHideTransition: 'slide',
            icon: icon,
            loaderBg: loaderBg,
            position: position || 'top-right' // Default position is top-right if not specified
        })
    };

    // Function to handle success messages
    showSuccessToast = function (message) {
        'use strict';
        showToast(
            'Success',
            message || 'Your operation was successful!',
            'success',
            '#f96868'
        );
    };

    // Function to handle info messages
    showInfoToast = function (message) {
        'use strict';
        showToast(
            'Info',
            message || 'Here is some information you should know.',
            'info',
            '#46c35f'
        );
    };

    // Function to handle warning messages
    showWarningToast = function (message) {
        'use strict';
        showToast(
            'Warning',
            message || 'Be cautious! Something might need your attention.',
            'warning',
            '#57c7d4'
        );
    };

    // Function to handle danger messages
    showDangerToast = function (message) {
        'use strict';
        showToast(
            'Danger',
            message || 'Something went wrong! Please check again.',
            'error',
            '#f2a654'
        );
    };

    // Optional function to show toast with custom position
    showToastPosition = function (message, position) {
        'use strict';
        showToast(
            'Positioning',
            message || 'This toast is positioned dynamically!',
            'info',
            '#f96868',
            position
        );
    };

    // Function for custom positioning of the toast
    showToastInCustomPosition = function () {
        'use strict';
        $.toast({
            heading: 'Custom Positioning',
            text: 'This is a custom positioned toast!',
            icon: 'info',
            position: {
                left: 120,
                top: 120
            },
            stack: false,
            loaderBg: '#f96868'
        })
    };

    // Reset the position of the toast
    resetToastPosition = function () {
        $('.jq-toast-wrap').removeClass('bottom-left bottom-right top-left top-right mid-center');
        $(".jq-toast-wrap").css({
            "top": "",
            "left": "",
            "bottom": "",
            "right": ""
        });
    }

})(jQuery);
