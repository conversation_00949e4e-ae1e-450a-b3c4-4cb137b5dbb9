@model HSSE_Models_Dto.ModelsDto.MstNewsletterDto

@{
    ViewData["Title"] = "Newsletter Details";
}
<div class="container">
    @if (Model == null)
    {
        <div class="alert alert-danger text-center" role="alert">
            <i class="bi bi-exclamation-triangle-fill me-2"></i>
            Unable to load newsletter details. Please try again later.
        </div>
        <div class="text-center mt-3">
            <a href="/Newsletter/ViewNewsletter" class="btn btn-outline-primary">
                <i class="bi bi-arrow-left me-1"></i> Back to Newsletters
            </a>
        </div>
    }
    else
    {
        <div class="card shadow-lg border-0">
            <div class="card-header  py-3 d-flex justify-content-between align-items-center flex-wrap">
                <h3 class="card-title mb-0">
                    <i class="mdi mdi-newspaper"></i> @Model.Title
                </h3>
                @if (!string.IsNullOrEmpty(Model.FileUrl))
                {
                    <a href="@Model.FileUrl"
                       class="btn btn-outline-info btn-sm ms-auto"
                       target="_blank"
                       download>
                        <i class="mdi mdi-folder-download"></i>Attachment
                    </a>
                }
            </div>
            <div class="card-body">
                @if (!string.IsNullOrEmpty(Model.ThumbnailPath))
                {
                    <div class="mb-4 text-left">
                        <img src="@Model.ThumbnailPath"
                             class="img-fluid rounded shadow-sm"
                             style="max-height: 200px; object-fit: cover;"
                             alt="Newsletter Thumbnail" />
                    </div>
                }
                <div class="mb-4 fs-5 text-muted">
                    @Html.Raw(Model.Description)
                </div>
                <div class="mb-3 d-flex flex-wrap gap-3 align-items-center">
                    <span class="badge bg-light text-dark border mr-2">Date: <strong>@(Model.CreatedAt?.ToString("MMM dd, yyyy") ?? "N/A")</strong></span>
                </div>
                <div class="d-flex flex-wrap gap-3 mt-4">
                    <a asp-controller="Newsletter"
                       asp-action="ViewNewsletter" class="btn btn-outline-secondary rounded-pill px-4 py-2 fw-semibold shadow-sm">
                        <i class="bi bi-arrow-left-circle me-1"></i> Back
                    </a>
                </div>
            </div>
        </div>
    }
</div> 