using HSSE_Domain.Models;
using HSSE_Models_Dto.ModelsDto;
using HSSE_Service.Constant;
using HSSE_Service.InterfaceService;
using HSSE_Service.ServiceResponce;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using System.Net.Http;
using Microsoft.Extensions.Configuration;
using System.Net.Http.Json;
using static Microsoft.AspNetCore.Hosting.Internal.HostingApplication;

namespace HSSE_Service.DataManager
{
    public class PermissionService : IPermissionService
    {
        private readonly HttpClient _httpClient;
        private readonly string _apiBaseUrl;
                private readonly HsseDbLatestContext _context;

        public PermissionService(IHttpClientFactory httpClientFactory, IConfiguration configuration, HsseDbLatestContext context)
        {
            _httpClient = httpClientFactory.CreateClient("AuthenticatedClient");
            var baseUrl = configuration["ExternalApi:BaseUrl"];
            if (!string.IsNullOrEmpty(baseUrl))
            {
                _httpClient.BaseAddress = new Uri(baseUrl);
            }
            _apiBaseUrl = configuration["ExternalApi:BaseUrl"];
            _context = context;
        }

        public async Task<ApiResponseDto<List<PermissionDto>>> GetPermissionsByUserIdAsync(int roleId)
        {
            try
            {
                var apiUrl = $"{_apiBaseUrl}/api/Permission/GetPermissionsByUserId?roleId={roleId}";
                var response = await _httpClient.GetAsync(apiUrl);

                if (response.IsSuccessStatusCode)
                {
                    var apiResult = await response.Content.ReadFromJsonAsync<ApiResponseDto<List<PermissionDto>>>();
                    return ApiResponseDto<List<PermissionDto>>.SuccessResponse(
                        apiResult?.Message ?? "Permissions fetched successfully.",
                        (int)response.StatusCode,
                        apiResult?.Data ?? new List<PermissionDto>()
                    );
                }
                else
                {
                    return ApiResponseDto<List<PermissionDto>>.ErrorResponse(
                        "Failed to fetch permissions.",
                        (int)response.StatusCode
                    );
                }
            }
            catch (Exception ex)
            {
                return ApiResponseDto<List<PermissionDto>>.ErrorResponse(
                    "Failed to fetch permissions.",
                    500,
                    new List<HSSE_Service.ServiceResponce.Error> {
                new HSSE_Service.ServiceResponce.Error { Code = 500, Message = ex.Message }
                    }
                );
            }
        }
        public async Task<ApiResponseDto<bool>> UpdatePermissionOrderAsync(List<PermissionOrderUpdateDto> orderUpdates)
        {
            try
            {
                if (orderUpdates == null || !orderUpdates.Any())
                    return ApiResponseDto<bool>.ErrorResponse("No data provided", StatusCodes.Status400BadRequest);

                var permissionIds = orderUpdates.Select(x => x.PermissionId).ToList();
                var permissions = await _context.MstPermissions
                    .Where(p => permissionIds.Contains(p.PermissionId))
                    .ToListAsync();

                foreach (var update in orderUpdates)
                {
                    var permission = permissions.FirstOrDefault(p => p.PermissionId == update.PermissionId);
                    if (permission != null)
                    {
                        permission.OrderNo = update.OrderNo;
                    }
                }

                await _context.SaveChangesAsync();
                return ApiResponseDto<bool>.SuccessResponse("Order updated successfully", StatusCodes.Status200OK, true);
            }
            catch (Exception ex)
            {
                return ApiResponseDto<bool>.ErrorResponse($"Error: {ex.Message}", StatusCodes.Status500InternalServerError);
            }
        }
        public async Task<ApiResponseDto<bool>> UpdatePermissionForRoleAsync(UpdatePermissionRequest request)
        {
            try
            {
                if (request == null || request.PermissionId <= 0 || request.RoleId <= 0)
                    return ApiResponseDto<bool>.ErrorResponse("Invalid request data", StatusCodes.Status400BadRequest);

                // Fetch RoleMenuPermission by PermissionId and RoleId
                var rolePermission = await _context.MstRoleMenuPermissions
                    .FirstOrDefaultAsync(p => p.PermissionId == request.PermissionId && p.RoleId == request.RoleId);

                if (rolePermission == null)
                    return ApiResponseDto<bool>.ErrorResponse("Role-based permission not found", StatusCodes.Status404NotFound);

                // Update RoleMenuPermissions (CanCreate, View, Edit, Delete)
                rolePermission.CanCreate = request.CanCreate;
                rolePermission.CanView = request.CanView;
                rolePermission.CanEdit = request.CanEdit;
                rolePermission.CanDelete = request.CanDelete;

                // Fetch main permission to update menu name
                var permission = await _context.MstPermissions
                    .FirstOrDefaultAsync(p => p.PermissionId == request.PermissionId);

                if (permission != null)
                {
                    permission.MenuName = request.MenuName;
                }

                await _context.SaveChangesAsync();

                return ApiResponseDto<bool>.SuccessResponse("Permission updated successfully", StatusCodes.Status200OK, true);
            }
            catch (Exception ex)
            {
                return ApiResponseDto<bool>.ErrorResponse($"Internal server error: {ex.Message}", StatusCodes.Status500InternalServerError);
            }
        }
        public async Task<List<PermissionDto>> GetParentMenusAsync()
        {
            var parentPermissions = await _context.MstPermissions
                .Where(p => p.ParentMenuId == null || p.ParentMenuId == 0)
                .OrderBy(p => p.OrderNo)
                .Select(p => new PermissionDto
                {
                    PermissionId = p.PermissionId,
                    MenuName = p.MenuName
                })
                .ToListAsync();

            return parentPermissions;
        }
        public async Task<List<PermissionDto>> GetChildMenusByParentAsync(int parentId)
        {
            var childPermissions = await _context.MstPermissions
                .Where(p => p.ParentMenuId == parentId)
                .OrderBy(p => p.OrderNo)
                .Select(p => new PermissionDto
                {
                    PermissionId = p.PermissionId,
                    MenuName = p.MenuName
                })
                .ToListAsync();

            return childPermissions;
        }
        public async Task<ApiResponseDto<bool>> MapPermissionToRoleAsync(UpdatePermissionRequest request)
        {
            try
            {
                if (request == null || request.RoleId <= 0 || request.PermissionId <= 0)
                {
                    return ApiResponseDto<bool>.ErrorResponse("Invalid data", StatusCodes.Status400BadRequest);
                }

                // Check if already mapped
                var exists = await _context.MstRoleMenuPermissions
                    .AnyAsync(x => x.RoleId == request.RoleId && x.PermissionId == request.PermissionId);

                if (exists)
                {
                    return ApiResponseDto<bool>.ErrorResponse("This permission is already mapped to the selected role", StatusCodes.Status409Conflict);
                }

                // Create new mapping
                var newMapping = new MstRoleMenuPermission
                {
                    RoleId = request.RoleId,
                    PermissionId = request.PermissionId,
                    CanCreate = request.CanCreate,
                    CanView = request.CanView,
                    CanEdit = request.CanEdit,
                    CanDelete = request.CanDelete,
                };

                _context.MstRoleMenuPermissions.Add(newMapping);
                await _context.SaveChangesAsync();

                return ApiResponseDto<bool>.SuccessResponse("Permission mapped to role successfully", StatusCodes.Status200OK, true);
            }
            catch (Exception ex)
            {
                return ApiResponseDto<bool>.ErrorResponse($"Internal server error: {ex.Message}", StatusCodes.Status500InternalServerError);
            }
        }
        public async Task<ApiResponseDto<bool>> CreatePermissionAsync(PermissionDto permission)
        {
            try
            {
                if (permission == null)
                {
                    return ApiResponseDto<bool>.ErrorResponse("Invalid permission data", StatusCodes.Status400BadRequest);
                }

                // Determine the next OrderNo
                int nextOrderNo = 1;
                if (permission.ParentMenuId.HasValue && permission.ParentMenuId.Value > 0)
                {
                    // Find the max OrderNo for the given parent
                    var maxOrderNo = await _context.MstPermissions
                        .Where(p => p.ParentMenuId == permission.ParentMenuId.Value)
                        .Select(p => p.OrderNo)
                        .DefaultIfEmpty(0) // Handle case where no children exist yet
                        .MaxAsync();
                    nextOrderNo = (maxOrderNo ?? 0) + 1;
                }
                else
                {
                    // Find the max OrderNo for root level permissions (ParentMenuId is null or 0)
                     var maxOrderNo = await _context.MstPermissions
                        .Where(p => p.ParentMenuId == null || p.ParentMenuId == 0)
                        .Select(p => p.OrderNo)
                        .DefaultIfEmpty(0) // Handle case where no root permissions exist yet
                        .MaxAsync();
                    nextOrderNo = (maxOrderNo ?? 0) + 1;
                }

                // Create new permission
                var newPermission = new MstPermission
                {
                    MenuName = permission.MenuName,
                    ControllerName = permission.ControllerName,
                    ActionName = permission.ActionName,
                    AreaName = permission.AreaName,
                    RouteParams = permission.RouteParams,
                    Icon = permission.Icon,
                    OrderNo = nextOrderNo, // Use the calculated order number
                    ParentMenuId = permission.ParentMenuId > 0 ? permission.ParentMenuId : null, // Set ParentMenuId to null if 0 or less
                    IsActive = permission.IsActive
                };

                _context.MstPermissions.Add(newPermission);
                await _context.SaveChangesAsync();

                return ApiResponseDto<bool>.SuccessResponse("Permission created successfully", StatusCodes.Status200OK, true);
            }
            catch (Exception ex)
            {
                return ApiResponseDto<bool>.ErrorResponse($"Error creating permission: {ex.Message}", StatusCodes.Status500InternalServerError);
            }
        }
        public async Task<ApiResponseDto<PermissionDto>> GetPermissionByIdAsync(int permissionId)
        {
            try
            {
                var permission = await _context.MstPermissions
                    .Where(p => p.PermissionId == permissionId)
                    .Select(p => new PermissionDto
                    {
                        PermissionId = p.PermissionId,
                        ParentMenuId = p.ParentMenuId,
                        MenuName = p.MenuName,
                        ControllerName = p.ControllerName,
                        ActionName = p.ActionName,
                        AreaName = p.AreaName,
                        RouteParams = p.RouteParams,
                        Icon = p.Icon,
                        OrderNo = p.OrderNo.GetValueOrDefault(),
                        IsActive = p.IsActive.GetValueOrDefault(),
                        // Children are not needed for editing a single permission
                        Children = null
                    })
                    .FirstOrDefaultAsync();

                if (permission == null)
                {
                    return ApiResponseDto<PermissionDto>.ErrorResponse("Permission not found", StatusCodes.Status404NotFound);
                }

                return ApiResponseDto<PermissionDto>.SuccessResponse("Permission fetched successfully", StatusCodes.Status200OK, permission);
            }
            catch (Exception ex)
            {
                return ApiResponseDto<PermissionDto>.ErrorResponse($"Error fetching permission: {ex.Message}", StatusCodes.Status500InternalServerError);
            }
        }
        public async Task<ApiResponseDto<bool>> TogglePermissionActiveStatusAsync(int permissionId, bool isActive)
        {
            try
            {
                var permissionToUpdate = await _context.MstPermissions.FirstOrDefaultAsync(p => p.PermissionId == permissionId);

                if (permissionToUpdate == null)
                {
                    return ApiResponseDto<bool>.ErrorResponse("Permission not found", StatusCodes.Status404NotFound);
                }

                permissionToUpdate.IsActive = isActive;

                _context.MstPermissions.Update(permissionToUpdate);
                await _context.SaveChangesAsync();

                return ApiResponseDto<bool>.SuccessResponse("Permission status updated successfully", StatusCodes.Status200OK, true);
            }
            catch (Exception ex)
            {
                return ApiResponseDto<bool>.ErrorResponse($"Error toggling permission status: {ex.Message}", StatusCodes.Status500InternalServerError);
            }
        }
        public async Task<ApiResponseDto<bool>> UpdatePermissionDetailsAsync(PermissionDto permission)
        {
            try
            {
                if (permission == null || permission.PermissionId <= 0)
                {
                    return ApiResponseDto<bool>.ErrorResponse("Invalid permission data", StatusCodes.Status400BadRequest);
                }

                var existingPermission = await _context.MstPermissions.FirstOrDefaultAsync(p => p.PermissionId == permission.PermissionId);

                if (existingPermission == null)
                {
                    return ApiResponseDto<bool>.ErrorResponse("Permission not found", StatusCodes.Status404NotFound);
                }

                // Update the properties
                existingPermission.MenuName = permission.MenuName;
                existingPermission.ControllerName = permission.ControllerName;
                existingPermission.ActionName = permission.ActionName;
                existingPermission.AreaName = permission.AreaName;
                existingPermission.RouteParams = permission.RouteParams;
                existingPermission.Icon = permission.Icon;
                existingPermission.IsActive = permission.IsActive;
                // Update ParentMenuId - set to null if 0 or less
                existingPermission.ParentMenuId = permission.ParentMenuId > 0 ? permission.ParentMenuId : null;

                _context.MstPermissions.Update(existingPermission);
                await _context.SaveChangesAsync();

                return ApiResponseDto<bool>.SuccessResponse("Permission updated successfully", StatusCodes.Status200OK, true);
            }
            catch (Exception ex)
            {
                return ApiResponseDto<bool>.ErrorResponse($"Internal server error: {ex.Message}", StatusCodes.Status500InternalServerError);
            }
        }
        public async Task<ApiResponseDto<List<PermissionDto>>> GetAllPermissionsAsync(string filterType = "all")
        {
            try
            {
                var query = _context.MstPermissions.AsQueryable();

                if (filterType.ToLower() == "parent")
                {
                    query = query.Where(p => p.ParentMenuId == null || p.ParentMenuId == 0);
                }
                else if (filterType.ToLower() == "child")
                {
                    query = query.Where(p => p.ParentMenuId != null && p.ParentMenuId > 0);
                }
                // If filterType is "all" or anything else, no additional filtering is applied

                var permissions = await query
                    .Select(p => new PermissionDto
                    {
                        PermissionId = p.PermissionId,
                        ParentMenuId = p.ParentMenuId,
                        // Include ParentMenuName by joining with MstPermissions table again for parent
                        ParentMenuName = p.ParentMenuId.HasValue ? _context.MstPermissions
                                                                   .Where(parent => parent.PermissionId == p.ParentMenuId.Value)
                                                                   .Select(parent => parent.MenuName)
                                                                   .FirstOrDefault() : null,
                        MenuName = p.MenuName,
                        ControllerName = p.ControllerName,
                        ActionName = p.ActionName,
                        AreaName = p.AreaName,
                        RouteParams = p.RouteParams,
                        Icon = p.Icon,
                        OrderNo = p.OrderNo.GetValueOrDefault(),
                        IsActive = p.IsActive.GetValueOrDefault(),
                        Children = null // Children are not needed for this list view
                    })
                    .ToListAsync();

                return ApiResponseDto<List<PermissionDto>>.SuccessResponse("Permissions fetched successfully", StatusCodes.Status200OK, permissions);
            }
            catch (Exception ex)
            {
                return ApiResponseDto<List<PermissionDto>>.ErrorResponse($"Error fetching permissions: {ex.Message}", StatusCodes.Status500InternalServerError);
            }
        }
        public async Task<ApiResponseDto<bool>> DeletePermissionMappingAsync(int permissionId, int roleId)
        {
            try
            {
                var mappingToDelete = await _context.MstRoleMenuPermissions
                    .FirstOrDefaultAsync(m => m.PermissionId == permissionId && m.RoleId == roleId);

                if (mappingToDelete == null)
                {
                    return ApiResponseDto<bool>.ErrorResponse("Permission mapping not found for this role and permission", StatusCodes.Status404NotFound);
                }

                _context.MstRoleMenuPermissions.Remove(mappingToDelete);
                await _context.SaveChangesAsync();

                return ApiResponseDto<bool>.SuccessResponse("Permission mapping deleted successfully", StatusCodes.Status200OK, true);
            }
            catch (Exception ex)
            {
                return ApiResponseDto<bool>.ErrorResponse($"Error deleting permission mapping: {ex.Message}", StatusCodes.Status500InternalServerError);
            }
        }
    }
} 