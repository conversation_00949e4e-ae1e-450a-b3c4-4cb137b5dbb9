using System.Collections.Generic;

namespace HSSE_Models_Dto.ModelsDto
{
    public class PermissionDto
    {
        public int PermissionId { get; set; }
        public int? ParentMenuId { get; set; }
        public string? ParentMenuName { get; set; }
        public string MenuName { get; set; }
        public string ControllerName { get; set; }
        public string ActionName { get; set; }
        public string AreaName { get; set; }
        public string RouteParams { get; set; }
        public string Icon { get; set; }
        public int OrderNo { get; set; }
        public bool IsActive { get; set; }
        public bool CanView { get; set; }
        public bool CanCreate { get; set; }
        public bool CanEdit { get; set; }
        public bool CanDelete { get; set; }
        public List<PermissionDto> Children { get; set; }
    }
    public class PermissionOrderUpdateDto
    {
        public int PermissionId { get; set; }
        public int OrderNo { get; set; }
    }
    public class UpdatePermissionRequest
    {
        public int PermissionId { get; set; }
        public int RoleId { get; set; }
        public string MenuName { get; set; }
        public bool CanCreate { get; set; }
        public bool CanView { get; set; }
        public bool CanEdit { get; set; }
        public bool CanDelete { get; set; }
    }

}