﻿using HSSE_Domain.Models;
using HSSE_Models_Dto.ModelsDto;
using HSSE_Models_Dto.ViewModels;
using HSSE_Service.DataManager;
using HSSE_Service.InterfaceService;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using System.Threading.Tasks;

namespace HSSE_WEB.Controllers
{
    public class FacilityController : Controller
    {
        private readonly IFacilityService _facilityService;
        private readonly IUserService _userService;
        private readonly IUserRoleService _userRoleService;
        private readonly IPermissionService _permissionService;
        private IConfiguration _configuration;
        public FacilityController(IUserRoleService userRoleService, IUserService userService, IConfiguration configuration, IFacilityService facilityService, IPermissionService permissionService)
        {
            _facilityService = facilityService;
            _configuration = configuration;
            _userService = userService;
            _userRoleService = userRoleService;
            _permissionService = permissionService;
        }
        public async Task<IActionResult> ManageFacility()
        {
            var viewModel = new AddUserViewModel
            {
                Organisation = await _facilityService.GetAllOrgAsync()
            };

            return View(viewModel);
        }
        public async Task<IActionResult> ViewStaff()
        {
            var viewModel = new AddUserViewModel
            {
                Facilities = await _facilityService.GetAllFacilityAsync(),
                Roles = await _userRoleService.GetAllUserRoleAsync(),
            };

            return View(viewModel);
        }
        [HttpGet]
        public async Task<IActionResult> GetAllFacility()
        {
            var facility = await _facilityService.GetAllFacilityAsync();
            return Json(facility);
        }
        public async Task<IActionResult> GetAllMasterFacility()
        {
            var facility = await _facilityService.GetAllMasterFacilityAsync();
            return Json(facility);
        }
        
        [HttpGet]
        public async Task<IActionResult> GetFacilityById(int id)
        {
            var facility = await _facilityService.GetFacilityByIdAsync(id);
            if (facility == null)
            {
                return Json(new { success = false, message = "Facility not found" });
            }
            return Json(facility);
        }
        [HttpGet]
        public async Task<IActionResult> GetAllUsers(int? roleId, int? facilityId)
        {
            var userDetails = await _userService.GetAllUserRolesConfigAsync(roleId, facilityId);
            return Json(userDetails);
        }
        [HttpGet]
        public async Task<IActionResult> GetFacilities()
        {
            var facilities = await _facilityService.GetAllFacilities();
            return Json(facilities);
        }
        [HttpGet]
        public async Task<IActionResult> GetUserFacilities()
        {
            var userId = int.Parse(HttpContext?.Session?.GetString("UserId"));
            var facilities = await _userService.GetUserFacilitiesWithRoles(userId);
            return Json(facilities);
        }
        [HttpGet]
        public async Task<IActionResult> GetRolesForFacility(int facilityId)
        {
            var userId = int.Parse(HttpContext?.Session?.GetString("UserId"));
            var roles = await _facilityService.GetUserRolesForFacility(facilityId, userId);
            return Json(roles);
        }
        [HttpGet]
        public async Task<IActionResult> GetPermissions(int facilityId, int roleId)
        {
            var result = await _permissionService.GetPermissionsByUserIdAsync(roleId);
            return Json(result.Data);
        }
        [HttpPost]
        public async Task<IActionResult> UpdateFacility([FromBody] MstFacilityDto facility)
        {
            try
            {
                var result = await _facilityService.UpdateFacilityAsync(facility);
                if (result)
                {
                    return Json(new { success = true, message = "Facility updated successfully" });
                }
                return Json(new { success = false, message = "Failed to update facility" });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = ex.Message });
            }
        }

        [HttpPost]
        public async Task<IActionResult> SaveFacility([FromBody] MstFacilityDto facility)
        {
            try
            {
                var result = await _facilityService.SaveFacilityAsync(facility);
                if (result)
                {
                    return Json(new { success = true, message = "Facility saved successfully" });
                }
                return Json(new { success = false, message = "Failed to save facility" });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = ex.Message });
            }
        }
        [HttpPost]
        public async Task<IActionResult> ToggleFacilityStatus(int id)
        {
            try
            {
                var result = await _facilityService.ToggleFacilityStatusAsync(id);
                if (result)
                {
                    return Json(new { success = true, message = "Facility status updated successfully" });
                }
                return Json(new { success = false, message = "Failed to update facility status" });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = ex.Message });
            }
        }
    }
}
