﻿using AutoMapper;
using HSSE_Domain.Models;
using HSSE_Models_Dto.ModelsDto;
using HSSE_Service.Constant;
using HSSE_Service.InterfaceService;
using HSSE_Service.ServiceResponce;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Org.BouncyCastle.Crypto;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HSSE_Service.DataManager
{
    public class UserRoleService : IUserRoleService
    {
        private readonly HsseDbLatestContext _context;
        private IConfiguration _configuration;
        private readonly IMapper _mapper;


        public UserRoleService(HsseDbLatestContext context, IConfiguration configuration, IMapper mapper)
        {
            _context = context;
            _configuration = configuration;
            _mapper = mapper;

        }

        public async Task<List<MstUsersRoleDto>> GetAllUserRoleAsync()
        {
            var facilities = await _context.MstUsersRoles.ToListAsync();
            return _mapper.Map<List<MstUsersRoleDto>>(facilities);
        }

        public async Task<MstUsersRoleDto> GetAllUserRoleByIdAsync(int roleId)
        {
            var facilities = await _context.MstUsersRoles.Where(r => r.RoleId == roleId).FirstOrDefaultAsync();
            return _mapper.Map<MstUsersRoleDto>(facilities);
        }
        public async Task<ApiResponseDto<object>> SaveRoleAsync(MstUsersRoleDto dto)
        {
            try
            {
                var normalizedRoleName = dto.RoleName.Trim().ToLower();

                // Check if another role (excluding current one) has the same name
                bool roleNameExists = await _context.MstUsersRoles
                    .AnyAsync(r => r.RoleName.ToLower() == normalizedRoleName && r.RoleId != dto.RoleId);

                if (roleNameExists)
                {
                    return ApiResponseDto<object>.ErrorResponse(
                        "Role name already exists.",
                        StatusCodes.Status400BadRequest
                    );
                }

                // Check if this is an update or create
                var role = await _context.MstUsersRoles.FirstOrDefaultAsync(r => r.RoleId == dto.RoleId);

                if (role == null)
                {
                    // Create new role
                    role = new MstUsersRole
                    {
                        RoleName = dto.RoleName.Trim(),
                        CreatedAt = DateTime.UtcNow,
                        CreatedBy = dto.CreatedBy ?? 1
                    };
                    _context.MstUsersRoles.Add(role);
                }
                else
                {
                    // Update role
                    role.RoleName = dto.RoleName.Trim();
                    role.IsActive = dto.IsActive ;
                    role.ModifiedAt = DateTime.UtcNow;
                    role.ModifiedBy = dto.ModifiedBy ?? dto.CreatedBy ?? 1;
                }

                await _context.SaveChangesAsync();

                return ApiResponseDto<object>.SuccessResponse(ApiMessages.RoleSaveSuccess, StatusCodes.Status200OK);
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse(ApiMessages.RoleSaveFailed, StatusCodes.Status500InternalServerError, new List<Error>
        {
            new Error { Code = 500, Message = ex.Message }
        });
            }
        }

        public async Task<ApiResponseDto<object>> ToggleRoleActivationAsync(int roleId)
        {
            try
            {
                var role = await _context.MstUsersRoles.FirstOrDefaultAsync(r => r.RoleId == roleId);
                if (role == null)
                {
                    return ApiResponseDto<object>.ErrorResponse(ApiMessages.RoleNotFound, StatusCodes.Status404NotFound, new List<Error>
            {
                new Error { Code = 404, Message = ApiMessages.RoleNotFound }
            });
                }

                // Toggle the IsActive status
                role.IsActive = !role.IsActive;
                await _context.SaveChangesAsync();

                var message = role.IsActive ? ApiMessages.RoleActivationSuccess : ApiMessages.RoleDeactivationSuccess;
                return ApiResponseDto<object>.SuccessResponse(message, StatusCodes.Status200OK, role.IsActive);
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse(ApiMessages.InternalServerError, StatusCodes.Status500InternalServerError, new List<Error>
        {
            new Error { Code = 500, Message = ex.Message }
        });
            }
        }

        public async Task<ApiResponseDto<object>> SaveUserRoleMappingAsync(MstUserRolesConfigDto dto)
        {
            try
            {
                // Validate input
                if (dto.UserId == null || dto.RoleId == null)
                {
                    return ApiResponseDto<object>.ErrorResponse(ApiMessages.UserRoleMappingInvalidInput,
                        StatusCodes.Status400BadRequest);
                }


                // Check for existing mapping (same UserId, RoleId, and FacilityId)
                var existingMapping = await _context.MstUserRolesConfigs
                    .FirstOrDefaultAsync(x => x.UserRoleConfigId != dto.UserRoleConfigId &&
                        x.UserId == dto.UserId &&
                        x.RoleId == dto.RoleId &&
                        x.FacilityId == dto.FacilityId);

                if (existingMapping != null)
                {
                    return ApiResponseDto<object>.ErrorResponse(ApiMessages.UserRoleMappingDuplicate,
                        StatusCodes.Status409Conflict);
                }
                var entity = await _context.MstUserRolesConfigs.FirstOrDefaultAsync(e => e.UserRoleConfigId == dto.UserRoleConfigId);
                if (dto.UserRoleConfigId != 0 && entity != null)
                {
                    // Update
                    _mapper.Map(dto, entity);
                }
                else
                {
                    // Create new mapping
                    var newMapping = new MstUserRolesConfig
                    {
                        UserId = dto.UserId,
                        RoleId = dto.RoleId,
                        FacilityId = dto.FacilityId,
                        CreatedAt = DateTime.UtcNow,
                    };

                    _context.MstUserRolesConfigs.AddAsync(newMapping);
                }
                await _context.SaveChangesAsync();

                return ApiResponseDto<object>.SuccessResponse(ApiMessages.UserRoleMappingSaveSuccess,
                    StatusCodes.Status200OK);
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse(ApiMessages.UserRoleMappingSaveFailed,
                    StatusCodes.Status500InternalServerError,
                    new List<Error>
                    {
                new Error { Code = 500, Message = ex.Message }
                    });
            }
        }

        public async Task<List<MstUserRolesConfigDto>> GetAllUserRoleMappingAsync()
        {
            var details = await _context.MstUserRolesConfigs.Include(r=>r.Role).Include(u => u.User ).Include(f=>f.Facility).ToListAsync();

            var userRolesConfigs = details.Select(config => new MstUserRolesConfigDto
            {
                UserRoleConfigId = config.UserRoleConfigId,  // Map UserRoleConfigId
                Username = config.User.Username,             // Get the Username from the User entity
                RoleName = config.Role.RoleName,              // Get the RoleName from the Role entity
                FacilityName = config?.Facility?.FacilityName
            }).ToList();

            return userRolesConfigs;
        }
        public async Task<MstUserRolesConfigDto> GetAllUserRoleMappingByIdAsync(int id)
        {
            var details = await _context.MstUserRolesConfigs.Include(r => r.Role).Include(u => u.User).Include(f => f.Facility).FirstOrDefaultAsync(x=>x.UserRoleConfigId == id);

            var userRolesConfigs = new MstUserRolesConfigDto
            {
                UserRoleConfigId = details.UserRoleConfigId,  // Map UserRoleConfigId
                Username = details.User.Username,             // Get the Username from the User entity
                RoleName = details.Role.RoleName,              // Get the RoleName from the Role entity
                FacilityName = details?.Facility?.FacilityName,
                FacilityId = details?.FacilityId,
                RoleId = details.RoleId,
                UserId = details.UserId,
            };

            return userRolesConfigs;
        }

        public async Task<ApiResponseDto<object>> DeleteUserRoleMappingAsync(int userRoleConfigId)
        {
            try
            {
                var mapping = await _context.MstUserRolesConfigs
                    .FirstOrDefaultAsync(x => x.UserRoleConfigId == userRoleConfigId);

                if (mapping == null)
                {
                    return ApiResponseDto<object>.ErrorResponse(ApiMessages.UserRoleMappingNotFound, StatusCodes.Status404NotFound,
                        new List<Error> { new Error { Code = 404, Message = ApiMessages.UserRoleMappingNotFound } });
                }

                _context.MstUserRolesConfigs.Remove(mapping);
                await _context.SaveChangesAsync();

                return ApiResponseDto<object>.SuccessResponse(ApiMessages.UserRoleMappingDeleted, StatusCodes.Status200OK);
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse(ApiMessages.InternalServerError, StatusCodes.Status500InternalServerError,
                    new List<Error> { new Error { Code = 500, Message = ex.Message } });
            }
        }

    }
}

