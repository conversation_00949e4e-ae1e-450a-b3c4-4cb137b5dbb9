@{
    ViewData["Title"] = "Create Feedback";
}

<div class="col-12 grid-margin">
<div class="card mt-2">
    <div class="card-body">


            <h4 class="card-title">Create Feedback</h4>
<form id="createFeedbackForm">
    <div class="form-horizontal">
        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="Name" class="control-label col-md-2">Name</label>
                    <div class="col-md-10">
                        <input name="Name" id="Name" class="form-control" required />
                    </div>
                </div>
                <div class="form-group">
                    <label for="Title" class="control-label col-md-2">Title</label>
                    <div class="col-md-10">
                        <input name="Title" id="Title" class="form-control" required />
                    </div>
                </div>
            
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="facilityId" class="control-label col-md-2">Facility</label>
                    <div class="col-md-10">
                        <select id="facilityId" class="form-control" required></select>
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label col-md-2">Document</label>
                    <div class="col-md-10">

                                    <div class="custom-file-upload">
                                        <input type="file" class="d-none" id="ProfileImageUpload" name="ProfileImage" accept="image/*" data-permission="create" />
                                        <div class="input-group">
                                            <input type="text" class="form-control file-upload-info" id="ProfileImageFileName" readonly placeholder="Choose Image" />
                                            <div class="input-group-append">
                                                <button class="btn btn-primary btn-sm" type="button" id="uploadProfileImageBtn">Upload</button>
                                                <button class="btn btn-danger btn-sm ml-1 d-none" type="button" id="removeProfileImageBtn">Remove</button>
                                            </div>
                                        </div>
                                        <div class="mt-2 d-none" id="ProfileImagePreviewContainer">
                                            <img id="ProfileImagePreview" src="" class="img-thumbnail" style="max-width: 250px;" />
                                        </div>
                                    </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="form-group">
            <label for="Description" class="control-label col-md-2">Description</label>
            <div class="col-md-12">
                <textarea name="Description" id="tinyMceExample" class="form-control"></textarea>
            </div>
        </div>
        <div class="form-group">
            <div class="col-md-offset-2 col-md-10 mt-2">
                <button type="submit" class="btn btn-primary">Preview</button>
            </div>
        </div>
    </div>
</form>

    </div>
</div>
</div>
<div class="col-12 grid-margin">
<div class="card mt-4">
    <div class="card-body">
        <h4 class="card-title">Feedback List</h4>
        <div class="table-responsive">
            <table id="feedBack-listing" class="table">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>Name</th>
                        <th>Title</th>
                        <th>Description</th>
                         <th>Date</th>
                                           <th>Status</th>

                        <th>Action</th>
                    </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
        </div>
    </div>
</div>
</div>
<!-- Preview Modal -->
<div class="modal fade" id="feedbackPreviewModal" tabindex="-1" role="dialog" aria-labelledby="previewModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-xl" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Preview Feedback</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <table class="table table-bordered">
          <tbody>
            <tr><th>Name</th><td id="previewName"></td></tr>
            <tr><th>Title</th><td id="previewTitle"></td></tr>
            <tr><th>Facility</th><td id="previewFacility"></td></tr>
                        <tr>
                            <th>Description</th>
                            <td id="previewDescription" style="white-space: pre-wrap; word-wrap: break-word; max-width: 100%;"></td>
                        </tr>
            <tr><th>File</th><td id="previewFile"></td></tr>
          </tbody>
        </table>
      </div>
      <div class="modal-footer">
        <button type="submit" id="confirmSubmitBtn" type="button" class="btn btn-primary">Submit</button>
        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>

<!-- Edit Modal -->
<div class="modal fade" id="editFeedbackModal" tabindex="-1" role="dialog" aria-labelledby="editModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-xl" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Edit Feedback</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <form id="editFeedbackForm">
          <input type="hidden" id="editFeedbackId" />
          <div class="form-group">
            <label for="editName">Name</label>
            <input type="text" class="form-control" id="editName" required>
          </div>
          <div class="form-group">
            <label for="editTitle">Title</label>
            <input type="text" class="form-control" id="editTitle" required>
          </div>
          <div class="form-group">
            <label for="editFacilityId">Facility</label>
            <select class="form-control" id="editFacilityId" required></select>
          </div>
          <div class="form-group">
            <label for="editDescription">Description</label>
            <textarea id="editTinyMceExample" class="form-control"></textarea>
          </div>
          <div class="form-group">
            <label>Document</label>
                        <div class="custom-file-upload">
                            <input type="hidden" id="ExistingThumbnailPath" />

                            <input type="file" class="d-none" id="editProfileImageInput" name="ProfileImage" accept="image/*" data-permission="create" />
                            <div class="input-group">
                                <input type="text" class="form-control file-upload-info" id="editProfileImageFileName" readonly placeholder="Choose Image" />
                                <div class="input-group-append">
                                    <button class="btn btn-primary btn-sm" type="button" id="editProfileImageBtn">Upload</button>
                                    <button class="btn btn-danger btn-sm ml-1 d-none" type="button" id="removeEditProfileImageBtn">Remove</button>
                                </div>
                            </div>
                            <div class="mt-2 d-none" id="editProfileImagePreviewContainer">
                                <img id="editProfileImagePreview" src="" class="img-thumbnail" style="max-width: 250px;" />
                            </div>
                        </div>
      
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
        <button type="button" class="btn btn-primary" id="saveEditBtn">Save Changes</button>
      </div>
    </div>
  </div>
</div>


<div class="modal fade" id="PreviewModal" tabindex="-1" role="dialog" aria-labelledby="previewModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-centered" role="document">
        <div class="modal-content rounded shadow-sm">
            <div class="modal-header">
                <h5 class="modal-title font-weight-semibold">Feedback Details</h5>
                <button type="button" class="close text-white" data-dismiss="modal">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>

            <div class="modal-body px-4 py-3">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <div class="border p-2 rounded">
                            <label class="text-muted mb-1">Name</label>
                            <div class="font-weight-bold text-dark" id="previewNameDetails"></div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="border p-2 rounded">
                            <label class="text-muted mb-1">Facility</label>
                            <div class="font-weight-bold text-dark" id="previewFacilityDetails"></div>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <div class="border p-2 rounded">
                        <label class="text-muted mb-1">Title</label>
                        <div class="font-weight-bold text-dark" id="previewTitleDetails"></div>
                    </div>
                </div>

                <div class="mb-3">
                    <div class="border p-2 rounded">
                        <label class="text-muted mb-1">Description</label>
                        <div id="previewDescriptionDetails" class="text-dark" style="white-space: pre-wrap;"></div>
                    </div>
                </div>

                <div class="mb-3">
                    <div class="border p-2 rounded">
                        <label class="text-muted mb-1">Attached File</label>
                        <div id="previewFileDetails" class="mt-1">
                            <!-- File link will be injected -->
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <div class="border p-2 rounded">
                        <label class="text-muted mb-1">Status</label>
                        <div>
                            <span id="previewStatusDetails" class="badge badge-pill badge-outline-primary mt-1"></span>
                        </div>
                    </div>
                </div>

                <div class="mb-0">
                    <div class="border p-2 rounded">
                        <label class="text-muted mb-1">Response</label>
                        <div id="previewResponseDetails" class="text-dark" style="white-space: pre-wrap;"></div>
                    </div>
                </div>
            </div>

            <div class="modal-footer border-top-0">
                <button type="button" class="btn btn-secondary px-4" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>


<script>
                    const createFeedbackUrl = '@Url.Action("CreateFeedback", "Feedback")';
            const getAllFacilitiesUrl = '@Url.Action("GetAllFacilities", "Feedback")'; // Assuming Facility controller is accessible, adjust if needed
    const getFeedback = '@Url.Action("GetFeedback", "Feedback")'; // Assuming Facility controller is accessible, adjust if needed

    const getFeedbackById =  '@Url.Action("GetFeedbackById", "Feedback")?id=';

</script>
@section Scripts {
   <script src="~/js/feedback/createFeedback.js"></script>

} 