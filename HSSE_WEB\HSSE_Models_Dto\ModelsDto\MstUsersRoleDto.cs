﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HSSE_Models_Dto.ModelsDto
{
    public class MstUsersRoleDto
    {
        public int? RoleId { get; set; }

        public string RoleName { get; set; } = null!;

        public bool? SeededRole { get; set; }

        public DateTime? CreatedAt { get; set; }

        public int? CreatedBy { get; set; }
        public bool IsActive { get; set; }
        public int? ModifiedBy { get; set; } 
    }
}
