namespace HSSE_Service.Constant
{
    public static class ApiEndpoints
    {
        public const string InsertOrUpdateGroup = "/api/Announcement/InsertOrUpdateGroup";
        public const string GetGroups = "/api/Announcement/GetGroups";
        public const string GetGroupById = "/api/Announcement/GetGroupById?id=";
        public const string DeleteGroup = "/api/Announcement/DeleteGroup?id=";
        public const string InsertGroupMember = "/api/Announcement/InsertGroupMembers";
        public const string UpdateGroupMember = "/api/Announcement/UpdateGroupMembers";

        public const string GetGroupMembers = "/api/Announcement/GetGroupMembers";
        public const string GetGroupMembersByGroupId = "/api/Announcement/GetGroupMembersByGroupId?groupId=";
        public const string DeleteAllGroupMembersByGroupId = "/api/Announcement/DeleteAllGroupMembersByGroupId?id=";

        public const string InsertOrUpdateAnnouncement = "/api/Announcement/InsertOrUpdateAnnouncement";
        public const string GetAnnouncements = "/api/Announcement/GetAnnouncements";
        public const string DeleteAnnouncements = "/api/Announcement/DeleteAnnouncement?id=";
        public const string GetAnnouncementsById = "/api/Announcement/GetAnnouncementById?id=";
        public const string GetAnnouncementsByUserId = "/api/Announcement/GetAnnouncementsByUserId";
        public const string GetAnnouncementDetailsById = "/api/Announcement/GetAnnouncementDetailsById?id=";
        public const string UpdateAnnouncement = "/api/Announcement/UpdateAnnouncement";
        public const string GetUsersAnnouncementsByUserId = "/api/Announcement/GetUsersAnnouncementsByUserId";

        public const string InsertOrUpdateEvent = "/api/Event/InsertOrUpdateEvent";
        public const string GetEvents = "/api/Event/GetEvents";
        public const string GetEventById = "/api/Event/GetEventById?id=";
        public const string GetEventsByFacilityId = "/api/Event/GetEventsByFacilityId?facilityId=";
        public const string ToggleAnnouncementStatus = "/api/Announcement/ToggleAnnouncementStatus";
        public const string GetEventRsvpDetails = "/api/Event/GetEventRsvpDetails?eventId=";

        public const string InsertOrUpdateAnnouncementCategory = "/api/Announcement/InsertOrUpdateAnnouncementCategory";
        public const string GetAnnouncementCategoriesByUserId = "/api/Announcement/GetAnnouncementCategoriesByUserId";
        public const string GetAnnouncementCategories = "/api/Announcement/GetAnnouncementCategories";
        public const string GetAnnouncementCategoryById = "/api/Announcement/GetAnnouncementCategoryById?id=";
        public const string ToggleAnnouncementCategoryStatus = "/api/Announcement/ToggleAnnouncementCategoryStatus";

        public const string CreateOrUpdateDocument = "/api/DocumentLibrary/CreateOrUpdateDocument";
        public const string GetDocuments = "/api/DocumentLibrary/GetDocuments";
        public const string GetDocumentsByUserId = "/api/DocumentLibrary/GetDocumentsByUserId?userId=";
        public const string GetDocumentById = "/api/DocumentLibrary/GetDocumentById?documentId=";
        public const string CreateFeedback = "/api/MstFeedback/CreateOrUpdateFeedback";
        public const string UpdateFeedbackResponse = "/api/MstFeedback/UpdateFeedbackWithResponse";

    }
} 