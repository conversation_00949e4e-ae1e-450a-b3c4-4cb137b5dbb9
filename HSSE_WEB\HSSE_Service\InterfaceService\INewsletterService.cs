using HSSE_Models_Dto.ModelsDto;
using HSSE_Service.ServiceResponce;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace HSSE_Service.InterfaceService
{
    public interface INewsletterService
    {
        Task<ApiResponseDto<object>> InsertOrUpdateNewsletterAsync(MstNewsletterDto dto);
        Task<ApiResponseDto<List<MstNewsletterDto>>> GetNewslettersAsync(int? userId, List<int>? facilityIds = null);
        Task<ApiResponseDto<object>> ToggleNewsletterActivationAsync(int newsletterId);
        Task<ApiResponseDto<object>> DeleteNewsletterAsync(int newsletterId);
        Task<ApiResponseDto<MstNewsletterDto>> GetNewsletterByIdAsync(int newsletterId);
        Task<ApiResponseDto<List<MstNewsletterDto>>> GetNewslettersByFacilityIdsAsync(List<int> facilityIds, int userId);
    }
} 