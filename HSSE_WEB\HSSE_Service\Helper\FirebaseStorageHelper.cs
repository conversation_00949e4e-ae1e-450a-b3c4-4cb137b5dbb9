﻿using Google.Apis.Auth.OAuth2;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Firebase.Storage;
using FirebaseAdmin;
using Microsoft.Extensions.Configuration;
using Azure.Storage.Blobs.Models;
using Azure.Storage.Blobs;


namespace HSSE_Service.Helper
{
    public  class FirebaseStorageHelper
    {
        private readonly string _connectionString;
        private readonly string _containerName;
        private readonly IConfiguration _configuration;


        public FirebaseStorageHelper(IConfiguration configuration)
        {
            _configuration = configuration;
            _connectionString = configuration["AzureBlobStorage:ConnectionString"];
            _containerName = configuration["AzureBlobStorage:ContainerName"];
        }
        public async Task<string> UploadBase64FileAsync(string base64String, string fileName, string folderName)
        {
            if (string.IsNullOrWhiteSpace(base64String))
                throw new ArgumentNullException(nameof(base64String));

            var base64Parts = base64String.Split(",");
            string cleanBase64 = base64Parts.Length > 1 ? base64Parts[1] : base64Parts[0];

            byte[] fileBytes = Convert.FromBase64String(cleanBase64);
            using var stream = new MemoryStream(fileBytes);

            string uniqueFileName = $"{Guid.NewGuid()}_{fileName}";
            string blobPath = string.IsNullOrEmpty(folderName) ? uniqueFileName : $"{folderName}/{uniqueFileName}";

            var blobServiceClient = new BlobServiceClient(_connectionString);
            var containerClient = blobServiceClient.GetBlobContainerClient(_containerName);
            await containerClient.CreateIfNotExistsAsync(PublicAccessType.Blob);

            var blobClient = containerClient.GetBlobClient(blobPath);
            await blobClient.UploadAsync(stream, overwrite: true);

            return blobClient.Uri.ToString(); // This is the public URL to access the blob
        }
    }
}
